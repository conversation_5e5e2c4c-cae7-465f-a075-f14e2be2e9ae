/**
 * AG3NT Framework - Agent Marketplace & Plugin System
 *
 * Extensible marketplace system that allows third-party developers to create,
 * distribute, and monetize custom agents and plugins.
 *
 * Features:
 * - Agent plugin architecture
 * - Marketplace for agent distribution
 * - Sandboxed execution environment
 * - Version management and updates
 * - Security scanning and validation
 * - Revenue sharing and monetization
 */
import { EventEmitter } from "events";
export interface MarketplaceConfig {
    enabled: boolean;
    sandboxEnabled: boolean;
    securityScanningEnabled: boolean;
    autoUpdatesEnabled: boolean;
    revenueSharing: boolean;
    marketplaceUrl: string;
    apiKey: string;
    trustedPublishers: string[];
}
export interface AgentPlugin {
    pluginId: string;
    name: string;
    version: string;
    description: string;
    author: AuthorInfo;
    category: PluginCategory;
    capabilities: PluginCapability[];
    dependencies: PluginDependency[];
    permissions: PluginPermission[];
    pricing: PricingInfo;
    metadata: PluginMetadata;
    manifest: PluginManifest;
}
export interface AuthorInfo {
    id: string;
    name: string;
    email: string;
    website?: string;
    verified: boolean;
    reputation: number;
    publishedPlugins: number;
    totalDownloads: number;
}
export interface PluginCategory {
    primary: string;
    secondary: string[];
    tags: string[];
    targetAudience: 'developer' | 'business' | 'enterprise' | 'all';
}
export interface PluginCapability {
    name: string;
    type: 'core' | 'extended' | 'experimental';
    description: string;
    apiVersion: string;
    compatibility: string[];
    performance: CapabilityPerformance;
}
export interface CapabilityPerformance {
    averageExecutionTime: number;
    memoryUsage: number;
    cpuUsage: number;
    reliability: number;
    scalability: number;
}
export interface PluginDependency {
    name: string;
    version: string;
    type: 'required' | 'optional' | 'peer';
    source: 'npm' | 'marketplace' | 'system';
    verified: boolean;
}
export interface PluginPermission {
    type: 'filesystem' | 'network' | 'system' | 'data' | 'ui' | 'agent_communication';
    level: 'read' | 'write' | 'execute' | 'admin';
    scope: string[];
    justification: string;
    required: boolean;
}
export interface PricingInfo {
    model: 'free' | 'one_time' | 'subscription' | 'usage_based' | 'freemium';
    price: number;
    currency: string;
    trialPeriod?: number;
    usageLimits?: UsageLimits;
    revenueShare: number;
}
export interface UsageLimits {
    executions: number;
    dataProcessed: number;
    apiCalls: number;
    period: 'hour' | 'day' | 'month' | 'year';
}
export interface PluginMetadata {
    created: number;
    updated: number;
    downloads: number;
    rating: number;
    reviews: number;
    size: number;
    checksum: string;
    securityScan: SecurityScanResult;
    compatibility: CompatibilityInfo;
}
export interface SecurityScanResult {
    scanned: boolean;
    scanDate: number;
    status: 'safe' | 'warning' | 'dangerous' | 'unknown';
    vulnerabilities: SecurityVulnerability[];
    score: number;
    recommendations: string[];
}
export interface SecurityVulnerability {
    id: string;
    type: 'code_injection' | 'data_leak' | 'privilege_escalation' | 'malware' | 'suspicious_behavior';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    location: string;
    recommendation: string;
}
export interface CompatibilityInfo {
    frameworkVersion: string[];
    nodeVersion: string[];
    operatingSystem: string[];
    architecture: string[];
    tested: boolean;
    testResults: TestResult[];
}
export interface TestResult {
    environment: string;
    version: string;
    status: 'passed' | 'failed' | 'warning';
    details: string;
    timestamp: number;
}
export interface PluginManifest {
    name: string;
    version: string;
    main: string;
    exports: PluginExport[];
    hooks: PluginHook[];
    configuration: PluginConfiguration[];
    resources: PluginResource[];
    sandbox: SandboxConfiguration;
}
export interface PluginExport {
    name: string;
    type: 'agent' | 'function' | 'component' | 'service';
    entry: string;
    interface: string;
    documentation: string;
}
export interface PluginHook {
    name: string;
    type: 'before' | 'after' | 'around' | 'event';
    target: string;
    priority: number;
    async: boolean;
}
export interface PluginConfiguration {
    key: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    default: any;
    required: boolean;
    description: string;
    validation: string;
}
export interface PluginResource {
    type: 'file' | 'url' | 'data' | 'service';
    path: string;
    size: number;
    checksum: string;
    permissions: string[];
}
export interface SandboxConfiguration {
    enabled: boolean;
    restrictions: SandboxRestriction[];
    allowedModules: string[];
    resourceLimits: ResourceLimits;
    timeouts: SandboxTimeouts;
}
export interface SandboxRestriction {
    type: 'filesystem' | 'network' | 'process' | 'memory' | 'cpu';
    policy: 'deny' | 'allow' | 'restrict';
    parameters: any;
}
export interface ResourceLimits {
    maxMemory: number;
    maxCpu: number;
    maxFileSize: number;
    maxNetworkRequests: number;
    maxExecutionTime: number;
}
export interface SandboxTimeouts {
    initialization: number;
    execution: number;
    cleanup: number;
    total: number;
}
export interface InstalledPlugin {
    plugin: AgentPlugin;
    installDate: number;
    status: 'active' | 'inactive' | 'error' | 'updating';
    configuration: Record<string, any>;
    usage: PluginUsage;
    performance: PluginPerformance;
    sandbox?: SandboxInstance;
}
export interface PluginUsage {
    executions: number;
    totalTime: number;
    dataProcessed: number;
    errors: number;
    lastUsed: number;
    averageRating: number;
}
export interface PluginPerformance {
    averageExecutionTime: number;
    successRate: number;
    memoryUsage: number;
    cpuUsage: number;
    reliability: number;
}
export interface SandboxInstance {
    id: string;
    status: 'running' | 'stopped' | 'error';
    pid?: number;
    memoryUsage: number;
    cpuUsage: number;
    startTime: number;
    restrictions: SandboxRestriction[];
}
export interface MarketplaceQuery {
    query?: string;
    category?: string;
    author?: string;
    priceRange?: {
        min: number;
        max: number;
    };
    rating?: number;
    compatibility?: string;
    sortBy: 'relevance' | 'rating' | 'downloads' | 'updated' | 'price';
    sortOrder: 'asc' | 'desc';
    limit: number;
    offset: number;
}
export interface MarketplaceResult {
    plugins: AgentPlugin[];
    total: number;
    facets: MarketplaceFacets;
    suggestions: string[];
}
export interface MarketplaceFacets {
    categories: FacetCount[];
    authors: FacetCount[];
    priceRanges: FacetCount[];
    ratings: FacetCount[];
}
export interface FacetCount {
    value: string;
    count: number;
}
export interface PluginInstallation {
    pluginId: string;
    version: string;
    configuration?: Record<string, any>;
    autoStart: boolean;
    sandboxed: boolean;
}
export interface PluginUpdate {
    pluginId: string;
    fromVersion: string;
    toVersion: string;
    changes: PluginChange[];
    breaking: boolean;
    automatic: boolean;
}
export interface PluginChange {
    type: 'feature' | 'bugfix' | 'security' | 'performance' | 'breaking';
    description: string;
    impact: 'low' | 'medium' | 'high';
}
/**
 * Agent Marketplace & Plugin System
 */
export declare class AgentMarketplace extends EventEmitter {
    private config;
    private installedPlugins;
    private pluginRegistry;
    private sandboxInstances;
    private pluginCache;
    constructor(config?: Partial<MarketplaceConfig>);
    /**
     * Initialize marketplace
     */
    private initialize;
    /**
     * Search marketplace for plugins
     */
    searchPlugins(query: MarketplaceQuery): Promise<MarketplaceResult>;
    /**
     * Get plugin details
     */
    getPluginDetails(pluginId: string): Promise<AgentPlugin>;
    /**
     * Install plugin
     */
    installPlugin(installation: PluginInstallation): Promise<InstalledPlugin>;
    /**
     * Activate plugin
     */
    activatePlugin(pluginId: string): Promise<void>;
    /**
     * Deactivate plugin
     */
    deactivatePlugin(pluginId: string): Promise<void>;
    /**
     * Uninstall plugin
     */
    uninstallPlugin(pluginId: string): Promise<void>;
    /**
     * Update plugin
     */
    updatePlugin(pluginId: string, targetVersion?: string): Promise<PluginUpdate>;
    /**
     * Get installed plugins
     */
    getInstalledPlugins(): InstalledPlugin[];
    /**
     * Get plugin usage statistics
     */
    getPluginUsage(pluginId: string): PluginUsage | null;
    /**
     * Record plugin execution
     */
    recordPluginExecution(pluginId: string, executionTime: number, success: boolean, dataProcessed?: number): void;
    /**
     * Private helper methods
     */
    private loadInstalledPlugins;
    private startUpdateChecker;
    private checkForUpdates;
    private mockMarketplaceSearch;
    private mockGetPluginDetails;
    private performSecurityScan;
    private createSandbox;
    private stopSandbox;
    private destroySandbox;
    private loadPlugin;
    private registerPluginWithFramework;
    private unregisterPluginFromFramework;
    private getUpdateInfo;
    /**
     * Shutdown marketplace
     */
    shutdown(): Promise<void>;
}
export default AgentMarketplace;
//# sourceMappingURL=agent-marketplace.d.ts.map