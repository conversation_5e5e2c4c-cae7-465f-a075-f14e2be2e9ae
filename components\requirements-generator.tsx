'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, CheckCircle, AlertCircle, FileText, RefreshCw } from 'lucide-react'
import { useRequirementsAgent } from '@/hooks/use-requirements-agent'
import { RequirementsUtils } from '@/lib/agents'

export function RequirementsGenerator() {
  const [userInput, setUserInput] = useState('')
  const {
    isLoading,
    currentRequirements,
    validationResult,
    error,
    iterations,
    generateRequirements,
    validateRequirements,
    refineRequirements,
    performIterativeAnalysis,
    generateMarkdownDocument,
    clearError,
    reset,
    completenessScore,
    isComplete,
    requirementsCount
  } = useRequirementsAgent({
    provider: 'openai',
    model: 'gpt-4o-mini',
    autoValidate: true,
    targetCompletenessScore: 85
  })

  const handleGenerate = async () => {
    if (!userInput.trim()) return
    
    try {
      await generateRequirements(userInput, {
        includeNonFunctional: true,
        detailLevel: 'detailed',
        domain: 'software development'
      })
    } catch (err) {
      console.error('Failed to generate requirements:', err)
    }
  }

  const handleIterativeAnalysis = async () => {
    if (!userInput.trim()) return
    
    try {
      await performIterativeAnalysis(userInput)
    } catch (err) {
      console.error('Failed to perform iterative analysis:', err)
    }
  }

  const handleRefine = async () => {
    try {
      await refineRequirements('Focus on improving clarity and completeness')
    } catch (err) {
      console.error('Failed to refine requirements:', err)
    }
  }

  const handleExportMarkdown = async () => {
    try {
      const markdown = await generateMarkdownDocument()
      const blob = new Blob([markdown], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${currentRequirements?.projectName || 'requirements'}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Failed to export markdown:', err)
    }
  }

  const coverageMetrics = currentRequirements ? 
    RequirementsUtils.calculateCoverageMetrics(currentRequirements.requirements) : null

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Requirements Generator
          </CardTitle>
          <CardDescription>
            Generate comprehensive requirements from your project description using AI
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="project-input" className="text-sm font-medium">
              Project Description
            </label>
            <Textarea
              id="project-input"
              placeholder="Describe your project idea, features, and goals..."
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleGenerate}
              disabled={!userInput.trim() || isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Requirements'
              )}
            </Button>
            
            <Button 
              onClick={handleIterativeAnalysis}
              disabled={!userInput.trim() || isLoading}
              variant="outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Iterative Analysis
            </Button>
            
            <Button 
              onClick={reset}
              disabled={isLoading}
              variant="outline"
              size="icon"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <AlertCircle className="h-4 w-4 text-destructive" />
              <span className="text-sm text-destructive">{error}</span>
              <Button 
                onClick={clearError}
                variant="ghost"
                size="sm"
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {currentRequirements && (
        <div className="space-y-6">
          {/* Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Requirements Overview</span>
                <div className="flex gap-2">
                  <Button 
                    onClick={handleRefine}
                    disabled={isLoading}
                    variant="outline"
                    size="sm"
                  >
                    Refine
                  </Button>
                  <Button 
                    onClick={handleExportMarkdown}
                    variant="outline"
                    size="sm"
                  >
                    Export MD
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{requirementsCount}</div>
                  <div className="text-sm text-muted-foreground">Total Requirements</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{completenessScore}%</div>
                  <div className="text-sm text-muted-foreground">Completeness</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{iterations.length}</div>
                  <div className="text-sm text-muted-foreground">Iterations</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center">
                    {isComplete ? (
                      <CheckCircle className="h-6 w-6 text-green-500" />
                    ) : (
                      <AlertCircle className="h-6 w-6 text-yellow-500" />
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">Status</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Completeness Score</span>
                  <span>{completenessScore}%</span>
                </div>
                <Progress value={completenessScore} className="h-2" />
              </div>

              {coverageMetrics && (
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">
                    Functional: {coverageMetrics.functionalCount}
                  </Badge>
                  <Badge variant="secondary">
                    Non-Functional: {coverageMetrics.nonFunctionalCount}
                  </Badge>
                  <Badge variant="secondary">
                    Constraints: {coverageMetrics.constraintCount}
                  </Badge>
                  <Badge variant="secondary">
                    High Priority: {coverageMetrics.highPriorityCount}
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Requirements Details */}
          <Card>
            <CardHeader>
              <CardTitle>{currentRequirements.projectName}</CardTitle>
              <CardDescription>{currentRequirements.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="requirements" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="requirements">Requirements</TabsTrigger>
                  <TabsTrigger value="validation">Validation</TabsTrigger>
                  <TabsTrigger value="iterations">Iterations</TabsTrigger>
                  <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                </TabsList>

                <TabsContent value="requirements" className="space-y-4">
                  <ScrollArea className="h-96">
                    <div className="space-y-4">
                      {currentRequirements.requirements.map((req, index) => (
                        <Card key={req.id}>
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-base">{req.id}</CardTitle>
                              <div className="flex gap-1">
                                <Badge variant={req.priority === 'high' ? 'destructive' : req.priority === 'medium' ? 'default' : 'secondary'}>
                                  {req.priority}
                                </Badge>
                                <Badge variant="outline">{req.type}</Badge>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-2">
                            <div className="text-sm">
                              <strong>User Story:</strong> As a {req.userStory.role}, I want {req.userStory.feature}, so that {req.userStory.benefit}
                            </div>
                            <div className="space-y-1">
                              <strong className="text-sm">Acceptance Criteria:</strong>
                              <ul className="text-sm space-y-1 ml-4">
                                {req.acceptanceCriteria.map((criteria) => (
                                  <li key={criteria.id} className="list-disc">
                                    {criteria.format.replace('_', ' ')}: {criteria.condition} → {criteria.response}
                                  </li>
                                ))}
                              </ul>
                            </div>
                            {req.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {req.tags.map((tag) => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="validation" className="space-y-4">
                  {validationResult && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Validation Status</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex items-center gap-2">
                              {validationResult.isValid ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-yellow-500" />
                              )}
                              <span className="text-sm">
                                {validationResult.isValid ? 'Valid' : 'Needs Improvement'}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Completeness Score</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">{validationResult.completenessScore}%</div>
                          </CardContent>
                        </Card>
                      </div>

                      {validationResult.missingAreas.length > 0 && (
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Missing Areas</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="text-sm space-y-1">
                              {validationResult.missingAreas.map((area, index) => (
                                <li key={index} className="flex items-center gap-2">
                                  <AlertCircle className="h-3 w-3 text-yellow-500" />
                                  {area}
                                </li>
                              ))}
                            </ul>
                          </CardContent>
                        </Card>
                      )}

                      {validationResult.qualityIssues.length > 0 && (
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Quality Issues</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="text-sm space-y-1">
                              {validationResult.qualityIssues.map((issue, index) => (
                                <li key={index} className="flex items-center gap-2">
                                  <AlertCircle className="h-3 w-3 text-red-500" />
                                  {issue}
                                </li>
                              ))}
                            </ul>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="iterations" className="space-y-4">
                  <ScrollArea className="h-96">
                    <div className="space-y-4">
                      {iterations.map((iteration) => (
                        <Card key={iteration.iteration}>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base">Iteration {iteration.iteration}</CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-2">
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div>
                                <strong>Requirements:</strong> {iteration.requirements.length}
                              </div>
                              <div>
                                <strong>Score:</strong> {iteration.validation.completenessScore}%
                              </div>
                              <div>
                                <strong>Valid:</strong> {iteration.validation.isValid ? 'Yes' : 'No'}
                              </div>
                            </div>
                            {iteration.improvements.length > 0 && (
                              <div>
                                <strong className="text-sm">Improvements:</strong>
                                <ul className="text-sm mt-1 space-y-1">
                                  {iteration.improvements.map((improvement, index) => (
                                    <li key={index} className="text-muted-foreground">
                                      • {improvement}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-4">
                  {currentRequirements.recommendations.length > 0 ? (
                    <div className="space-y-2">
                      {currentRequirements.recommendations.map((recommendation, index) => (
                        <Card key={index}>
                          <CardContent className="pt-4">
                            <div className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5" />
                              <span className="text-sm">{recommendation}</span>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      No recommendations available
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}