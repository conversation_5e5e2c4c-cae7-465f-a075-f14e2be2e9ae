"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  Bot, 
  User, 
  CheckCircle, 
  Clock, 
  Edit3, 
  Save, 
  RefreshCw,
  Play,
  Pause,
  AlertCircle,
  Zap,
  Eye,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Settings,
  Download,
  Upload,
  Copy,
  Trash2,
  Plus,
  Minus,
  Target,
  Users,
  Star
} from "lucide-react"
import { useRequirementsAgent } from "@/hooks/use-requirements-agent"

interface UserStory {
  id: string
  title: string
  asA: string
  iWant: string
  soThat: string
  acceptanceCriteria: string[]
  priority: 'low' | 'medium' | 'high' | 'critical'
  storyPoints: number
  status: 'draft' | 'review' | 'approved' | 'rejected'
  feedback?: string
  version: number
}

interface RequirementsGeneratorProps {
  projectDescription?: string
  onRequirementsGenerated?: (requirements: UserStory[]) => void
  className?: string
}

export default function RequirementsGenerator({ 
  projectDescription = "",
  onRequirementsGenerated,
  className = "" 
}: RequirementsGeneratorProps) {
  const {
    isGenerating,
    progress,
    currentThought,
    userStories,
    generateRequirements,
    regenerateStory,
    approveStory,
    rejectStory,
    editStory
  } = useRequirementsAgent()

  const [projectInput, setProjectInput] = useState(projectDescription)
  const [editingStory, setEditingStory] = useState<string | null>(null)
  const [newStoryData, setNewStoryData] = useState<Partial<UserStory>>({})
  const [showAddStory, setShowAddStory] = useState(false)

  useEffect(() => {
    if (onRequirementsGenerated && userStories.length > 0) {
      onRequirementsGenerated(userStories)
    }
  }, [userStories, onRequirementsGenerated])

  const handleGenerateRequirements = async () => {
    if (!projectInput.trim()) return
    await generateRequirements(projectInput)
  }

  const handleEditStory = (storyId: string, updates: Partial<UserStory>) => {
    editStory(storyId, updates)
    setEditingStory(null)
  }

  const handleAddNewStory = () => {
    if (!newStoryData.title || !newStoryData.asA || !newStoryData.iWant || !newStoryData.soThat) return

    const newStory: UserStory = {
      id: `story-${Date.now()}`,
      title: newStoryData.title || '',
      asA: newStoryData.asA || '',
      iWant: newStoryData.iWant || '',
      soThat: newStoryData.soThat || '',
      acceptanceCriteria: newStoryData.acceptanceCriteria || [],
      priority: newStoryData.priority || 'medium',
      storyPoints: newStoryData.storyPoints || 3,
      status: 'draft',
      version: 1
    }

    editStory(newStory.id, newStory)
    setNewStoryData({})
    setShowAddStory(false)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-600/20 text-red-400 border-red-600/30'
      case 'high':
        return 'bg-orange-600/20 text-orange-400 border-orange-600/30'
      case 'medium':
        return 'bg-yellow-600/20 text-yellow-400 border-yellow-600/30'
      case 'low':
        return 'bg-green-600/20 text-green-400 border-green-600/30'
      default:
        return 'bg-gray-600/20 text-gray-400 border-gray-600/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit3 className="w-4 h-4 text-gray-400" />
      case 'review':
        return <Eye className="w-4 h-4 text-yellow-400" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'rejected':
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Project Input */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Requirements Generation
          </CardTitle>
          <CardDescription className="text-gray-400">
            Describe your project and let AI generate comprehensive user stories and requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm text-white">Project Description</Label>
            <Textarea
              value={projectInput}
              onChange={(e) => setProjectInput(e.target.value)}
              placeholder="Describe your project in detail. Include the purpose, target users, key features, and any specific requirements..."
              className="min-h-24 bg-[#111111] border-[#1a1a1a] text-white placeholder:text-gray-500"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              onClick={handleGenerateRequirements}
              disabled={!projectInput.trim() || isGenerating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isGenerating ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Generate Requirements
                </>
              )}
            </Button>
            
            {userStories.length > 0 && (
              <Button 
                variant="outline" 
                onClick={() => setShowAddStory(true)}
                className="bg-transparent border-[#2a2a2a] text-gray-300 hover:text-white hover:bg-[#1a1a1a]"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Story
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Generation Progress */}
      {isGenerating && (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Bot className="w-5 h-5 text-blue-400" />
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-white">AI Requirements Agent</span>
                  <span className="text-gray-400">{progress}%</span>
                </div>
                <Progress value={progress} className="h-2 bg-[#1a1a1a]" />
              </div>
            </div>
            {currentThought && (
              <div className="text-sm text-gray-300 bg-[#111111] p-3 rounded border border-[#1a1a1a]">
                <div className="flex items-center gap-2 mb-1">
                  <MessageSquare className="w-3 h-3 text-blue-400" />
                  <span className="text-xs text-gray-500">Agent Thinking</span>
                </div>
                {currentThought}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* User Stories */}
      {userStories.length > 0 && (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  User Stories ({userStories.length})
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Generated requirements in user story format
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge className="bg-green-600/20 text-green-400 border-green-600/30">
                  {userStories.filter(s => s.status === 'approved').length} Approved
                </Badge>
                <Badge className="bg-yellow-600/20 text-yellow-400 border-yellow-600/30">
                  {userStories.filter(s => s.status === 'review').length} In Review
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="max-h-96">
              <div className="space-y-4">
                {userStories.map((story) => (
                  <UserStoryCard
                    key={story.id}
                    story={story}
                    isEditing={editingStory === story.id}
                    onEdit={() => setEditingStory(story.id)}
                    onSave={(updates) => handleEditStory(story.id, updates)}
                    onCancel={() => setEditingStory(null)}
                    onApprove={() => approveStory(story.id)}
                    onReject={(feedback) => rejectStory(story.id, feedback)}
                    onRegenerate={() => regenerateStory(story.id)}
                  />
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Add New Story Modal */}
      {showAddStory && (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Plus className="w-5 h-5" />
              Add New User Story
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm text-white">Story Title</Label>
                <Input
                  value={newStoryData.title || ''}
                  onChange={(e) => setNewStoryData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Brief title for the user story"
                  className="bg-[#111111] border-[#1a1a1a] text-white"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-white">Priority</Label>
                <select
                  value={newStoryData.priority || 'medium'}
                  onChange={(e) => setNewStoryData(prev => ({ ...prev, priority: e.target.value as any }))}
                  className="w-full p-2 bg-[#111111] border border-[#1a1a1a] rounded text-white text-sm"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm text-white">As a...</Label>
                <Input
                  value={newStoryData.asA || ''}
                  onChange={(e) => setNewStoryData(prev => ({ ...prev, asA: e.target.value }))}
                  placeholder="user, admin, customer..."
                  className="bg-[#111111] border-[#1a1a1a] text-white"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-white">I want...</Label>
                <Input
                  value={newStoryData.iWant || ''}
                  onChange={(e) => setNewStoryData(prev => ({ ...prev, iWant: e.target.value }))}
                  placeholder="to be able to..."
                  className="bg-[#111111] border-[#1a1a1a] text-white"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-white">So that...</Label>
                <Input
                  value={newStoryData.soThat || ''}
                  onChange={(e) => setNewStoryData(prev => ({ ...prev, soThat: e.target.value }))}
                  placeholder="I can achieve..."
                  className="bg-[#111111] border-[#1a1a1a] text-white"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button onClick={handleAddNewStory} className="bg-green-600 hover:bg-green-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Story
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowAddStory(false)}
                className="bg-transparent border-[#2a2a2a] text-gray-300 hover:text-white hover:bg-[#1a1a1a]"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// User Story Card Component
interface UserStoryCardProps {
  story: UserStory
  isEditing: boolean
  onEdit: () => void
  onSave: (updates: Partial<UserStory>) => void
  onCancel: () => void
  onApprove: () => void
  onReject: (feedback: string) => void
  onRegenerate: () => void
}

function UserStoryCard({ 
  story, 
  isEditing, 
  onEdit, 
  onSave, 
  onCancel, 
  onApprove, 
  onReject, 
  onRegenerate 
}: UserStoryCardProps) {
  const [editData, setEditData] = useState(story)
  const [rejectFeedback, setRejectFeedback] = useState('')
  const [showRejectDialog, setShowRejectDialog] = useState(false)

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-600/20 text-red-400 border-red-600/30'
      case 'high':
        return 'bg-orange-600/20 text-orange-400 border-orange-600/30'
      case 'medium':
        return 'bg-yellow-600/20 text-yellow-400 border-yellow-600/30'
      case 'low':
        return 'bg-green-600/20 text-green-400 border-green-600/30'
      default:
        return 'bg-gray-600/20 text-gray-400 border-gray-600/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit3 className="w-4 h-4 text-gray-400" />
      case 'review':
        return <Eye className="w-4 h-4 text-yellow-400" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'rejected':
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <Card className="bg-[#111111] border-[#1a1a1a]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStatusIcon(story.status)}
            <div>
              <CardTitle className="text-white text-sm">{story.title}</CardTitle>
              <CardDescription className="text-xs">
                Version {story.version} • {story.storyPoints} story points
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={`text-xs ${getPriorityColor(story.priority)}`}>
              {story.priority}
            </Badge>
            <Badge variant="outline" className="text-xs capitalize">
              {story.status}
            </Badge>
            {!isEditing && story.status !== 'approved' && (
              <Button size="sm" variant="ghost" onClick={onEdit}>
                <Edit3 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm text-white">Title</Label>
              <Input
                value={editData.title}
                onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                className="bg-[#0a0a0a] border-[#1a1a1a] text-white"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm text-white">As a...</Label>
                <Input
                  value={editData.asA}
                  onChange={(e) => setEditData(prev => ({ ...prev, asA: e.target.value }))}
                  className="bg-[#0a0a0a] border-[#1a1a1a] text-white"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-white">I want...</Label>
                <Input
                  value={editData.iWant}
                  onChange={(e) => setEditData(prev => ({ ...prev, iWant: e.target.value }))}
                  className="bg-[#0a0a0a] border-[#1a1a1a] text-white"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm text-white">So that...</Label>
                <Input
                  value={editData.soThat}
                  onChange={(e) => setEditData(prev => ({ ...prev, soThat: e.target.value }))}
                  className="bg-[#0a0a0a] border-[#1a1a1a] text-white"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm text-white">Acceptance Criteria</Label>
              <Textarea
                value={editData.acceptanceCriteria.join('\n')}
                onChange={(e) => setEditData(prev => ({ 
                  ...prev, 
                  acceptanceCriteria: e.target.value.split('\n').filter(line => line.trim()) 
                }))}
                placeholder="Enter each acceptance criteria on a new line"
                className="bg-[#0a0a0a] border-[#1a1a1a] text-white"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button size="sm" onClick={() => onSave(editData)}>
                <Save className="w-3 h-3 mr-2" />
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* User Story Format */}
            <div className="bg-[#0a0a0a] p-4 rounded border border-[#1a1a1a]">
              <div className="text-sm text-gray-300 space-y-1">
                <div><span className="text-blue-400 font-medium">As a</span> {story.asA}</div>
                <div><span className="text-green-400 font-medium">I want</span> {story.iWant}</div>
                <div><span className="text-purple-400 font-medium">So that</span> {story.soThat}</div>
              </div>
            </div>

            {/* Acceptance Criteria */}
            {story.acceptanceCriteria.length > 0 && (
              <div>
                <div className="text-sm font-medium text-white mb-2">Acceptance Criteria:</div>
                <ul className="space-y-1">
                  {story.acceptanceCriteria.map((criteria, index) => (
                    <li key={index} className="text-sm text-gray-300 flex items-start gap-2">
                      <CheckCircle className="w-3 h-3 text-green-400 mt-0.5 flex-shrink-0" />
                      {criteria}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            {story.status === 'draft' && (
              <div className="flex items-center gap-2 pt-2 border-t border-[#1a1a1a]">
                <Button size="sm" onClick={onApprove} className="bg-green-600 hover:bg-green-700">
                  <ThumbsUp className="w-3 h-3 mr-2" />
                  Approve
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => setShowRejectDialog(true)}
                  className="border-red-600/30 text-red-400 hover:bg-red-600/10"
                >
                  <ThumbsDown className="w-3 h-3 mr-2" />
                  Request Changes
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={onRegenerate}
                  className="border-blue-600/30 text-blue-400 hover:bg-blue-600/10"
                >
                  <RefreshCw className="w-3 h-3 mr-2" />
                  Regenerate
                </Button>
              </div>
            )}

            {/* Reject Dialog */}
            {showRejectDialog && (
              <div className="space-y-3 p-3 bg-[#0a0a0a] rounded border border-[#1a1a1a]">
                <Label className="text-sm text-white">Feedback for improvements:</Label>
                <Textarea
                  value={rejectFeedback}
                  onChange={(e) => setRejectFeedback(e.target.value)}
                  placeholder="Explain what needs to be changed or improved..."
                  className="bg-[#111111] border-[#1a1a1a] text-white"
                />
                <div className="flex items-center gap-2">
                  <Button 
                    size="sm" 
                    onClick={() => {
                      onReject(rejectFeedback)
                      setShowRejectDialog(false)
                      setRejectFeedback('')
                    }}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Submit Feedback
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setShowRejectDialog(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* User Feedback */}
            {story.feedback && (
              <div className="p-3 bg-red-900/20 rounded border border-red-600/30">
                <div className="text-xs text-red-400 mb-1">User Feedback:</div>
                <div className="text-sm text-gray-300">{story.feedback}</div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}