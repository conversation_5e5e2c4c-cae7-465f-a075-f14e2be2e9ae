/**
 * AG3NT Framework - Adaptive Learning System
 *
 * Advanced machine learning system that enables agents to improve their
 * performance over time by learning from past executions, user feedback,
 * and success patterns.
 *
 * Features:
 * - Performance pattern recognition
 * - Automatic parameter optimization
 * - Success/failure analysis
 * - Predictive performance modeling
 * - Continuous improvement algorithms
 * - Knowledge transfer between agents
 */
import { EventEmitter } from "events";
export interface LearningConfiguration {
    enabled: boolean;
    learningRate: number;
    memorySize: number;
    optimizationInterval: number;
    knowledgeSharing: boolean;
    performanceThreshold: number;
    adaptationStrategy: 'conservative' | 'moderate' | 'aggressive';
}
export interface ExecutionRecord {
    executionId: string;
    agentId: string;
    agentType: string;
    task: TaskRecord;
    performance: PerformanceMetrics;
    context: ExecutionContext;
    outcome: ExecutionOutcome;
    timestamp: string;
    duration: number;
}
export interface TaskRecord {
    taskId: string;
    type: string;
    complexity: number;
    requirements: any;
    constraints: any;
    priority: string;
}
export interface PerformanceMetrics {
    accuracy: number;
    efficiency: number;
    quality: number;
    speed: number;
    resourceUsage: ResourceUsage;
    userSatisfaction: number;
    errorRate: number;
    completionRate: number;
}
export interface ResourceUsage {
    cpu: number;
    memory: number;
    network: number;
    storage: number;
    cost: number;
}
export interface ExecutionContext {
    environment: string;
    dependencies: string[];
    constraints: any;
    userPreferences: any;
    systemLoad: number;
    timeOfDay: string;
}
export interface ExecutionOutcome {
    status: 'success' | 'failure' | 'partial';
    results: any;
    feedback: UserFeedback;
    issues: ExecutionIssue[];
    improvements: string[];
}
export interface UserFeedback {
    rating: number;
    comments: string[];
    suggestions: string[];
    satisfaction: number;
    wouldRecommend: boolean;
}
export interface ExecutionIssue {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    impact: string;
    resolution?: string;
}
export interface LearningPattern {
    patternId: string;
    type: 'performance' | 'failure' | 'optimization' | 'context';
    description: string;
    conditions: PatternCondition[];
    outcomes: PatternOutcome[];
    confidence: number;
    frequency: number;
    lastSeen: string;
}
export interface PatternCondition {
    field: string;
    operator: string;
    value: any;
    weight: number;
}
export interface PatternOutcome {
    metric: string;
    impact: number;
    probability: number;
    recommendation: string;
}
export interface OptimizationRecommendation {
    agentId: string;
    parameter: string;
    currentValue: any;
    recommendedValue: any;
    expectedImprovement: number;
    confidence: number;
    rationale: string;
    riskLevel: 'low' | 'medium' | 'high';
}
export interface KnowledgeTransfer {
    fromAgent: string;
    toAgent: string;
    knowledge: TransferableKnowledge;
    applicability: number;
    transferDate: string;
    effectiveness?: number;
}
export interface TransferableKnowledge {
    type: 'pattern' | 'optimization' | 'strategy' | 'configuration';
    content: any;
    context: any;
    performance: PerformanceMetrics;
    conditions: string[];
}
export interface LearningInsight {
    type: 'trend' | 'anomaly' | 'opportunity' | 'risk';
    description: string;
    evidence: string[];
    impact: 'high' | 'medium' | 'low';
    actionable: boolean;
    recommendations: string[];
    confidence: number;
}
export interface AdaptationResult {
    agentId: string;
    adaptations: AgentAdaptation[];
    expectedImpact: number;
    riskAssessment: RiskAssessment;
    rollbackPlan: RollbackPlan;
    monitoringPlan: MonitoringPlan;
}
export interface AgentAdaptation {
    type: 'parameter' | 'strategy' | 'configuration' | 'capability';
    target: string;
    change: AdaptationChange;
    rationale: string;
    expectedBenefit: string;
}
export interface AdaptationChange {
    from: any;
    to: any;
    changeType: 'increment' | 'decrement' | 'replace' | 'add' | 'remove';
    magnitude: number;
}
export interface RiskAssessment {
    overallRisk: 'low' | 'medium' | 'high';
    risks: IdentifiedRisk[];
    mitigations: RiskMitigation[];
    contingencies: string[];
}
export interface IdentifiedRisk {
    type: string;
    probability: number;
    impact: number;
    description: string;
    indicators: string[];
}
export interface RiskMitigation {
    risk: string;
    strategy: string;
    effectiveness: number;
    cost: number;
}
export interface RollbackPlan {
    triggers: RollbackTrigger[];
    steps: RollbackStep[];
    timeframe: number;
    dataBackup: boolean;
}
export interface RollbackTrigger {
    metric: string;
    threshold: number;
    timeWindow: number;
    action: string;
}
export interface RollbackStep {
    order: number;
    action: string;
    parameters: any;
    validation: string;
}
export interface MonitoringPlan {
    metrics: string[];
    frequency: number;
    duration: number;
    alerts: AlertConfiguration[];
    reports: ReportConfiguration[];
}
export interface AlertConfiguration {
    metric: string;
    condition: string;
    threshold: number;
    severity: string;
    channels: string[];
}
export interface ReportConfiguration {
    type: string;
    frequency: string;
    recipients: string[];
    content: string[];
}
/**
 * Adaptive Learning System - Enables continuous agent improvement
 */
export declare class AdaptiveLearningSystem extends EventEmitter {
    private config;
    private executionHistory;
    private learningPatterns;
    private optimizationRecommendations;
    private knowledgeBase;
    private learningInsights;
    private isLearning;
    constructor(config?: Partial<LearningConfiguration>);
    /**
     * Record execution for learning
     */
    recordExecution(record: ExecutionRecord): void;
    /**
     * Analyze patterns and generate learning insights
     */
    analyzeAndLearn(agentId: string): Promise<LearningInsight[]>;
    /**
     * Apply optimizations to agent
     */
    applyOptimizations(agentId: string, approvedRecommendations: string[]): Promise<AdaptationResult>;
    /**
     * Get learning insights for agent
     */
    getLearningInsights(agentId?: string): LearningInsight[];
    /**
     * Get optimization recommendations for agent
     */
    getOptimizationRecommendations(agentId: string): OptimizationRecommendation[];
    /**
     * Get transferable knowledge for agent type
     */
    getTransferableKnowledge(agentType: string): TransferableKnowledge[];
    /**
     * Start continuous learning process
     */
    private startLearningProcess;
    /**
     * Perform periodic learning across all agents
     */
    private performPeriodicLearning;
    /**
     * Check if execution is significant enough to trigger immediate learning
     */
    private isSignificantEvent;
    /**
     * Analyze performance patterns in execution history
     */
    private analyzePerformancePatterns;
    /**
     * Analyze failure patterns
     */
    private analyzeFailurePatterns;
    /**
     * Detect optimization opportunities
     */
    private detectOptimizationOpportunities;
    /**
     * Analyze contextual factors
     */
    private analyzeContextualFactors;
    /**
     * Generate learning insights from patterns
     */
    private generateLearningInsights;
    /**
     * Generate optimization recommendations
     */
    private generateOptimizationRecommendations;
    /**
     * Share knowledge between agents
     */
    private shareKnowledge;
    /**
     * Helper methods for analysis
     */
    private analyzeTrend;
    private groupFailuresByCharacteristics;
    private findOptimalResourceConfigurations;
    private analyzeTimeBasedPatterns;
    private analyzeEnvironmentPatterns;
    private assessAdaptationRisks;
    private createRollbackPlan;
    private createMonitoringPlan;
    private cleanupOldInsights;
    /**
     * Shutdown learning system
     */
    shutdown(): Promise<void>;
}
export default AdaptiveLearningSystem;
//# sourceMappingURL=adaptive-learning-system.d.ts.map