import { NextRequest, NextResponse } from 'next/server'
import { AIAgent } from '@/lib/agent-core'

// Global agent instance (in production, you'd use proper state management)
let agentInstance: AIAgent | null = null

function getAgent() {
  if (!agentInstance) {
    agentInstance = new AIAgent('openai', 'gpt-4o-mini')
  }
  return agentInstance
}

export async function POST(req: NextRequest) {
  try {
    const { action, ...params } = await req.json()

    const agent = getAgent()

    switch (action) {
      case 'chat':
        const { message, conversationId, stream = false } = params
        
        if (stream) {
          const result = await agent.streamChat(message, conversationId)
          return result.toDataStreamResponse()
        } else {
          const response = await agent.chat(message, conversationId)
          return NextResponse.json({ response })
        }

      case 'executeTask':
        const { taskDescription } = params
        const task = await agent.executeTask(taskDescription)
        return NextResponse.json({ task })

      case 'getMemory':
        const memory = agent.getMemory()
        return NextResponse.json({ memory })

      case 'setPreference':
        const { key, value } = params
        agent.setPreference(key, value)
        return NextResponse.json({ success: true })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Agent API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const agent = getAgent()
    const memory = agent.getMemory()
    
    return NextResponse.json({
      status: 'active',
      memoryStats: {
        conversations: memory.conversations.length,
        facts: memory.facts.length,
        preferences: Object.keys(memory.preferences).length
      }
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get agent status' },
      { status: 500 }
    )
  }
}
