"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  MessageSquare, 
  Code, 
  Brain, 
  Sparkles, 
  Rocket,
  CheckCircle,
  ExternalLink
} from "lucide-react"

import AIC<PERSON> from "@/components/ai-chat"
import AIAssistant from "@/components/ai-assistant"
import AICodeGenerator from "@/components/ai/ai-code-generator"

interface Task {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  progress: number
  type: "code" | "file" | "database" | "test"
}

export default function AIDemoPage() {
  const [assistantTasks, setAssistantTasks] = useState<Task[]>([])
  const [assistantActive, setAssistantActive] = useState(false)

  const handleTaskGenerated = (tasks: Task[]) => {
    setAssistantTasks(prev => [...prev, ...tasks])
  }

  const handleAssistantStart = () => {
    setAssistantActive(true)
    // Simulate some initial tasks
    const initialTasks: Task[] = [
      {
        id: "1",
        title: "Analyze project structure",
        description: "Scanning files and dependencies",
        status: "running",
        progress: 75,
        type: "file"
      },
      {
        id: "2", 
        title: "Generate component tests",
        description: "Creating unit tests for React components",
        status: "pending",
        progress: 0,
        type: "test"
      }
    ]
    setAssistantTasks(initialTasks)
  }

  const handleAssistantStop = () => {
    setAssistantActive(false)
    setAssistantTasks(prev => 
      prev.map(task => ({ ...task, status: "completed" as const }))
    )
  }

  const features = [
    {
      icon: MessageSquare,
      title: "AI Chat",
      description: "Real-time streaming conversations with multiple AI providers",
      highlights: ["OpenAI, Anthropic, Google models", "Streaming responses", "Message history"]
    },
    {
      icon: Code,
      title: "Code Generation", 
      description: "Generate production-ready code with AI assistance",
      highlights: ["Multi-file output", "Multiple languages", "Download & copy"]
    },
    {
      icon: Brain,
      title: "AI Assistant",
      description: "Intelligent task planning and project management",
      highlights: ["Task extraction", "Progress tracking", "Interactive chat"]
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2">
          <Sparkles className="w-8 h-8 text-blue-400" />
          <h1 className="text-4xl font-bold text-white">AI SDK Demo</h1>
        </div>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto">
          Experience the power of Vercel AI SDK integration with real-time chat, 
          code generation, and intelligent assistance.
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge className="bg-green-500">
            <CheckCircle className="w-3 h-3 mr-1" />
            AI SDK Integrated
          </Badge>
          <Badge variant="outline">
            <Rocket className="w-3 h-3 mr-1" />
            Production Ready
          </Badge>
        </div>
      </div>

      {/* Features Overview */}
      <div className="grid md:grid-cols-3 gap-6">
        {features.map((feature, index) => (
          <Card key={index} className="bg-[#1a1a1a] border-[#333]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <feature.icon className="w-5 h-5 text-blue-400" />
                {feature.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-400 text-sm mb-3">{feature.description}</p>
              <ul className="space-y-1">
                {feature.highlights.map((highlight, i) => (
                  <li key={i} className="text-xs text-gray-500 flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-green-400" />
                    {highlight}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Interactive Demo */}
      <Card className="bg-[#1a1a1a] border-[#333]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-blue-400" />
              Interactive Demo
            </CardTitle>
            <Button variant="outline" size="sm" asChild>
              <a href="/README-AI-SDK.md" target="_blank" className="flex items-center gap-1">
                <ExternalLink className="w-3 h-3" />
                Documentation
              </a>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="chat" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#2a2a2a]">
              <TabsTrigger value="chat" className="data-[state=active]:bg-blue-500">
                <MessageSquare className="w-4 h-4 mr-1" />
                AI Chat
              </TabsTrigger>
              <TabsTrigger value="code" className="data-[state=active]:bg-blue-500">
                <Code className="w-4 h-4 mr-1" />
                Code Gen
              </TabsTrigger>
              <TabsTrigger value="assistant" className="data-[state=active]:bg-blue-500">
                <Brain className="w-4 h-4 mr-1" />
                Assistant
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="mt-6">
              <div className="h-[600px] border border-[#333] rounded-lg overflow-hidden">
                <AIChat 
                  systemPrompt="You are a helpful AI assistant demonstrating the Vercel AI SDK capabilities. Be informative and engaging."
                  defaultModel="gpt-4o-mini"
                  defaultProvider="openai"
                />
              </div>
            </TabsContent>

            <TabsContent value="code" className="mt-6">
              <AICodeGenerator />
            </TabsContent>

            <TabsContent value="assistant" className="mt-6">
              <div className="max-w-md mx-auto">
                <AIAssistant
                  isActive={assistantActive}
                  tasks={assistantTasks}
                  onStart={handleAssistantStart}
                  onStop={handleAssistantStop}
                  onTaskGenerated={handleTaskGenerated}
                  systemPrompt="You are a development project assistant. Help break down complex development tasks into manageable steps."
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quick Start */}
      <Card className="bg-[#1a1a1a] border-[#333]">
        <CardHeader>
          <CardTitle className="text-white">Quick Start</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="text-white font-medium mb-2">1. Environment Setup</h4>
              <code className="block bg-[#0d1117] p-2 rounded text-gray-300 text-xs">
                cp .env.local.example .env.local<br/>
                # Add your API keys
              </code>
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">2. Import Components</h4>
              <code className="block bg-[#0d1117] p-2 rounded text-gray-300 text-xs">
                import AIChat from '@/components/ai-chat'<br/>
                import AICodeGenerator from '@/components/ai/ai-code-generator'
              </code>
            </div>
          </div>
          <p className="text-gray-400 text-sm">
            Check the documentation for detailed usage examples and API reference.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
