"use strict";
/**
 * AG3NT Framework - Adaptive Learning System
 *
 * Advanced machine learning system that enables agents to improve their
 * performance over time by learning from past executions, user feedback,
 * and success patterns.
 *
 * Features:
 * - Performance pattern recognition
 * - Automatic parameter optimization
 * - Success/failure analysis
 * - Predictive performance modeling
 * - Continuous improvement algorithms
 * - Knowledge transfer between agents
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdaptiveLearningSystem = void 0;
const events_1 = require("events");
/**
 * Adaptive Learning System - Enables continuous agent improvement
 */
class AdaptiveLearningSystem extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.executionHistory = new Map();
        this.learningPatterns = new Map();
        this.optimizationRecommendations = new Map();
        this.knowledgeBase = new Map();
        this.learningInsights = [];
        this.isLearning = false;
        this.config = {
            enabled: true,
            learningRate: 0.1,
            memorySize: 10000,
            optimizationInterval: 3600000, // 1 hour
            knowledgeSharing: true,
            performanceThreshold: 0.8,
            adaptationStrategy: 'moderate',
            ...config
        };
        if (this.config.enabled) {
            this.startLearningProcess();
        }
    }
    /**
     * Record execution for learning
     */
    recordExecution(record) {
        if (!this.config.enabled)
            return;
        const agentHistory = this.executionHistory.get(record.agentId) || [];
        agentHistory.push(record);
        // Maintain memory size limit
        if (agentHistory.length > this.config.memorySize) {
            agentHistory.shift();
        }
        this.executionHistory.set(record.agentId, agentHistory);
        // Trigger immediate learning if significant event
        if (this.isSignificantEvent(record)) {
            this.analyzeAndLearn(record.agentId);
        }
        this.emit('execution_recorded', { agentId: record.agentId, record });
    }
    /**
     * Analyze patterns and generate learning insights
     */
    async analyzeAndLearn(agentId) {
        if (!this.config.enabled || this.isLearning)
            return [];
        this.isLearning = true;
        console.log(`🧠 Starting learning analysis for agent: ${agentId}`);
        try {
            const history = this.executionHistory.get(agentId) || [];
            if (history.length < 5)
                return []; // Need minimum data
            // Analyze performance patterns
            const performancePatterns = await this.analyzePerformancePatterns(history);
            // Identify failure patterns
            const failurePatterns = await this.analyzeFailurePatterns(history);
            // Detect optimization opportunities
            const optimizationOpportunities = await this.detectOptimizationOpportunities(history);
            // Generate contextual insights
            const contextualInsights = await this.analyzeContextualFactors(history);
            // Combine all patterns
            const allPatterns = [
                ...performancePatterns,
                ...failurePatterns,
                ...optimizationOpportunities,
                ...contextualInsights
            ];
            this.learningPatterns.set(agentId, allPatterns);
            // Generate insights
            const insights = await this.generateLearningInsights(agentId, allPatterns);
            this.learningInsights.push(...insights);
            // Generate optimization recommendations
            const recommendations = await this.generateOptimizationRecommendations(agentId, allPatterns);
            this.optimizationRecommendations.set(agentId, recommendations);
            // Share knowledge if enabled
            if (this.config.knowledgeSharing) {
                await this.shareKnowledge(agentId, allPatterns);
            }
            this.emit('learning_completed', { agentId, insights, recommendations });
            console.log(`✅ Learning analysis completed for agent: ${agentId}`);
            return insights;
        }
        finally {
            this.isLearning = false;
        }
    }
    /**
     * Apply optimizations to agent
     */
    async applyOptimizations(agentId, approvedRecommendations) {
        const recommendations = this.optimizationRecommendations.get(agentId) || [];
        const toApply = recommendations.filter(r => approvedRecommendations.includes(r.parameter));
        if (toApply.length === 0) {
            throw new Error('No valid recommendations to apply');
        }
        // Create adaptations
        const adaptations = toApply.map(rec => ({
            type: 'parameter',
            target: rec.parameter,
            change: {
                from: rec.currentValue,
                to: rec.recommendedValue,
                changeType: 'replace',
                magnitude: Math.abs(rec.expectedImprovement)
            },
            rationale: rec.rationale,
            expectedBenefit: `${rec.expectedImprovement}% improvement`
        }));
        // Assess risks
        const riskAssessment = await this.assessAdaptationRisks(adaptations);
        // Create rollback plan
        const rollbackPlan = await this.createRollbackPlan(adaptations);
        // Create monitoring plan
        const monitoringPlan = await this.createMonitoringPlan(adaptations);
        const result = {
            agentId,
            adaptations,
            expectedImpact: toApply.reduce((sum, rec) => sum + rec.expectedImprovement, 0) / toApply.length,
            riskAssessment,
            rollbackPlan,
            monitoringPlan
        };
        this.emit('optimizations_applied', { agentId, result });
        return result;
    }
    /**
     * Get learning insights for agent
     */
    getLearningInsights(agentId) {
        if (agentId) {
            return this.learningInsights.filter(insight => insight.description.includes(agentId) ||
                insight.evidence.some(e => e.includes(agentId)));
        }
        return this.learningInsights;
    }
    /**
     * Get optimization recommendations for agent
     */
    getOptimizationRecommendations(agentId) {
        return this.optimizationRecommendations.get(agentId) || [];
    }
    /**
     * Get transferable knowledge for agent type
     */
    getTransferableKnowledge(agentType) {
        return this.knowledgeBase.get(agentType) || [];
    }
    /**
     * Start continuous learning process
     */
    startLearningProcess() {
        setInterval(() => {
            this.performPeriodicLearning();
        }, this.config.optimizationInterval);
        console.log('🧠 Adaptive Learning System started');
    }
    /**
     * Perform periodic learning across all agents
     */
    async performPeriodicLearning() {
        console.log('🔄 Performing periodic learning analysis...');
        for (const agentId of this.executionHistory.keys()) {
            try {
                await this.analyzeAndLearn(agentId);
            }
            catch (error) {
                console.error(`Learning failed for agent ${agentId}:`, error);
            }
        }
        // Clean up old insights
        this.cleanupOldInsights();
    }
    /**
     * Check if execution is significant enough to trigger immediate learning
     */
    isSignificantEvent(record) {
        return (record.outcome.status === 'failure' ||
            record.performance.accuracy < this.config.performanceThreshold ||
            record.performance.userSatisfaction < 0.7 ||
            record.outcome.feedback.rating < 3);
    }
    /**
     * Analyze performance patterns in execution history
     */
    async analyzePerformancePatterns(history) {
        const patterns = [];
        // Analyze accuracy trends
        const accuracyTrend = this.analyzeTrend(history.map(h => h.performance.accuracy));
        if (Math.abs(accuracyTrend) > 0.1) {
            patterns.push({
                patternId: `accuracy-trend-${Date.now()}`,
                type: 'performance',
                description: `Accuracy ${accuracyTrend > 0 ? 'improving' : 'declining'} trend detected`,
                conditions: [{ field: 'accuracy', operator: 'trend', value: accuracyTrend, weight: 1.0 }],
                outcomes: [{
                        metric: 'accuracy',
                        impact: accuracyTrend,
                        probability: 0.8,
                        recommendation: accuracyTrend > 0 ? 'Continue current approach' : 'Investigate accuracy issues'
                    }],
                confidence: 0.8,
                frequency: history.length,
                lastSeen: new Date().toISOString()
            });
        }
        // Analyze efficiency patterns
        const efficiencyTrend = this.analyzeTrend(history.map(h => h.performance.efficiency));
        if (Math.abs(efficiencyTrend) > 0.1) {
            patterns.push({
                patternId: `efficiency-trend-${Date.now()}`,
                type: 'performance',
                description: `Efficiency ${efficiencyTrend > 0 ? 'improving' : 'declining'} trend detected`,
                conditions: [{ field: 'efficiency', operator: 'trend', value: efficiencyTrend, weight: 1.0 }],
                outcomes: [{
                        metric: 'efficiency',
                        impact: efficiencyTrend,
                        probability: 0.8,
                        recommendation: efficiencyTrend > 0 ? 'Optimize for efficiency' : 'Review resource usage'
                    }],
                confidence: 0.8,
                frequency: history.length,
                lastSeen: new Date().toISOString()
            });
        }
        return patterns;
    }
    /**
     * Analyze failure patterns
     */
    async analyzeFailurePatterns(history) {
        const patterns = [];
        const failures = history.filter(h => h.outcome.status === 'failure');
        if (failures.length > 0) {
            // Group failures by common characteristics
            const failureGroups = this.groupFailuresByCharacteristics(failures);
            for (const [characteristic, group] of failureGroups.entries()) {
                if (group.length >= 2) { // Pattern needs at least 2 occurrences
                    patterns.push({
                        patternId: `failure-${characteristic}-${Date.now()}`,
                        type: 'failure',
                        description: `Recurring failures related to ${characteristic}`,
                        conditions: [{ field: characteristic, operator: 'equals', value: group[0], weight: 1.0 }],
                        outcomes: [{
                                metric: 'failure_rate',
                                impact: -0.5,
                                probability: group.length / history.length,
                                recommendation: `Address ${characteristic} related issues`
                            }],
                        confidence: Math.min(0.9, group.length / failures.length),
                        frequency: group.length,
                        lastSeen: new Date().toISOString()
                    });
                }
            }
        }
        return patterns;
    }
    /**
     * Detect optimization opportunities
     */
    async detectOptimizationOpportunities(history) {
        const patterns = [];
        // Analyze resource usage efficiency
        const resourceEfficiency = history.map(h => ({
            cpu: h.performance.resourceUsage.cpu,
            memory: h.performance.resourceUsage.memory,
            performance: h.performance.efficiency
        }));
        // Find optimal resource configurations
        const optimalConfigs = this.findOptimalResourceConfigurations(resourceEfficiency);
        for (const config of optimalConfigs) {
            patterns.push({
                patternId: `optimization-${config.type}-${Date.now()}`,
                type: 'optimization',
                description: `Optimization opportunity for ${config.type} usage`,
                conditions: [
                    { field: config.type, operator: 'range', value: config.range, weight: 1.0 }
                ],
                outcomes: [{
                        metric: 'efficiency',
                        impact: config.improvement,
                        probability: 0.7,
                        recommendation: `Optimize ${config.type} usage to ${config.optimal}`
                    }],
                confidence: 0.7,
                frequency: config.frequency,
                lastSeen: new Date().toISOString()
            });
        }
        return patterns;
    }
    /**
     * Analyze contextual factors
     */
    async analyzeContextualFactors(history) {
        const patterns = [];
        // Analyze time-based patterns
        const timePatterns = this.analyzeTimeBasedPatterns(history);
        patterns.push(...timePatterns);
        // Analyze environment-based patterns
        const envPatterns = this.analyzeEnvironmentPatterns(history);
        patterns.push(...envPatterns);
        return patterns;
    }
    /**
     * Generate learning insights from patterns
     */
    async generateLearningInsights(agentId, patterns) {
        const insights = [];
        // High-confidence performance trends
        const performancePatterns = patterns.filter(p => p.type === 'performance' && p.confidence > 0.7);
        if (performancePatterns.length > 0) {
            insights.push({
                type: 'trend',
                description: `Agent ${agentId} shows consistent performance patterns`,
                evidence: performancePatterns.map(p => p.description),
                impact: 'medium',
                actionable: true,
                recommendations: performancePatterns.map(p => p.outcomes[0].recommendation),
                confidence: Math.max(...performancePatterns.map(p => p.confidence))
            });
        }
        // Critical failure patterns
        const failurePatterns = patterns.filter(p => p.type === 'failure' && p.frequency > 2);
        if (failurePatterns.length > 0) {
            insights.push({
                type: 'risk',
                description: `Agent ${agentId} has recurring failure patterns that need attention`,
                evidence: failurePatterns.map(p => p.description),
                impact: 'high',
                actionable: true,
                recommendations: failurePatterns.map(p => p.outcomes[0].recommendation),
                confidence: Math.max(...failurePatterns.map(p => p.confidence))
            });
        }
        // Optimization opportunities
        const optimizationPatterns = patterns.filter(p => p.type === 'optimization');
        if (optimizationPatterns.length > 0) {
            insights.push({
                type: 'opportunity',
                description: `Agent ${agentId} has optimization opportunities`,
                evidence: optimizationPatterns.map(p => p.description),
                impact: 'medium',
                actionable: true,
                recommendations: optimizationPatterns.map(p => p.outcomes[0].recommendation),
                confidence: Math.max(...optimizationPatterns.map(p => p.confidence))
            });
        }
        return insights;
    }
    /**
     * Generate optimization recommendations
     */
    async generateOptimizationRecommendations(agentId, patterns) {
        const recommendations = [];
        for (const pattern of patterns) {
            if (pattern.type === 'optimization' && pattern.confidence > 0.6) {
                const outcome = pattern.outcomes[0];
                recommendations.push({
                    agentId,
                    parameter: pattern.conditions[0].field,
                    currentValue: 'current', // Would be actual current value
                    recommendedValue: outcome.recommendation,
                    expectedImprovement: outcome.impact * 100,
                    confidence: pattern.confidence,
                    rationale: pattern.description,
                    riskLevel: outcome.impact > 0.3 ? 'medium' : 'low'
                });
            }
        }
        return recommendations;
    }
    /**
     * Share knowledge between agents
     */
    async shareKnowledge(agentId, patterns) {
        // Extract transferable knowledge
        const transferablePatterns = patterns.filter(p => p.confidence > 0.8);
        for (const pattern of transferablePatterns) {
            const knowledge = {
                type: 'pattern',
                content: pattern,
                context: { agentId, timestamp: new Date().toISOString() },
                performance: { accuracy: 0.8, efficiency: 0.8, quality: 0.8, speed: 0.8, resourceUsage: { cpu: 0, memory: 0, network: 0, storage: 0, cost: 0 }, userSatisfaction: 0.8, errorRate: 0.1, completionRate: 0.9 },
                conditions: pattern.conditions.map(c => `${c.field} ${c.operator} ${c.value}`)
            };
            // Add to knowledge base for agent type
            const agentType = agentId.split('-')[0]; // Extract agent type from ID
            const typeKnowledge = this.knowledgeBase.get(agentType) || [];
            typeKnowledge.push(knowledge);
            this.knowledgeBase.set(agentType, typeKnowledge);
        }
    }
    /**
     * Helper methods for analysis
     */
    analyzeTrend(values) {
        if (values.length < 2)
            return 0;
        const firstHalf = values.slice(0, Math.floor(values.length / 2));
        const secondHalf = values.slice(Math.floor(values.length / 2));
        const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
        return secondAvg - firstAvg;
    }
    groupFailuresByCharacteristics(failures) {
        const groups = new Map();
        for (const failure of failures) {
            // Group by task type
            const taskType = failure.task.type;
            if (!groups.has('taskType'))
                groups.set('taskType', []);
            groups.get('taskType').push(taskType);
            // Group by environment
            const environment = failure.context.environment;
            if (!groups.has('environment'))
                groups.set('environment', []);
            groups.get('environment').push(environment);
        }
        return groups;
    }
    findOptimalResourceConfigurations(resourceData) {
        // Simplified optimization detection
        return [
            {
                type: 'cpu',
                range: [0.3, 0.7],
                optimal: 0.5,
                improvement: 0.2,
                frequency: 5
            }
        ];
    }
    analyzeTimeBasedPatterns(history) {
        // Simplified time-based analysis
        return [];
    }
    analyzeEnvironmentPatterns(history) {
        // Simplified environment analysis
        return [];
    }
    async assessAdaptationRisks(adaptations) {
        return {
            overallRisk: 'low',
            risks: [],
            mitigations: [],
            contingencies: []
        };
    }
    async createRollbackPlan(adaptations) {
        return {
            triggers: [],
            steps: [],
            timeframe: 3600,
            dataBackup: true
        };
    }
    async createMonitoringPlan(adaptations) {
        return {
            metrics: ['accuracy', 'efficiency', 'errorRate'],
            frequency: 300,
            duration: 3600,
            alerts: [],
            reports: []
        };
    }
    cleanupOldInsights() {
        const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
        this.learningInsights = this.learningInsights.filter(insight => new Date(insight.evidence[0] || '').getTime() > cutoff);
    }
    /**
     * Shutdown learning system
     */
    async shutdown() {
        this.config.enabled = false;
        this.removeAllListeners();
        console.log('🧠 Adaptive Learning System shutdown complete');
    }
}
exports.AdaptiveLearningSystem = AdaptiveLearningSystem;
exports.default = AdaptiveLearningSystem;
//# sourceMappingURL=adaptive-learning-system.js.map