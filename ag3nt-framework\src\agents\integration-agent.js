"use strict";
/**
 * AG3NT Framework - Integration Agent
 *
 * Specialized agent for connecting with external APIs and ensuring
 * service interoperability across the system.
 *
 * Features:
 * - External API integration
 * - Service interoperability
 * - Data transformation and mapping
 * - Protocol adaptation
 * - Integration testing
 * - API gateway management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.IntegrationAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Integration Agent - External API and service integration
 */
class IntegrationAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('integration', {
            capabilities: {
                requiredCapabilities: [
                    'api_integration',
                    'service_connectivity',
                    'data_transformation',
                    'protocol_adaptation',
                    'authentication_setup',
                    'monitoring_configuration',
                    'integration_testing'
                ],
                contextFilters: ['integration', 'api', 'services', 'connectivity', 'data'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.integrationSteps = [
            'analyze_services', 'design_integration', 'implement_adapters',
            'setup_authentication', 'configure_data_flow', 'implement_monitoring',
            'create_tests', 'deploy_integration', 'validate_connectivity'
        ];
    }
    /**
     * Execute integration workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🔗 Starting integration workflow: ${input.task.title}`);
        // Execute integration steps sequentially
        for (const stepId of this.integrationSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Integration workflow completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual integration step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_services':
                return await this.analyzeServicesWithMCP(enhancedState, input);
            case 'design_integration':
                return await this.designIntegrationWithMCP(enhancedState);
            case 'implement_adapters':
                return await this.implementAdaptersWithMCP(enhancedState);
            case 'setup_authentication':
                return await this.setupAuthenticationWithMCP(enhancedState);
            case 'configure_data_flow':
                return await this.configureDataFlowWithMCP(enhancedState);
            case 'implement_monitoring':
                return await this.implementMonitoringWithMCP(enhancedState);
            case 'create_tests':
                return await this.createTestsWithMCP(enhancedState);
            case 'deploy_integration':
                return await this.deployIntegrationWithMCP(enhancedState);
            case 'validate_connectivity':
                return await this.validateConnectivityWithMCP(enhancedState);
            default:
                throw new Error(`Unknown integration step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.integrationSteps.length;
    }
    /**
     * Get relevant documentation for integration
     */
    async getRelevantDocumentation() {
        return {
            integration: 'API integration patterns and best practices',
            connectivity: 'Service connectivity and interoperability',
            authentication: 'Authentication and authorization patterns',
            dataTransformation: 'Data transformation and mapping techniques',
            monitoring: 'Integration monitoring and observability',
            testing: 'Integration testing strategies and patterns'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeServicesWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeServicesForIntegration(input.services, input.apis, input.task.scope);
        this.state.results.serviceAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async designIntegrationWithMCP(state) {
        const serviceAnalysis = this.state.results.serviceAnalysis;
        const design = await ai_service_1.aiService.designIntegrationArchitecture(serviceAnalysis, this.state.input.requirements);
        this.state.results.integrationDesign = design;
        return {
            results: design,
            needsInput: false,
            completed: false
        };
    }
    async implementAdaptersWithMCP(state) {
        const design = this.state.results.integrationDesign;
        const adapters = await ai_service_1.aiService.implementIntegrationAdapters(design);
        this.state.results.adapters = adapters;
        return {
            results: adapters,
            needsInput: false,
            completed: false
        };
    }
    async setupAuthenticationWithMCP(state) {
        const adapters = this.state.results.adapters;
        const authentication = await ai_service_1.aiService.setupIntegrationAuthentication(adapters, this.state.input.requirements.security);
        this.state.results.authentication = authentication;
        return {
            results: authentication,
            needsInput: false,
            completed: false
        };
    }
    async configureDataFlowWithMCP(state) {
        const authentication = this.state.results.authentication;
        const dataFlow = await ai_service_1.aiService.configureIntegrationDataFlow(authentication, this.state.input.requirements.data);
        this.state.results.dataFlow = dataFlow;
        return {
            results: dataFlow,
            needsInput: false,
            completed: false
        };
    }
    async implementMonitoringWithMCP(state) {
        const dataFlow = this.state.results.dataFlow;
        const monitoring = await ai_service_1.aiService.implementIntegrationMonitoring(dataFlow);
        this.state.results.monitoring = monitoring;
        return {
            results: monitoring,
            needsInput: false,
            completed: false
        };
    }
    async createTestsWithMCP(state) {
        const monitoring = this.state.results.monitoring;
        const tests = await ai_service_1.aiService.createIntegrationTests(monitoring, this.state.input.requirements);
        this.state.results.tests = tests;
        return {
            results: tests,
            needsInput: false,
            completed: false
        };
    }
    async deployIntegrationWithMCP(state) {
        const tests = this.state.results.tests;
        const deployment = await ai_service_1.aiService.deployIntegration(tests);
        this.state.results.deployment = deployment;
        return {
            results: deployment,
            needsInput: false,
            completed: false
        };
    }
    async validateConnectivityWithMCP(state) {
        const deployment = this.state.results.deployment;
        const validation = await ai_service_1.aiService.validateIntegrationConnectivity(deployment, this.state.input.task);
        this.state.results.validation = validation;
        return {
            results: validation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.IntegrationAgent = IntegrationAgent;
exports.default = IntegrationAgent;
//# sourceMappingURL=integration-agent.js.map