import { PlanningMode, PlanningSession } from "@/components/planning-mode-selector"

export interface PlanningPreferences {
  defaultMode: PlanningMode
  autoSave: boolean
  interventionPoints: {
    requirementsReview: boolean
    designReview: boolean
    tasksReview: boolean
  }
  notifications: {
    phaseCompletion: boolean
    errorAlerts: boolean
    progressUpdates: boolean
  }
}

export interface PlanningWorkflowState {
  currentSession?: PlanningSession
  preferences: PlanningPreferences
  history: PlanningSession[]
}

export class PlanningSessionManager {
  private static instance: PlanningSessionManager
  private state: PlanningWorkflowState
  private listeners: Set<(state: PlanningWorkflowState) => void> = new Set()

  private constructor() {
    this.state = this.loadState()
  }

  static getInstance(): PlanningSessionManager {
    if (!PlanningSessionManager.instance) {
      PlanningSessionManager.instance = new PlanningSessionManager()
    }
    return PlanningSessionManager.instance
  }

  // State Management
  getState(): PlanningWorkflowState {
    return { ...this.state }
  }

  subscribe(listener: (state: PlanningWorkflowState) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.getState()))
  }

  private saveState(): void {
    if (typeof window !== "undefined") {
      localStorage.setItem("planning-session-state", JSON.stringify({
        ...this.state,
        currentSession: this.state.currentSession ? {
          ...this.state.currentSession,
          startedAt: this.state.currentSession.startedAt?.toISOString(),
          completedAt: this.state.currentSession.completedAt?.toISOString()
        } : undefined,
        history: this.state.history.map(session => ({
          ...session,
          startedAt: session.startedAt?.toISOString(),
          completedAt: session.completedAt?.toISOString()
        }))
      }))
    }
  }

  private loadState(): PlanningWorkflowState {
    if (typeof window === "undefined") {
      return this.getDefaultState()
    }

    try {
      const saved = localStorage.getItem("planning-session-state")
      if (saved) {
        const parsed = JSON.parse(saved)
        return {
          ...parsed,
          currentSession: parsed.currentSession ? {
            ...parsed.currentSession,
            startedAt: parsed.currentSession.startedAt ? new Date(parsed.currentSession.startedAt) : undefined,
            completedAt: parsed.currentSession.completedAt ? new Date(parsed.currentSession.completedAt) : undefined
          } : undefined,
          history: parsed.history?.map((session: any) => ({
            ...session,
            startedAt: session.startedAt ? new Date(session.startedAt) : undefined,
            completedAt: session.completedAt ? new Date(session.completedAt) : undefined
          })) || []
        }
      }
    } catch (error) {
      console.error("Failed to load planning session state:", error)
    }

    return this.getDefaultState()
  }

  private getDefaultState(): PlanningWorkflowState {
    return {
      preferences: {
        defaultMode: "copilot",
        autoSave: true,
        interventionPoints: {
          requirementsReview: true,
          designReview: true,
          tasksReview: true
        },
        notifications: {
          phaseCompletion: true,
          errorAlerts: true,
          progressUpdates: false
        }
      },
      history: []
    }
  }

  // Session Management
  createSession(mode: PlanningMode, projectName?: string): PlanningSession {
    const session: PlanningSession = {
      id: this.generateSessionId(),
      mode,
      status: "planning",
      progress: 0,
      currentPhase: "requirements",
      startedAt: new Date(),
      projectName
    }

    this.state.currentSession = session
    this.saveState()
    this.notifyListeners()

    return session
  }

  updateSession(updates: Partial<PlanningSession>): void {
    if (!this.state.currentSession) return

    this.state.currentSession = {
      ...this.state.currentSession,
      ...updates
    }

    this.saveState()
    this.notifyListeners()
  }

  pauseSession(): void {
    if (!this.state.currentSession) return

    this.updateSession({ status: "paused" })
  }

  resumeSession(): void {
    if (!this.state.currentSession) return

    this.updateSession({ status: "planning" })
  }

  completeSession(): void {
    if (!this.state.currentSession) return

    const completedSession = {
      ...this.state.currentSession,
      status: "completed" as const,
      progress: 100,
      currentPhase: "completed" as const,
      completedAt: new Date()
    }

    this.state.history.unshift(completedSession)
    this.state.currentSession = undefined

    this.saveState()
    this.notifyListeners()
  }

  stopSession(): void {
    if (!this.state.currentSession) return

    // Add to history if it has made some progress
    if (this.state.currentSession.progress > 0) {
      this.state.history.unshift({
        ...this.state.currentSession,
        status: "error",
        completedAt: new Date()
      })
    }

    this.state.currentSession = undefined
    this.saveState()
    this.notifyListeners()
  }

  // Phase Management
  advancePhase(): void {
    if (!this.state.currentSession) return

    const phases: Array<PlanningSession["currentPhase"]> = ["requirements", "design", "tasks", "completed"]
    const currentIndex = phases.indexOf(this.state.currentSession.currentPhase)
    
    if (currentIndex < phases.length - 1) {
      const nextPhase = phases[currentIndex + 1]
      const progress = ((currentIndex + 1) / (phases.length - 1)) * 100

      this.updateSession({
        currentPhase: nextPhase,
        progress: Math.round(progress)
      })

      if (nextPhase === "completed") {
        this.completeSession()
      }
    }
  }

  updatePhaseProgress(progress: number): void {
    if (!this.state.currentSession) return

    const phases = ["requirements", "design", "tasks", "completed"]
    const currentIndex = phases.indexOf(this.state.currentSession.currentPhase)
    const baseProgress = (currentIndex / (phases.length - 1)) * 100
    const phaseProgress = progress / (phases.length - 1)
    const totalProgress = Math.round(baseProgress + phaseProgress)

    this.updateSession({ progress: Math.min(totalProgress, 100) })
  }

  // Preferences Management
  updatePreferences(preferences: Partial<PlanningPreferences>): void {
    this.state.preferences = {
      ...this.state.preferences,
      ...preferences
    }

    this.saveState()
    this.notifyListeners()
  }

  getPreferences(): PlanningPreferences {
    return { ...this.state.preferences }
  }

  // Workflow Routing
  shouldShowInterventionPoint(phase: keyof PlanningPreferences["interventionPoints"]): boolean {
    if (!this.state.currentSession) return false
    
    // Autonomous mode skips intervention points unless explicitly enabled
    if (this.state.currentSession.mode === "autonomous") {
      return false
    }

    return this.state.preferences.interventionPoints[phase]
  }

  getWorkflowRoute(): string {
    if (!this.state.currentSession) return "/planning/setup"

    const { mode, currentPhase, status } = this.state.currentSession

    if (status === "completed") return "/planning/completed"
    if (status === "error") return "/planning/error"
    if (status === "paused") return "/planning/paused"

    // Route based on mode and phase
    const baseRoute = mode === "autonomous" ? "/planning/autonomous" : "/planning/copilot"
    return `${baseRoute}/${currentPhase}`
  }

  // Utility Methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getCurrentSession(): PlanningSession | undefined {
    return this.state.currentSession ? { ...this.state.currentSession } : undefined
  }

  getSessionHistory(): PlanningSession[] {
    return [...this.state.history]
  }

  clearHistory(): void {
    this.state.history = []
    this.saveState()
    this.notifyListeners()
  }

  // Error Handling
  handleSessionError(error: string): void {
    if (!this.state.currentSession) return

    this.updateSession({
      status: "error"
    })

    if (this.state.preferences.notifications.errorAlerts) {
      // Could integrate with toast notifications here
      console.error("Planning session error:", error)
    }
  }

  // Analytics and Metrics
  getSessionMetrics() {
    const totalSessions = this.state.history.length + (this.state.currentSession ? 1 : 0)
    const completedSessions = this.state.history.filter(s => s.status === "completed").length
    const autonomousSessions = this.state.history.filter(s => s.mode === "autonomous").length
    const copilotSessions = this.state.history.filter(s => s.mode === "copilot").length

    const avgCompletionTime = this.state.history
      .filter(s => s.status === "completed" && s.startedAt && s.completedAt)
      .reduce((acc, s) => {
        const duration = s.completedAt!.getTime() - s.startedAt!.getTime()
        return acc + duration
      }, 0) / completedSessions || 0

    return {
      totalSessions,
      completedSessions,
      completionRate: totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0,
      autonomousSessions,
      copilotSessions,
      avgCompletionTime: Math.round(avgCompletionTime / 1000 / 60), // minutes
      preferredMode: autonomousSessions > copilotSessions ? "autonomous" : "copilot"
    }
  }
}

// Export singleton instance
export const planningSessionManager = PlanningSessionManager.getInstance()