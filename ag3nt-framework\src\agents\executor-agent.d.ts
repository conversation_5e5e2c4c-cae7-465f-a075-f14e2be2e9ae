/**
 * AG3NT Framework - Executor Agent
 *
 * Specialized agent for executing individual tasks and coordinating with other agents.
 * Acts as the orchestrator for task execution and delegation.
 *
 * Features:
 * - Task execution coordination
 * - Agent delegation and handoffs
 * - Progress monitoring and reporting
 * - Error handling and recovery
 * - Quality assurance integration
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface ExecutorInput {
    task: ExecutableTask;
    context: ExecutionContext;
    resources: AvailableResources;
}
export interface ExecutableTask {
    taskId: string;
    title: string;
    description: string;
    type: 'development' | 'design' | 'testing' | 'deployment' | 'documentation';
    priority: 'critical' | 'high' | 'medium' | 'low';
    requirements: string[];
    acceptanceCriteria: string[];
    dependencies: string[];
    estimatedHours: number;
    assignedAgent?: string;
    deadline?: string;
}
export interface ExecutionContext {
    projectId: string;
    sessionId: string;
    previousResults: Record<string, any>;
    availableAgents: string[];
    constraints: ExecutionConstraints;
}
export interface ExecutionConstraints {
    timeLimit?: number;
    qualityThreshold?: number;
    resourceLimits?: Record<string, number>;
    complianceRequirements?: string[];
}
export interface AvailableResources {
    agents: AgentResource[];
    tools: ToolResource[];
    data: DataResource[];
}
export interface AgentResource {
    agentId: string;
    agentType: string;
    capabilities: string[];
    availability: number;
    currentLoad: number;
}
export interface ToolResource {
    toolId: string;
    toolType: string;
    capabilities: string[];
    available: boolean;
}
export interface DataResource {
    resourceId: string;
    resourceType: string;
    location: string;
    accessible: boolean;
}
export interface ExecutorResult {
    taskId: string;
    status: 'completed' | 'failed' | 'delegated' | 'in_progress';
    results: any;
    executionLog: ExecutionLogEntry[];
    qualityMetrics: QualityMetrics;
    resourceUsage: ResourceUsage;
    nextActions: NextAction[];
}
export interface ExecutionLogEntry {
    timestamp: string;
    action: string;
    agentId: string;
    details: any;
    status: 'success' | 'error' | 'warning' | 'info';
}
export interface QualityMetrics {
    completeness: number;
    accuracy: number;
    performance: number;
    maintainability: number;
    overallScore: number;
}
export interface ResourceUsage {
    timeSpent: number;
    agentsUsed: string[];
    toolsUsed: string[];
    costEstimate: number;
}
export interface NextAction {
    actionType: 'delegate' | 'review' | 'test' | 'deploy' | 'document';
    targetAgent: string;
    priority: number;
    description: string;
}
/**
 * Executor Agent - Task execution and coordination
 */
export declare class ExecutorAgent extends BaseAgent {
    private readonly executionSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute task execution workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual execution step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for task execution
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    /**
     * Log execution activity
     */
    private logExecution;
    private analyzeTaskWithMCP;
    private planExecutionWithMCP;
    private delegateOrExecuteWithMCP;
    private monitorProgressWithMCP;
    private qualityCheckWithMCP;
    private handleResultsWithMCP;
    private planNextActionsWithMCP;
    /**
     * Delegate task to specialized agent
     */
    private delegateTask;
    /**
     * Monitor delegated task progress
     */
    private monitorDelegation;
}
export { ExecutorAgent as default };
//# sourceMappingURL=executor-agent.d.ts.map