"use strict";
/**
 * AG3NT Framework - Maintenance Agent
 *
 * Specialized agent for handling upgrades, dependency management,
 * bug triage, and legacy refactorings.
 *
 * Features:
 * - Dependency management and updates
 * - Legacy code refactoring
 * - Bug triage and prioritization
 * - Technical debt management
 * - Performance optimization
 * - Code quality improvements
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.MaintenanceAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Maintenance Agent - Code maintenance and dependency management
 */
class MaintenanceAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('maintenance', {
            capabilities: {
                requiredCapabilities: [
                    'dependency_management',
                    'code_refactoring',
                    'bug_triage',
                    'debt_reduction',
                    'performance_optimization',
                    'legacy_modernization',
                    'quality_improvement'
                ],
                contextFilters: ['maintenance', 'dependencies', 'refactoring', 'debt', 'performance'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.maintenanceSteps = [
            'analyze_codebase', 'assess_dependencies', 'triage_issues',
            'plan_maintenance', 'update_dependencies', 'refactor_legacy',
            'optimize_performance', 'reduce_debt', 'validate_changes'
        ];
    }
    /**
     * Execute maintenance workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🔧 Starting maintenance workflow: ${input.task.title}`);
        // Execute maintenance steps sequentially
        for (const stepId of this.maintenanceSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Maintenance workflow completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual maintenance step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_codebase':
                return await this.analyzeCodebaseWithMCP(enhancedState, input);
            case 'assess_dependencies':
                return await this.assessDependenciesWithMCP(enhancedState);
            case 'triage_issues':
                return await this.triageIssuesWithMCP(enhancedState);
            case 'plan_maintenance':
                return await this.planMaintenanceWithMCP(enhancedState);
            case 'update_dependencies':
                return await this.updateDependenciesWithMCP(enhancedState);
            case 'refactor_legacy':
                return await this.refactorLegacyWithMCP(enhancedState);
            case 'optimize_performance':
                return await this.optimizePerformanceWithMCP(enhancedState);
            case 'reduce_debt':
                return await this.reduceDebtWithMCP(enhancedState);
            case 'validate_changes':
                return await this.validateChangesWithMCP(enhancedState);
            default:
                throw new Error(`Unknown maintenance step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.maintenanceSteps.length;
    }
    /**
     * Get relevant documentation for maintenance
     */
    async getRelevantDocumentation() {
        return {
            maintenance: 'Software maintenance best practices and strategies',
            dependencies: 'Dependency management and update strategies',
            refactoring: 'Code refactoring techniques and patterns',
            debt: 'Technical debt management and reduction',
            performance: 'Performance optimization and monitoring',
            legacy: 'Legacy code modernization and migration'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeCodebaseWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeCodebaseForMaintenance(input.codebase, input.task.scope);
        this.state.results.codebaseAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async assessDependenciesWithMCP(state) {
        const dependencies = this.state.input.dependencies;
        const assessment = await ai_service_1.aiService.assessDependencies(dependencies);
        this.state.results.dependencyAssessment = assessment;
        return {
            results: assessment,
            needsInput: false,
            completed: false
        };
    }
    async triageIssuesWithMCP(state) {
        const issues = this.state.input.issues;
        const triage = await ai_service_1.aiService.triageIssues(issues);
        this.state.results.triage = triage;
        return {
            results: triage,
            needsInput: false,
            completed: false
        };
    }
    async planMaintenanceWithMCP(state) {
        const allAnalysis = {
            codebase: this.state.results.codebaseAnalysis,
            dependencies: this.state.results.dependencyAssessment,
            triage: this.state.results.triage
        };
        const plan = await ai_service_1.aiService.planMaintenanceStrategy(allAnalysis);
        this.state.results.maintenancePlan = plan;
        return {
            results: plan,
            needsInput: false,
            completed: false
        };
    }
    async updateDependenciesWithMCP(state) {
        const plan = this.state.results.maintenancePlan;
        const updates = await ai_service_1.aiService.updateDependencies(plan.dependencies, this.state.input.dependencies);
        this.state.results.dependencyUpdates = updates;
        return {
            results: updates,
            needsInput: false,
            completed: false
        };
    }
    async refactorLegacyWithMCP(state) {
        const plan = this.state.results.maintenancePlan;
        const refactoring = await ai_service_1.aiService.refactorLegacyCode(plan.refactoring, this.state.input.codebase.legacy);
        this.state.results.refactoring = refactoring;
        return {
            results: refactoring,
            needsInput: false,
            completed: false
        };
    }
    async optimizePerformanceWithMCP(state) {
        const plan = this.state.results.maintenancePlan;
        const optimization = await ai_service_1.aiService.optimizeCodePerformance(plan.optimization, this.state.input.codebase.performance);
        this.state.results.optimization = optimization;
        return {
            results: optimization,
            needsInput: false,
            completed: false
        };
    }
    async reduceDebtWithMCP(state) {
        const plan = this.state.results.maintenancePlan;
        const debtReduction = await ai_service_1.aiService.reduceTechnicalDebt(plan.debt, this.state.input.codebase.debt);
        this.state.results.debtReduction = debtReduction;
        return {
            results: debtReduction,
            needsInput: false,
            completed: false
        };
    }
    async validateChangesWithMCP(state) {
        const allResults = {
            dependencies: this.state.results.dependencyUpdates,
            refactoring: this.state.results.refactoring,
            optimization: this.state.results.optimization,
            debt: this.state.results.debtReduction
        };
        const validation = await ai_service_1.aiService.validateMaintenanceChanges(allResults, this.state.input.task);
        this.state.results.validation = validation;
        return {
            results: validation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.MaintenanceAgent = MaintenanceAgent;
exports.default = MaintenanceAgent;
//# sourceMappingURL=maintenance-agent.js.map