#!/usr/bin/env node

/**
 * AG3NT Framework - Setup Script
 * 
 * Sets up the development environment
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Setting up AG3NT Framework development environment...')

try {
  // Check Node.js version
  console.log('🔍 Checking Node.js version...')
  const nodeVersion = process.version
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  
  if (majorVersion < 18) {
    throw new Error(`Node.js 18+ required, found ${nodeVersion}`)
  }
  console.log(`✅ Node.js ${nodeVersion} (compatible)`)

  // Install dependencies
  console.log('📦 Installing dependencies...')
  execSync('npm install', { stdio: 'inherit' })

  // Build the project
  console.log('🔨 Building project...')
  execSync('npm run build', { stdio: 'inherit' })

  // Run tests
  console.log('🧪 Running tests...')
  try {
    execSync('npm test', { stdio: 'inherit' })
  } catch (error) {
    console.warn('⚠️ Some tests failed, but setup continues...')
  }

  // Create .env template if it doesn't exist
  console.log('⚙️ Setting up configuration...')
  const envTemplate = `# AG3NT Framework Configuration

# Framework Features
AG3NT_ENABLE_MCP=true
AG3NT_ENABLE_COORDINATION=true
AG3NT_ENABLE_DISCOVERY=true
AG3NT_ENABLE_ANALYTICS=true

# Database Configuration (Optional)
# AG3NT_NEO4J_URI=bolt://localhost:7687
# AG3NT_NEO4J_USER=neo4j
# AG3NT_NEO4J_PASSWORD=password

# AG3NT_TEMPORAL_DB_URI=postgresql://localhost:5432/temporal
# AG3NT_REDIS_URI=redis://localhost:6379

# Performance Tuning
AG3NT_MAX_AGENTS=50
AG3NT_LOAD_BALANCE_ALGORITHM=adaptive
AG3NT_ENABLE_FAILOVER=true

# Logging
AG3NT_LOG_LEVEL=info
AG3NT_ENABLE_DEBUG=false
`

  if (!fs.existsSync('.env')) {
    fs.writeFileSync('.env', envTemplate)
    console.log('✅ Created .env configuration file')
  }

  console.log('\n🎉 Setup completed successfully!')
  console.log('\n📚 Next steps:')
  console.log('  1. Review .env configuration')
  console.log('  2. Run examples: npm run demo:master')
  console.log('  3. Read documentation in ./docs/')
  console.log('  4. Start developing with the framework!')

} catch (error) {
  console.error('❌ Setup failed:', error.message)
  process.exit(1)
}
