"use client"

import { useState, useEffect } from "react"
import { useChat } from '@ai-sdk/react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Brain,
  Zap,
  Code,
  FileText,
  Database,
  TestTube,
  CheckCircle,
  Clock,
  AlertCircle,
  Play,
  Pause,
  Square,
  MessageSquare,
  Settings,
  Sparkles,
} from "lucide-react"

interface Task {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  progress: number
  type: "code" | "file" | "database" | "test"
  estimatedTime?: string
}

interface AIAssistantProps {
  isActive?: boolean
  currentTask?: string
  tasks?: Task[]
  onStart?: () => void
  onPause?: () => void
  onStop?: () => void
  onTaskGenerated?: (tasks: Task[]) => void
  systemPrompt?: string
}

export default function AIAssistant({
  isActive = false,
  currentTask = "Analyzing project structure...",
  tasks = [],
  onStart,
  onPause,
  onStop,
  onTaskGenerated,
  systemPrompt = "You are an AI development assistant. Help analyze, plan, and execute development tasks. Provide structured responses with clear action items."
}: AIAssistantProps) {
  const [assistantState, setAssistantState] = useState<"idle" | "thinking" | "working">(isActive ? "working" : "idle")
  const [showChat, setShowChat] = useState(false)
  const [taskPrompt, setTaskPrompt] = useState("")
  const [selectedModel, setSelectedModel] = useState("openai:gpt-4o-mini")
  const [availableModels, setAvailableModels] = useState<Array<{
    provider: string
    model: string
    displayName: string
    value: string
  }>>([])

  // AI Chat for task generation and assistance
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isChatLoading,
    error: chatError,
  } = useChat({
    api: '/api/chat',
    body: {
      provider: selectedModel.split(':')[0],
      model: selectedModel.split(':')[1],
      system: systemPrompt,
    },
    onFinish: (message) => {
      // Try to extract tasks from AI response
      if (onTaskGenerated) {
        const extractedTasks = extractTasksFromResponse(message.content)
        if (extractedTasks.length > 0) {
          onTaskGenerated(extractedTasks)
        }
      }
    }
  })

  // Fetch available models
  useEffect(() => {
    fetch('/api/models')
      .then(res => res.json())
      .then(data => {
        if (data.models) {
          setAvailableModels(data.models)
        }
      })
      .catch(console.error)
  }, [])

  // Extract tasks from AI response
  const extractTasksFromResponse = (content: string): Task[] => {
    const tasks: Task[] = []
    const lines = content.split('\n')

    lines.forEach((line, index) => {
      // Look for task patterns like "1. Task name" or "- Task name"
      const taskMatch = line.match(/^[\d\-\*]\s*(.+)/)
      if (taskMatch) {
        const title = taskMatch[1].trim()
        if (title.length > 0) {
          tasks.push({
            id: `task-${Date.now()}-${index}`,
            title,
            description: `Generated from AI assistant`,
            status: "pending",
            progress: 0,
            type: determineTaskType(title),
          })
        }
      }
    })

    return tasks
  }

  const determineTaskType = (title: string): "code" | "file" | "database" | "test" => {
    const lowerTitle = title.toLowerCase()
    if (lowerTitle.includes('test') || lowerTitle.includes('spec')) return "test"
    if (lowerTitle.includes('database') || lowerTitle.includes('db') || lowerTitle.includes('sql')) return "database"
    if (lowerTitle.includes('file') || lowerTitle.includes('document') || lowerTitle.includes('readme')) return "file"
    return "code"
  }

  const getTaskIcon = (type: string) => {
    switch (type) {
      case "code":
        return <Code className="w-4 h-4" />
      case "file":
        return <FileText className="w-4 h-4" />
      case "database":
        return <Database className="w-4 h-4" />
      case "test":
        return <TestTube className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400"
      case "running":
        return "text-blue-400"
      case "error":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "running":
        return <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const completedTasks = tasks.filter((task) => task.status === "completed").length
  const totalProgress = tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0

  return (
    <Card className="bg-[#1a1a1a] border-[#333]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              {isActive && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                </div>
              )}
            </div>
            <div>
              <CardTitle className="text-white text-lg">AI Assistant</CardTitle>
              <p className="text-gray-400 text-sm">
                {assistantState === "idle" && "Ready to help"}
                {assistantState === "thinking" && "Processing your request..."}
                {assistantState === "working" && currentTask}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isActive ? "default" : "secondary"} className="bg-blue-500">
              {isActive ? "Active" : "Idle"}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Overview */}
        {tasks.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Overall Progress</span>
              <span className="text-white">{Math.round(totalProgress)}%</span>
            </div>
            <Progress value={totalProgress} className="h-2" />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>
                {completedTasks} of {tasks.length} tasks completed
              </span>
              <div className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                <span>AI-powered</span>
              </div>
            </div>
          </div>
        )}

        {/* Task List */}
        {tasks.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white">Current Tasks</h4>
            <ScrollArea className="h-48">
              <div className="space-y-2 pr-4">
                {tasks.slice(0, 5).map((task) => (
                <div
                  key={task.id}
                  className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                    task.status === "running" ? "bg-blue-500/10 border border-blue-500/20" : "bg-[#2a2a2a]"
                  }`}
                >
                  <div className="flex-shrink-0">{getStatusIcon(task.status)}</div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium truncate ${getStatusColor(task.status)}`}>{task.title}</p>
                    {task.description && <p className="text-xs text-gray-500 truncate">{task.description}</p>}
                    {task.status === "running" && task.progress > 0 && (
                      <div className="mt-1">
                        <Progress value={task.progress} className="h-1" />
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0 text-blue-400">{getTaskIcon(task.type)}</div>
                </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* AI Chat Interface */}
        {showChat && (
          <div className="space-y-3 pt-3 border-t border-[#333]">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-white flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                AI Assistant Chat
              </h4>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="w-32 h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model.value} value={model.value} className="text-xs">
                      {model.model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Chat Messages */}
            <ScrollArea className="h-32 bg-[#2a2a2a] rounded-lg p-2">
              <div className="space-y-2">
                {messages.slice(-3).map((message) => (
                  <div key={message.id} className={`text-xs ${message.role === 'user' ? 'text-blue-400' : 'text-gray-300'}`}>
                    <span className="font-medium">{message.role === 'user' ? 'You' : 'AI'}:</span> {message.content}
                  </div>
                ))}
                {isChatLoading && (
                  <div className="text-xs text-gray-400 flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    AI is thinking...
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="space-y-2">
              <Textarea
                value={input}
                onChange={handleInputChange}
                placeholder="Ask AI to help with tasks, analysis, or planning..."
                className="bg-[#2a2a2a] border-[#444] text-sm resize-none"
                rows={2}
                disabled={isChatLoading}
              />
              <Button
                type="submit"
                size="sm"
                className="w-full bg-blue-500 hover:bg-blue-600"
                disabled={!input.trim() || isChatLoading}
              >
                <Sparkles className="w-3 h-3 mr-1" />
                Send to AI
              </Button>
            </form>

            {chatError && (
              <div className="text-xs text-red-400 bg-red-900/20 p-2 rounded">
                Error: {chatError.message}
              </div>
            )}
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center gap-2 pt-2 border-t border-[#333]">
          <Button
            onClick={() => setShowChat(!showChat)}
            variant="outline"
            size="sm"
            className="bg-transparent"
          >
            <MessageSquare className="w-4 h-4 mr-1" />
            {showChat ? 'Hide' : 'Chat'}
          </Button>

          {!isActive ? (
            <Button onClick={onStart} className="flex-1 bg-blue-500 hover:bg-blue-600">
              <Play className="w-4 h-4 mr-2" />
              Start Assistant
            </Button>
          ) : (
            <>
              <Button onClick={onPause} variant="outline" className="flex-1 bg-transparent">
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
              <Button onClick={onStop} variant="outline" className="flex-1 bg-transparent">
                <Square className="w-4 h-4 mr-2" />
                Stop
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
