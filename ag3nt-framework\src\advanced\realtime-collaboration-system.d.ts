/**
 * AG3NT Framework - Real-time Agent Collaboration System
 *
 * Advanced collaboration system that enables multiple agents to work together
 * in real-time on the same task with conflict resolution and synchronization.
 *
 * Features:
 * - Real-time agent coordination
 * - Conflict detection and resolution
 * - Collaborative task execution
 * - Shared workspace management
 * - Live synchronization
 * - Consensus mechanisms
 */
import { EventEmitter } from "events";
export interface CollaborationConfig {
    maxConcurrentAgents: number;
    conflictResolutionStrategy: 'priority' | 'consensus' | 'merge' | 'sequential';
    syncInterval: number;
    timeoutDuration: number;
    enableRealTimeSync: boolean;
    consensusThreshold: number;
}
export interface CollaborativeSession {
    sessionId: string;
    taskId: string;
    participants: AgentParticipant[];
    workspace: SharedWorkspace;
    status: 'active' | 'paused' | 'completed' | 'failed';
    startTime: number;
    endTime?: number;
    metadata: SessionMetadata;
}
export interface AgentParticipant {
    agentId: string;
    agentType: string;
    role: 'leader' | 'contributor' | 'observer';
    priority: number;
    capabilities: string[];
    status: 'active' | 'idle' | 'busy' | 'disconnected';
    joinTime: number;
    lastActivity: number;
    contributions: Contribution[];
}
export interface Contribution {
    contributionId: string;
    agentId: string;
    type: 'code' | 'analysis' | 'suggestion' | 'review' | 'decision';
    content: any;
    timestamp: number;
    status: 'pending' | 'accepted' | 'rejected' | 'merged';
    conflicts: Conflict[];
    metadata: ContributionMetadata;
}
export interface ContributionMetadata {
    confidence: number;
    effort: number;
    impact: 'high' | 'medium' | 'low';
    dependencies: string[];
    reviewers: string[];
}
export interface Conflict {
    conflictId: string;
    type: 'resource' | 'logic' | 'priority' | 'timing';
    description: string;
    involvedAgents: string[];
    severity: 'critical' | 'high' | 'medium' | 'low';
    resolutionStrategy: string;
    status: 'detected' | 'resolving' | 'resolved' | 'escalated';
    resolution?: ConflictResolution;
}
export interface ConflictResolution {
    strategy: string;
    decision: any;
    decidedBy: string[];
    timestamp: number;
    rationale: string;
    alternatives: any[];
}
export interface SharedWorkspace {
    workspaceId: string;
    resources: WorkspaceResource[];
    locks: ResourceLock[];
    history: WorkspaceChange[];
    permissions: WorkspacePermission[];
    synchronization: SyncState;
}
export interface WorkspaceResource {
    resourceId: string;
    type: 'file' | 'data' | 'configuration' | 'state';
    content: any;
    version: number;
    lastModified: number;
    modifiedBy: string;
    checksum: string;
    metadata: ResourceMetadata;
}
export interface ResourceMetadata {
    size: number;
    encoding: string;
    mimeType: string;
    tags: string[];
    importance: number;
}
export interface ResourceLock {
    lockId: string;
    resourceId: string;
    agentId: string;
    lockType: 'read' | 'write' | 'exclusive';
    acquiredAt: number;
    expiresAt: number;
    reason: string;
}
export interface WorkspaceChange {
    changeId: string;
    resourceId: string;
    agentId: string;
    changeType: 'create' | 'update' | 'delete' | 'move' | 'copy';
    before: any;
    after: any;
    timestamp: number;
    description: string;
}
export interface WorkspacePermission {
    agentId: string;
    resourceId: string;
    permissions: ('read' | 'write' | 'delete' | 'lock')[];
    granted: number;
    grantedBy: string;
    expires?: number;
}
export interface SyncState {
    lastSync: number;
    syncVersion: number;
    pendingChanges: WorkspaceChange[];
    conflicts: SyncConflict[];
    status: 'synced' | 'syncing' | 'conflict' | 'error';
}
export interface SyncConflict {
    conflictId: string;
    resourceId: string;
    conflictingChanges: WorkspaceChange[];
    detectedAt: number;
    status: 'pending' | 'resolved';
    resolution?: any;
}
export interface SessionMetadata {
    createdBy: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    tags: string[];
    estimatedDuration: number;
    actualDuration?: number;
    successMetrics: SuccessMetric[];
}
export interface SuccessMetric {
    name: string;
    target: number;
    current: number;
    unit: string;
    trend: 'improving' | 'stable' | 'declining';
}
export interface CollaborationEvent {
    eventId: string;
    sessionId: string;
    type: 'agent_joined' | 'agent_left' | 'contribution_made' | 'conflict_detected' | 'conflict_resolved' | 'sync_completed';
    agentId: string;
    timestamp: number;
    data: any;
    metadata: EventMetadata;
}
export interface EventMetadata {
    source: string;
    severity: 'info' | 'warning' | 'error';
    category: string;
    correlationId?: string;
}
export interface ConsensusRequest {
    requestId: string;
    sessionId: string;
    topic: string;
    options: ConsensusOption[];
    requiredVotes: number;
    deadline: number;
    status: 'pending' | 'completed' | 'expired';
    votes: ConsensusVote[];
    result?: ConsensusResult;
}
export interface ConsensusOption {
    optionId: string;
    description: string;
    proposedBy: string;
    details: any;
    pros: string[];
    cons: string[];
}
export interface ConsensusVote {
    voteId: string;
    agentId: string;
    optionId: string;
    confidence: number;
    rationale: string;
    timestamp: number;
}
export interface ConsensusResult {
    winningOption: string;
    voteCount: number;
    confidence: number;
    unanimous: boolean;
    dissenting: string[];
    timestamp: number;
}
export interface CollaborationMetrics {
    sessionId: string;
    duration: number;
    participantCount: number;
    contributionCount: number;
    conflictCount: number;
    resolutionTime: number;
    efficiency: number;
    satisfaction: number;
    qualityScore: number;
}
/**
 * Real-time Agent Collaboration System
 */
export declare class RealtimeCollaborationSystem extends EventEmitter {
    private config;
    private activeSessions;
    private agentConnections;
    private consensusRequests;
    private eventHistory;
    private syncTimer?;
    constructor(config?: Partial<CollaborationConfig>);
    /**
     * Initialize collaboration system
     */
    private initialize;
    /**
     * Create collaborative session
     */
    createSession(taskId: string, createdBy: string, metadata?: Partial<SessionMetadata>): Promise<CollaborativeSession>;
    /**
     * Join collaborative session
     */
    joinSession(sessionId: string, agentId: string, agentType: string, capabilities: string[]): Promise<AgentParticipant>;
    /**
     * Make contribution to session
     */
    makeContribution(sessionId: string, agentId: string, contribution: Partial<Contribution>): Promise<Contribution>;
    /**
     * Request consensus on decision
     */
    requestConsensus(sessionId: string, requesterId: string, topic: string, options: ConsensusOption[], deadline?: number): Promise<ConsensusRequest>;
    /**
     * Vote on consensus
     */
    vote(requestId: string, agentId: string, optionId: string, confidence: number, rationale: string): Promise<ConsensusVote>;
    /**
     * Synchronize workspace
     */
    synchronizeWorkspace(sessionId: string): Promise<void>;
    /**
     * Get session metrics
     */
    getSessionMetrics(sessionId: string): CollaborationMetrics | null;
    /**
     * Private helper methods
     */
    private startSyncTimer;
    private calculateAgentPriority;
    private detectConflicts;
    private handleConflicts;
    private resolvePriorityConflict;
    private resolveConsensusConflict;
    private resolveMergeConflict;
    private resolveSequentialConflict;
    private resolveConsensus;
    private applyContribution;
    private applyWorkspaceChange;
    private resolveSyncConflict;
    private grantWorkspacePermissions;
    private notifyAgent;
    private emitEvent;
    private calculateChecksum;
    private calculateAverageResolutionTime;
    private calculateEfficiency;
    private calculateSatisfaction;
    private calculateQualityScore;
    /**
     * Shutdown collaboration system
     */
    shutdown(): Promise<void>;
}
export default RealtimeCollaborationSystem;
//# sourceMappingURL=realtime-collaboration-system.d.ts.map