import { tool } from 'ai'
import { z } from 'zod'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs/promises'
import path from 'path'

const execAsync = promisify(exec)

// File System Tools
export const fileSystemTools = {
  readFile: tool({
    description: 'Read the contents of a file',
    parameters: z.object({
      filePath: z.string().describe('Path to the file to read'),
    }),
    execute: async ({ filePath }) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8')
        return { success: true, content }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },
  }),

  writeFile: tool({
    description: 'Write content to a file',
    parameters: z.object({
      filePath: z.string().describe('Path to the file to write'),
      content: z.string().describe('Content to write to the file'),
    }),
    execute: async ({ filePath, content }) => {
      try {
        await fs.writeFile(filePath, content, 'utf-8')
        return { success: true, message: `File written to ${filePath}` }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },
  }),

  listDirectory: tool({
    description: 'List contents of a directory',
    parameters: z.object({
      dirPath: z.string().describe('Path to the directory to list'),
    }),
    execute: async ({ dirPath }) => {
      try {
        const items = await fs.readdir(dirPath, { withFileTypes: true })
        const result = items.map(item => ({
          name: item.name,
          type: item.isDirectory() ? 'directory' : 'file',
          path: path.join(dirPath, item.name)
        }))
        return { success: true, items: result }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },
  }),
}

// Code Execution Tools
export const executionTools = {
  runCommand: tool({
    description: 'Execute a shell command',
    parameters: z.object({
      command: z.string().describe('Command to execute'),
      workingDir: z.string().optional().describe('Working directory for the command'),
    }),
    execute: async ({ command, workingDir }) => {
      try {
        const { stdout, stderr } = await execAsync(command, {
          cwd: workingDir || process.cwd(),
          timeout: 30000, // 30 second timeout
        })
        return { 
          success: true, 
          stdout: stdout.trim(), 
          stderr: stderr.trim() 
        }
      } catch (error) {
        return { 
          success: false, 
          error: error.message,
          stdout: error.stdout?.trim() || '',
          stderr: error.stderr?.trim() || ''
        }
      }
    },
  }),

  installPackage: tool({
    description: 'Install an npm package',
    parameters: z.object({
      packageName: z.string().describe('Name of the package to install'),
      isDev: z.boolean().optional().describe('Install as dev dependency'),
    }),
    execute: async ({ packageName, isDev = false }) => {
      const command = `npm install ${isDev ? '--save-dev' : ''} ${packageName}`
      try {
        const { stdout, stderr } = await execAsync(command)
        return { 
          success: true, 
          message: `Package ${packageName} installed successfully`,
          output: stdout.trim()
        }
      } catch (error) {
        return { 
          success: false, 
          error: `Failed to install ${packageName}: ${error.message}`
        }
      }
    },
  }),
}

// Web Tools
export const webTools = {
  fetchUrl: tool({
    description: 'Fetch content from a URL',
    parameters: z.object({
      url: z.string().describe('URL to fetch'),
    }),
    execute: async ({ url }) => {
      try {
        const response = await fetch(url)
        const content = await response.text()
        return { 
          success: true, 
          content,
          status: response.status,
          headers: Object.fromEntries(response.headers.entries())
        }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },
  }),
}

// Task Management Tools
export const taskTools = {
  createTask: tool({
    description: 'Create a new task',
    parameters: z.object({
      title: z.string().describe('Task title'),
      description: z.string().describe('Task description'),
      priority: z.enum(['low', 'medium', 'high']).optional(),
    }),
    execute: async ({ title, description, priority = 'medium' }) => {
      const task = {
        id: `task-${Date.now()}`,
        title,
        description,
        priority,
        status: 'pending',
        createdAt: new Date().toISOString(),
      }
      // In a real implementation, you'd save this to a database
      return { success: true, task }
    },
  }),
}

// Combine all tools
export const agentTools = {
  ...fileSystemTools,
  ...executionTools,
  ...webTools,
  ...taskTools,
}
