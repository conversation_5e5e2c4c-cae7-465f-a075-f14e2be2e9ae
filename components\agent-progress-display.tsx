"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { 
  Bot, 
  Brain, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  FileText,
  Lightbulb,
  ListTodo,
  MessageSquare,
  Cpu,
  Activity
} from "lucide-react"

interface AgentThought {
  id: string
  timestamp: Date
  agent: 'requirements' | 'design' | 'tasks'
  type: 'thinking' | 'analyzing' | 'generating' | 'validating' | 'completed'
  message: string
  progress?: number
  metadata?: {
    confidence?: number
    complexity?: 'low' | 'medium' | 'high'
    dependencies?: string[]
  }
}

interface AgentProgressDisplayProps {
  isActive: boolean
  currentPhase: 'requirements' | 'design' | 'tasks' | 'completed'
  className?: string
}

export default function AgentProgressDisplay({ 
  isActive, 
  currentPhase, 
  className = "" 
}: AgentProgressDisplayProps) {
  const [thoughts, setThoughts] = useState<AgentThought[]>([])
  const [currentProgress, setCurrentProgress] = useState(0)
  const [activeAgent, setActiveAgent] = useState<string>('')

  // Simulate agent thinking process
  useEffect(() => {
    if (!isActive) return

    const interval = setInterval(() => {
      generateAgentThought()
    }, 1500 + Math.random() * 2000) // Random interval between 1.5-3.5 seconds

    return () => clearInterval(interval)
  }, [isActive, currentPhase])

  const generateAgentThought = () => {
    // Don't generate thoughts if phase is completed
    if (currentPhase === 'completed') return
    
    const agentThoughts = {
      requirements: [
        {
          type: 'thinking' as const,
          messages: [
            "Analyzing user input and project context...",
            "Identifying key stakeholders and user personas...",
            "Considering functional and non-functional requirements...",
            "Evaluating business rules and constraints...",
            "Mapping user journeys and workflows..."
          ]
        },
        {
          type: 'analyzing' as const,
          messages: [
            "Breaking down complex requirements into user stories...",
            "Validating requirement completeness and consistency...",
            "Checking for conflicting or ambiguous requirements...",
            "Prioritizing requirements by business value...",
            "Estimating complexity and effort for each requirement..."
          ]
        },
        {
          type: 'generating' as const,
          messages: [
            "Crafting detailed user stories with acceptance criteria...",
            "Defining clear success metrics and KPIs...",
            "Creating requirement traceability matrix...",
            "Generating edge cases and error scenarios...",
            "Documenting assumptions and dependencies..."
          ]
        },
        {
          type: 'validating' as const,
          messages: [
            "Cross-referencing requirements with industry standards...",
            "Validating technical feasibility and constraints...",
            "Ensuring requirements align with project goals...",
            "Checking for missing or incomplete requirements...",
            "Reviewing requirement quality and clarity..."
          ]
        }
      ],
      design: [
        {
          type: 'thinking' as const,
          messages: [
            "Analyzing requirements to understand system needs...",
            "Considering architectural patterns and best practices...",
            "Evaluating technology stack options...",
            "Planning system boundaries and interfaces...",
            "Thinking about scalability and performance requirements..."
          ]
        },
        {
          type: 'analyzing' as const,
          messages: [
            "Decomposing system into logical components...",
            "Identifying data flows and dependencies...",
            "Analyzing integration points and APIs...",
            "Evaluating security and compliance requirements...",
            "Assessing performance and scalability needs..."
          ]
        },
        {
          type: 'generating' as const,
          messages: [
            "Creating system architecture diagrams...",
            "Designing database schemas and data models...",
            "Defining API contracts and interfaces...",
            "Planning component interactions and workflows...",
            "Documenting design decisions and rationale..."
          ]
        },
        {
          type: 'validating' as const,
          messages: [
            "Validating design against requirements...",
            "Checking architectural consistency and coherence...",
            "Reviewing design for potential bottlenecks...",
            "Ensuring design follows established patterns...",
            "Validating technical feasibility and constraints..."
          ]
        }
      ],
      tasks: [
        {
          type: 'thinking' as const,
          messages: [
            "Breaking down design into implementable tasks...",
            "Considering development workflow and dependencies...",
            "Planning task sequencing and prioritization...",
            "Thinking about resource allocation and timeline...",
            "Evaluating risk factors and mitigation strategies..."
          ]
        },
        {
          type: 'analyzing' as const,
          messages: [
            "Analyzing task complexity and effort estimation...",
            "Identifying critical path and dependencies...",
            "Evaluating team skills and capacity...",
            "Assessing technical risks and challenges...",
            "Planning testing and quality assurance tasks..."
          ]
        },
        {
          type: 'generating' as const,
          messages: [
            "Creating detailed implementation tasks...",
            "Defining task acceptance criteria and DoD...",
            "Organizing tasks into sprints and milestones...",
            "Generating task dependencies and relationships...",
            "Creating development and testing checklists..."
          ]
        },
        {
          type: 'validating' as const,
          messages: [
            "Validating task completeness and coverage...",
            "Checking task dependencies and sequencing...",
            "Ensuring tasks align with design and requirements...",
            "Reviewing effort estimates and timeline...",
            "Validating task clarity and actionability..."
          ]
        }
      ]
    }

    const phaseThoughts = agentThoughts[currentPhase]
    const thoughtCategory = phaseThoughts[Math.floor(Math.random() * phaseThoughts.length)]
    const message = thoughtCategory.messages[Math.floor(Math.random() * thoughtCategory.messages.length)]

    const newThought: AgentThought = {
      id: `thought-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      agent: currentPhase,
      type: thoughtCategory.type,
      message,
      progress: Math.min(currentProgress + Math.random() * 15, 95),
      metadata: {
        confidence: Math.random() * 0.4 + 0.6, // 60-100%
        complexity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
        dependencies: Math.random() > 0.7 ? ['previous-task', 'external-api'] : undefined
      }
    }

    setThoughts(prev => [newThought, ...prev.slice(0, 19)]) // Keep last 20 thoughts
    setCurrentProgress(newThought.progress || 0)
    setActiveAgent(`${currentPhase}-agent`)

    // Simulate completion
    if (newThought.progress && newThought.progress > 90) {
      setTimeout(() => {
        const completionThought: AgentThought = {
          id: `completion-${Date.now()}`,
          timestamp: new Date(),
          agent: currentPhase,
          type: 'completed',
          message: `${currentPhase.charAt(0).toUpperCase() + currentPhase.slice(1)} phase completed successfully!`,
          progress: 100,
          metadata: {
            confidence: 0.95,
            complexity: 'high'
          }
        }
        setThoughts(prev => [completionThought, ...prev.slice(0, 19)])
        setCurrentProgress(100)
      }, 2000)
    }
  }

  const getAgentIcon = (agent: string) => {
    switch (agent) {
      case 'requirements':
        return <FileText className="w-4 h-4" />
      case 'design':
        return <Lightbulb className="w-4 h-4" />
      case 'tasks':
        return <ListTodo className="w-4 h-4" />
      default:
        return <Bot className="w-4 h-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'thinking':
        return <Brain className="w-3 h-3 text-blue-400" />
      case 'analyzing':
        return <Activity className="w-3 h-3 text-yellow-400" />
      case 'generating':
        return <Zap className="w-3 h-3 text-green-400" />
      case 'validating':
        return <CheckCircle className="w-3 h-3 text-purple-400" />
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-500" />
      default:
        return <AlertCircle className="w-3 h-3 text-gray-400" />
    }
  }

  const getComplexityColor = (complexity?: string) => {
    switch (complexity) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  if (!isActive) {
    return (
      <Card className={`bg-[#0a0a0a] border-[#1a1a1a] ${className}`}>
        <CardContent className="p-6 text-center">
          <Bot className="w-8 h-8 mx-auto mb-3 text-gray-400" />
          <p className="text-gray-400 text-sm">AI agents are idle</p>
          <p className="text-gray-500 text-xs mt-1">Start a planning session to see agents at work</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`bg-[#0a0a0a] border-[#1a1a1a] ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Bot className="w-5 h-5 text-blue-400" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse" />
            </div>
            <div>
              <CardTitle className="text-white text-sm">AI Agent Activity</CardTitle>
              <CardDescription className="text-xs">
                {currentPhase.charAt(0).toUpperCase() + currentPhase.slice(1)} Agent Working
              </CardDescription>
            </div>
          </div>
          <Badge className="bg-blue-600/20 text-blue-400 border-blue-600/30 text-xs">
            Active
          </Badge>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-gray-400">Progress</span>
            <span className="text-white">{Math.round(currentProgress)}%</span>
          </div>
          <Progress value={currentProgress} className="h-1.5 bg-[#1a1a1a]" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <ScrollArea className="h-64">
          <div className="space-y-2">
            {thoughts.map((thought) => (
              <div 
                key={thought.id} 
                className={`p-2 rounded-lg border transition-all duration-300 ${
                  thought.type === 'completed' 
                    ? 'bg-green-900/20 border-green-600/30' 
                    : 'bg-[#111111] border-[#1a1a1a] hover:border-[#2a2a2a]'
                }`}
              >
                <div className="flex items-start gap-2">
                  <div className="flex items-center gap-1 mt-0.5">
                    {getAgentIcon(thought.agent)}
                    {getTypeIcon(thought.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs px-1 py-0 capitalize">
                          {thought.type}
                        </Badge>
                        {thought.metadata?.complexity && (
                          <span className={`text-xs ${getComplexityColor(thought.metadata.complexity)}`}>
                            {thought.metadata.complexity}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {thought.timestamp.toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit',
                          second: '2-digit'
                        })}
                      </span>
                    </div>
                    <p className="text-xs text-gray-300 leading-relaxed">
                      {thought.message}
                    </p>
                    
                    {/* Metadata */}
                    {thought.metadata && (
                      <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                        {thought.metadata.confidence && (
                          <div className="flex items-center gap-1">
                            <Cpu className="w-3 h-3" />
                            <span>{Math.round(thought.metadata.confidence * 100)}% confidence</span>
                          </div>
                        )}
                        {thought.progress && (
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{Math.round(thought.progress)}% complete</span>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Dependencies */}
                    {thought.metadata?.dependencies && (
                      <div className="flex items-center gap-1 mt-1">
                        <span className="text-xs text-gray-500">Dependencies:</span>
                        {thought.metadata.dependencies.map((dep, index) => (
                          <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                            {dep}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {thoughts.length === 0 && (
              <div className="text-center py-6 text-gray-400">
                <MessageSquare className="w-6 h-6 mx-auto mb-2" />
                <p className="text-sm">Waiting for agent activity...</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}