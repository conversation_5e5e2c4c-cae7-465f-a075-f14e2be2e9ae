"use strict";
/**
 * AG3NT Framework - DevOps Agent
 *
 * Specialized agent for CI/CD pipeline tasks, deployment automation,
 * and environment configuration management.
 *
 * Features:
 * - CI/CD pipeline creation and management
 * - Deployment automation
 * - Environment configuration
 * - Infrastructure as Code
 * - Monitoring and alerting setup
 * - Container orchestration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.DevOpsAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * DevOps Agent - CI/CD and infrastructure automation
 */
class DevOpsAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('devops', {
            capabilities: {
                requiredCapabilities: [
                    'pipeline_creation',
                    'infrastructure_automation',
                    'deployment_automation',
                    'monitoring_setup',
                    'security_configuration',
                    'container_orchestration',
                    'cloud_management'
                ],
                contextFilters: ['devops', 'infrastructure', 'deployment', 'monitoring', 'security'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.devopsSteps = [
            'analyze_project', 'assess_infrastructure', 'design_pipeline',
            'create_infrastructure', 'setup_monitoring', 'configure_security',
            'implement_deployment', 'setup_automation', 'validate_setup', 'create_documentation'
        ];
    }
    /**
     * Execute DevOps workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🚀 Starting DevOps workflow: ${input.task.title}`);
        // Execute DevOps steps sequentially
        for (const stepId of this.devopsSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ DevOps workflow completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual DevOps step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_project':
                return await this.analyzeProjectWithMCP(enhancedState, input);
            case 'assess_infrastructure':
                return await this.assessInfrastructureWithMCP(enhancedState);
            case 'design_pipeline':
                return await this.designPipelineWithMCP(enhancedState);
            case 'create_infrastructure':
                return await this.createInfrastructureWithMCP(enhancedState);
            case 'setup_monitoring':
                return await this.setupMonitoringWithMCP(enhancedState);
            case 'configure_security':
                return await this.configureSecurityWithMCP(enhancedState);
            case 'implement_deployment':
                return await this.implementDeploymentWithMCP(enhancedState);
            case 'setup_automation':
                return await this.setupAutomationWithMCP(enhancedState);
            case 'validate_setup':
                return await this.validateSetupWithMCP(enhancedState);
            case 'create_documentation':
                return await this.createDocumentationWithMCP(enhancedState);
            default:
                throw new Error(`Unknown DevOps step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.devopsSteps.length;
    }
    /**
     * Get relevant documentation for DevOps
     */
    async getRelevantDocumentation() {
        return {
            devops: 'DevOps practices and CI/CD pipeline best practices',
            infrastructure: 'Infrastructure as Code and cloud automation',
            deployment: 'Deployment strategies and automation patterns',
            monitoring: 'Application and infrastructure monitoring',
            security: 'DevSecOps and security automation practices',
            containerization: 'Container orchestration and management'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeProjectWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeProjectForDevOps(input.project, input.task.scope);
        this.state.results.projectAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async assessInfrastructureWithMCP(state) {
        const infrastructure = this.state.input.infrastructure;
        const assessment = await ai_service_1.aiService.assessInfrastructure(infrastructure);
        this.state.results.infrastructureAssessment = assessment;
        return {
            results: assessment,
            needsInput: false,
            completed: false
        };
    }
    async designPipelineWithMCP(state) {
        const projectAnalysis = this.state.results.projectAnalysis;
        const pipeline = await ai_service_1.aiService.designCIPipeline(projectAnalysis, this.state.input.requirements.pipeline);
        this.state.results.pipeline = pipeline;
        return {
            results: pipeline,
            needsInput: false,
            completed: false
        };
    }
    async createInfrastructureWithMCP(state) {
        const assessment = this.state.results.infrastructureAssessment;
        const infrastructure = await ai_service_1.aiService.createInfrastructureAsCode(assessment, this.state.input.infrastructure.target);
        this.state.results.infrastructure = infrastructure;
        return {
            results: infrastructure,
            needsInput: false,
            completed: false
        };
    }
    async setupMonitoringWithMCP(state) {
        const infrastructure = this.state.results.infrastructure;
        const monitoring = await ai_service_1.aiService.setupMonitoring(infrastructure, this.state.input.requirements.monitoring);
        this.state.results.monitoring = monitoring;
        return {
            results: monitoring,
            needsInput: false,
            completed: false
        };
    }
    async configureSecurityWithMCP(state) {
        const infrastructure = this.state.results.infrastructure;
        const security = await ai_service_1.aiService.configureDevOpsSecurity(infrastructure, this.state.input.requirements.security);
        this.state.results.security = security;
        return {
            results: security,
            needsInput: false,
            completed: false
        };
    }
    async implementDeploymentWithMCP(state) {
        const pipeline = this.state.results.pipeline;
        const infrastructure = this.state.results.infrastructure;
        const deployment = await ai_service_1.aiService.implementDeploymentStrategy(pipeline, infrastructure, this.state.input.requirements.deployment);
        this.state.results.deployment = deployment;
        return {
            results: deployment,
            needsInput: false,
            completed: false
        };
    }
    async setupAutomationWithMCP(state) {
        const allResults = {
            pipeline: this.state.results.pipeline,
            infrastructure: this.state.results.infrastructure,
            monitoring: this.state.results.monitoring,
            security: this.state.results.security,
            deployment: this.state.results.deployment
        };
        const automation = await ai_service_1.aiService.setupDevOpsAutomation(allResults);
        this.state.results.automation = automation;
        return {
            results: automation,
            needsInput: false,
            completed: false
        };
    }
    async validateSetupWithMCP(state) {
        const allResults = this.state.results;
        const validation = await ai_service_1.aiService.validateDevOpsSetup(allResults, this.state.input.requirements);
        this.state.results.validation = validation;
        return {
            results: validation,
            needsInput: false,
            completed: false
        };
    }
    async createDocumentationWithMCP(state) {
        const allResults = this.state.results;
        const documentation = await ai_service_1.aiService.createDevOpsDocumentation(allResults, this.state.input.task);
        this.state.results.documentation = documentation;
        return {
            results: documentation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.DevOpsAgent = DevOpsAgent;
exports.default = DevOpsAgent;
//# sourceMappingURL=devops-agent.js.map