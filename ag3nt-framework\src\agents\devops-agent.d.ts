/**
 * AG3NT Framework - DevOps Agent
 *
 * Specialized agent for CI/CD pipeline tasks, deployment automation,
 * and environment configuration management.
 *
 * Features:
 * - CI/CD pipeline creation and management
 * - Deployment automation
 * - Environment configuration
 * - Infrastructure as Code
 * - Monitoring and alerting setup
 * - Container orchestration
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface DevOpsInput {
    task: DevOpsTask;
    project: ProjectInfo;
    infrastructure: InfrastructureInfo;
    requirements: DevOpsRequirements;
}
export interface DevOpsTask {
    taskId: string;
    type: 'pipeline' | 'deployment' | 'infrastructure' | 'monitoring' | 'security' | 'optimization';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    scope: DevOpsScope;
    deadline?: string;
}
export interface DevOpsScope {
    environments: string[];
    services: string[];
    regions: string[];
    platforms: string[];
    includeMonitoring: boolean;
    includeSecurity: boolean;
    includeBackup: boolean;
}
export interface ProjectInfo {
    name: string;
    type: 'web_app' | 'api' | 'microservices' | 'mobile_app' | 'desktop_app';
    language: string;
    framework: string;
    buildTool: string;
    testFramework: string;
    dependencies: ProjectDependency[];
    structure: ProjectStructure;
}
export interface ProjectDependency {
    name: string;
    version: string;
    type: 'runtime' | 'build' | 'test';
    manager: 'npm' | 'yarn' | 'pip' | 'maven' | 'gradle' | 'cargo';
}
export interface ProjectStructure {
    sourceDir: string;
    buildDir: string;
    testDir: string;
    configDir: string;
    dockerFile?: string;
    buildScript?: string;
}
export interface InfrastructureInfo {
    current: CurrentInfrastructure;
    target: TargetInfrastructure;
    constraints: InfrastructureConstraints;
}
export interface CurrentInfrastructure {
    provider: 'aws' | 'azure' | 'gcp' | 'digitalocean' | 'heroku' | 'vercel' | 'netlify';
    services: InfrastructureService[];
    environments: Environment[];
    monitoring: MonitoringSetup;
    security: SecuritySetup;
}
export interface TargetInfrastructure {
    provider: 'aws' | 'azure' | 'gcp' | 'digitalocean' | 'heroku' | 'vercel' | 'netlify';
    architecture: 'monolith' | 'microservices' | 'serverless' | 'hybrid';
    services: TargetService[];
    scaling: ScalingStrategy;
    backup: BackupStrategy;
}
export interface InfrastructureService {
    name: string;
    type: 'compute' | 'database' | 'storage' | 'network' | 'monitoring' | 'security';
    provider: string;
    configuration: any;
    status: 'active' | 'inactive' | 'deprecated';
}
export interface TargetService {
    name: string;
    type: 'compute' | 'database' | 'storage' | 'network' | 'monitoring' | 'security';
    specifications: ServiceSpecification;
    dependencies: string[];
}
export interface ServiceSpecification {
    cpu: string;
    memory: string;
    storage: string;
    network: string;
    replicas: number;
    autoScaling: boolean;
}
export interface Environment {
    name: string;
    type: 'development' | 'staging' | 'production' | 'testing';
    url: string;
    configuration: EnvironmentConfig;
    deployment: DeploymentConfig;
}
export interface EnvironmentConfig {
    variables: Record<string, string>;
    secrets: Record<string, string>;
    resources: ResourceConfig;
    networking: NetworkConfig;
}
export interface ResourceConfig {
    cpu: string;
    memory: string;
    storage: string;
    instances: number;
}
export interface NetworkConfig {
    vpc: string;
    subnets: string[];
    securityGroups: string[];
    loadBalancer: boolean;
}
export interface DeploymentConfig {
    strategy: 'rolling' | 'blue_green' | 'canary' | 'recreate';
    automation: boolean;
    rollback: boolean;
    healthChecks: HealthCheck[];
}
export interface HealthCheck {
    type: 'http' | 'tcp' | 'command';
    endpoint?: string;
    port?: number;
    command?: string;
    interval: number;
    timeout: number;
    retries: number;
}
export interface MonitoringSetup {
    tools: MonitoringTool[];
    metrics: MetricConfig[];
    alerts: AlertConfig[];
    dashboards: DashboardConfig[];
}
export interface MonitoringTool {
    name: string;
    type: 'metrics' | 'logs' | 'traces' | 'uptime' | 'performance';
    configuration: any;
    enabled: boolean;
}
export interface MetricConfig {
    name: string;
    type: 'counter' | 'gauge' | 'histogram' | 'summary';
    description: string;
    labels: string[];
    threshold: number;
}
export interface AlertConfig {
    name: string;
    condition: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    channels: string[];
    escalation: EscalationRule[];
}
export interface EscalationRule {
    delay: number;
    channels: string[];
    condition: string;
}
export interface DashboardConfig {
    name: string;
    description: string;
    panels: DashboardPanel[];
    refresh: number;
}
export interface DashboardPanel {
    title: string;
    type: 'graph' | 'table' | 'stat' | 'gauge' | 'heatmap';
    query: string;
    visualization: any;
}
export interface SecuritySetup {
    authentication: AuthenticationSetup;
    authorization: AuthorizationSetup;
    encryption: EncryptionSetup;
    compliance: ComplianceSetup;
    scanning: SecurityScanning;
}
export interface AuthenticationSetup {
    provider: string;
    methods: string[];
    configuration: any;
    mfa: boolean;
}
export interface AuthorizationSetup {
    model: 'rbac' | 'abac' | 'acl';
    policies: PolicyConfig[];
    roles: RoleConfig[];
}
export interface PolicyConfig {
    name: string;
    rules: string[];
    resources: string[];
    actions: string[];
}
export interface RoleConfig {
    name: string;
    permissions: string[];
    policies: string[];
}
export interface EncryptionSetup {
    atRest: boolean;
    inTransit: boolean;
    keyManagement: string;
    algorithms: string[];
}
export interface ComplianceSetup {
    standards: string[];
    auditing: boolean;
    reporting: boolean;
    retention: number;
}
export interface SecurityScanning {
    vulnerability: boolean;
    dependency: boolean;
    container: boolean;
    infrastructure: boolean;
    frequency: string;
}
export interface ScalingStrategy {
    type: 'horizontal' | 'vertical' | 'auto';
    triggers: ScalingTrigger[];
    limits: ScalingLimits;
}
export interface ScalingTrigger {
    metric: string;
    threshold: number;
    action: 'scale_up' | 'scale_down';
    cooldown: number;
}
export interface ScalingLimits {
    minInstances: number;
    maxInstances: number;
    maxCpu: string;
    maxMemory: string;
}
export interface BackupStrategy {
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
    retention: number;
    location: 'local' | 'cloud' | 'both';
    encryption: boolean;
    testing: boolean;
}
export interface InfrastructureConstraints {
    budget: BudgetConstraints;
    compliance: ComplianceConstraints;
    performance: PerformanceConstraints;
    availability: AvailabilityConstraints;
}
export interface BudgetConstraints {
    monthly: number;
    annual: number;
    currency: string;
    alerts: number[];
}
export interface ComplianceConstraints {
    standards: string[];
    regions: string[];
    dataResidency: boolean;
    auditing: boolean;
}
export interface PerformanceConstraints {
    latency: number;
    throughput: number;
    availability: number;
    durability: number;
}
export interface AvailabilityConstraints {
    uptime: number;
    rto: number;
    rpo: number;
    regions: number;
}
export interface DevOpsRequirements {
    pipeline: PipelineRequirements;
    deployment: DeploymentRequirements;
    monitoring: MonitoringRequirements;
    security: SecurityRequirements;
    compliance: ComplianceRequirements;
}
export interface PipelineRequirements {
    stages: PipelineStage[];
    triggers: PipelineTrigger[];
    notifications: NotificationConfig[];
    artifacts: ArtifactConfig;
    testing: TestingConfig;
}
export interface PipelineStage {
    name: string;
    type: 'build' | 'test' | 'security' | 'deploy' | 'verify';
    dependencies: string[];
    parallel: boolean;
    timeout: number;
}
export interface PipelineTrigger {
    type: 'push' | 'pull_request' | 'schedule' | 'manual' | 'webhook';
    branches: string[];
    schedule?: string;
    conditions: string[];
}
export interface NotificationConfig {
    channel: 'email' | 'slack' | 'teams' | 'webhook';
    events: string[];
    recipients: string[];
}
export interface ArtifactConfig {
    storage: string;
    retention: number;
    versioning: boolean;
    signing: boolean;
}
export interface TestingConfig {
    unit: boolean;
    integration: boolean;
    e2e: boolean;
    performance: boolean;
    security: boolean;
    coverage: number;
}
export interface DeploymentRequirements {
    strategy: 'rolling' | 'blue_green' | 'canary' | 'recreate';
    automation: boolean;
    approval: boolean;
    rollback: boolean;
    environments: string[];
}
export interface MonitoringRequirements {
    metrics: boolean;
    logs: boolean;
    traces: boolean;
    alerts: boolean;
    dashboards: boolean;
    retention: number;
}
export interface SecurityRequirements {
    scanning: boolean;
    secrets: boolean;
    compliance: boolean;
    encryption: boolean;
    access: boolean;
}
export interface DevOpsResult {
    taskId: string;
    status: 'completed' | 'failed' | 'partial';
    deliverables: DevOpsDeliverable[];
    configurations: ConfigurationFile[];
    pipelines: PipelineDefinition[];
    infrastructure: InfrastructureDefinition[];
    monitoring: MonitoringConfiguration[];
    security: SecurityConfiguration[];
    documentation: DevOpsDocumentation[];
    metrics: DevOpsMetrics;
}
export interface DevOpsDeliverable {
    type: 'pipeline' | 'infrastructure' | 'configuration' | 'script' | 'documentation';
    name: string;
    path: string;
    content: string;
    format: string;
    metadata: any;
}
export interface ConfigurationFile {
    name: string;
    type: 'yaml' | 'json' | 'toml' | 'env' | 'dockerfile' | 'terraform';
    path: string;
    content: string;
    environment: string;
    purpose: string;
}
export interface PipelineDefinition {
    name: string;
    platform: 'github_actions' | 'gitlab_ci' | 'jenkins' | 'azure_devops' | 'circleci';
    stages: PipelineStageDefinition[];
    triggers: PipelineTrigger[];
    variables: Record<string, string>;
    secrets: string[];
}
export interface PipelineStageDefinition {
    name: string;
    jobs: PipelineJob[];
    dependencies: string[];
    conditions: string[];
}
export interface PipelineJob {
    name: string;
    image: string;
    commands: string[];
    artifacts: string[];
    environment: Record<string, string>;
}
export interface InfrastructureDefinition {
    name: string;
    type: 'terraform' | 'cloudformation' | 'arm' | 'pulumi' | 'cdk';
    resources: InfrastructureResource[];
    variables: Record<string, any>;
    outputs: Record<string, any>;
}
export interface InfrastructureResource {
    name: string;
    type: string;
    properties: Record<string, any>;
    dependencies: string[];
}
export interface MonitoringConfiguration {
    tool: string;
    configuration: any;
    dashboards: any[];
    alerts: any[];
    metrics: any[];
}
export interface SecurityConfiguration {
    tool: string;
    policies: any[];
    rules: any[];
    scans: any[];
    compliance: any[];
}
export interface DevOpsDocumentation {
    type: 'runbook' | 'deployment_guide' | 'troubleshooting' | 'architecture';
    title: string;
    content: string;
    audience: string;
}
export interface DevOpsMetrics {
    deploymentFrequency: number;
    leadTime: number;
    changeFailureRate: number;
    recoveryTime: number;
    automation: number;
    reliability: number;
}
/**
 * DevOps Agent - CI/CD and infrastructure automation
 */
export declare class DevOpsAgent extends BaseAgent {
    private readonly devopsSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute DevOps workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual DevOps step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for DevOps
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzeProjectWithMCP;
    private assessInfrastructureWithMCP;
    private designPipelineWithMCP;
    private createInfrastructureWithMCP;
    private setupMonitoringWithMCP;
    private configureSecurityWithMCP;
    private implementDeploymentWithMCP;
    private setupAutomationWithMCP;
    private validateSetupWithMCP;
    private createDocumentationWithMCP;
}
export { DevOpsAgent as default };
//# sourceMappingURL=devops-agent.d.ts.map