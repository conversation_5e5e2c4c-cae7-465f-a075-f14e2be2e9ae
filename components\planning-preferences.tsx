"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Settings,
  Bell,
  Save,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Info,
  Bot,
  Users
} from "lucide-react"
import { PlanningPreferences } from "@/lib/planning-session-manager"
import { PlanningMode } from "@/components/planning-mode-selector"

interface PlanningPreferencesProps {
  preferences: PlanningPreferences
  onUpdatePreferences: (preferences: Partial<PlanningPreferences>) => void
  onReset?: () => void
  className?: string
}

export default function PlanningPreferencesComponent({
  preferences,
  onUpdatePreferences,
  onReset,
  className = ""
}: PlanningPreferencesProps) {
  const handleDefaultMode<PERSON>hange = (mode: PlanningMode) => {
    onUpdatePreferences({ defaultMode: mode })
  }

  const handleInterventionToggle = (key: keyof PlanningPreferences["interventionPoints"]) => {
    onUpdatePreferences({
      interventionPoints: {
        ...preferences.interventionPoints,
        [key]: !preferences.interventionPoints[key]
      }
    })
  }

  const handleNotificationToggle = (key: keyof PlanningPreferences["notifications"]) => {
    onUpdatePreferences({
      notifications: {
        ...preferences.notifications,
        [key]: !preferences.notifications[key]
      }
    })
  }

  const handleAutoSaveToggle = () => {
    onUpdatePreferences({ autoSave: !preferences.autoSave })
  }

  const handleReset = () => {
    if (onReset) {
      onReset()
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Planning Preferences
          </CardTitle>
          <CardDescription className="text-[#666]">
            Configure your default planning behavior and workflow settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Default Mode Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-white">Default Planning Mode</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Card 
                className={`cursor-pointer transition-all border-2 ${
                  preferences.defaultMode === "autonomous" 
                    ? "border-blue-500 bg-blue-500/10" 
                    : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
                }`}
                onClick={() => handleDefaultModeChange("autonomous")}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-600/20 rounded-lg">
                      <Bot className="w-4 h-4 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-white">Autonomous</span>
                        {preferences.defaultMode === "autonomous" && (
                          <CheckCircle className="w-4 h-4 text-blue-400" />
                        )}
                      </div>
                      <p className="text-xs text-[#666] mt-1">Fully automated planning</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card 
                className={`cursor-pointer transition-all border-2 ${
                  preferences.defaultMode === "copilot" 
                    ? "border-green-500 bg-green-500/10" 
                    : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
                }`}
                onClick={() => handleDefaultModeChange("copilot")}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-600/20 rounded-lg">
                      <Users className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-white">Co-Pilot</span>
                        {preferences.defaultMode === "copilot" && (
                          <CheckCircle className="w-4 h-4 text-green-400" />
                        )}
                      </div>
                      <p className="text-xs text-[#666] mt-1">Collaborative planning</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator className="bg-[#1a1a1a]" />

          {/* General Settings */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-white">General Settings</Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Auto-save Progress</Label>
                  <p className="text-xs text-[#666]">Automatically save planning session progress</p>
                </div>
                <Switch
                  checked={preferences.autoSave}
                  onCheckedChange={handleAutoSaveToggle}
                />
              </div>
            </div>
          </div>

          <Separator className="bg-[#1a1a1a]" />

          {/* Intervention Points */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium text-white">Intervention Points</Label>
              <Badge className="text-xs bg-green-600/20 text-green-400 border-green-600/30">
                Co-Pilot Mode Only
              </Badge>
            </div>
            <p className="text-xs text-[#666]">
              Choose when you want to review and approve planning phases in co-pilot mode
            </p>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Requirements Review</Label>
                  <p className="text-xs text-[#666]">Review generated requirements before design phase</p>
                </div>
                <Switch
                  checked={preferences.interventionPoints.requirementsReview}
                  onCheckedChange={() => handleInterventionToggle("requirementsReview")}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Design Review</Label>
                  <p className="text-xs text-[#666]">Review generated design before task planning</p>
                </div>
                <Switch
                  checked={preferences.interventionPoints.designReview}
                  onCheckedChange={() => handleInterventionToggle("designReview")}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Tasks Review</Label>
                  <p className="text-xs text-[#666]">Review generated tasks before implementation</p>
                </div>
                <Switch
                  checked={preferences.interventionPoints.tasksReview}
                  onCheckedChange={() => handleInterventionToggle("tasksReview")}
                />
              </div>
            </div>
          </div>

          <Separator className="bg-[#1a1a1a]" />

          {/* Notifications */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4 text-[#666]" />
              <Label className="text-sm font-medium text-white">Notifications</Label>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Phase Completion</Label>
                  <p className="text-xs text-[#666]">Notify when planning phases are completed</p>
                </div>
                <Switch
                  checked={preferences.notifications.phaseCompletion}
                  onCheckedChange={() => handleNotificationToggle("phaseCompletion")}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Error Alerts</Label>
                  <p className="text-xs text-[#666]">Notify when planning errors occur</p>
                </div>
                <Switch
                  checked={preferences.notifications.errorAlerts}
                  onCheckedChange={() => handleNotificationToggle("errorAlerts")}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-white">Progress Updates</Label>
                  <p className="text-xs text-[#666]">Notify on planning progress milestones</p>
                </div>
                <Switch
                  checked={preferences.notifications.progressUpdates}
                  onCheckedChange={() => handleNotificationToggle("progressUpdates")}
                />
              </div>
            </div>
          </div>

          <Separator className="bg-[#1a1a1a]" />

          {/* Actions */}
          <div className="flex items-center justify-between pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset to Defaults
            </Button>
            <div className="flex items-center gap-2 text-xs text-[#666]">
              <Save className="w-3 h-3" />
              <span>Settings saved automatically</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Card */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white">Planning Mode Guide</h4>
              <div className="space-y-2 text-xs text-[#666]">
                <div>
                  <span className="text-blue-400 font-medium">Autonomous Mode:</span> AI handles the entire planning process automatically. Best for rapid prototyping and when you trust the AI to make good decisions.
                </div>
                <div>
                  <span className="text-green-400 font-medium">Co-Pilot Mode:</span> You collaborate with AI at each phase. Best for complex projects where you want control over requirements, design, and task planning.
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}