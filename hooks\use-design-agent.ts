import { useState, useCallback } from 'react'
import { createDesignAgent, DesignDocument, ArchitecturalAnalysis, TechStack, Component, DataModel, APIDesign, UIDesign, DesignGenerationOptions } from '../lib/agents/design-agent'

export interface DesignAgentState {
  isLoading: boolean
  error: string | null
  designDocument: DesignDocument | null
  architecturalAnalysis: ArchitecturalAnalysis | null
  techStackSelection: {
    recommended: TechStack
    alternatives: TechStack[]
    rationale: Record<string, string>
  } | null
  components: Component[]
  dataModel: DataModel | null
  apiDesign: APIDesign | null
  uiDesign: UIDesign | null
  wireframes: Record<string, string>
  userJourneyDiagram: string | null
  markdownDocument: string | null
  qualityScore: number | null
  recommendations: string[]
}

export interface UseDesignAgentOptions {
  provider?: string
  model?: string
  autoGenerateMarkdown?: boolean
}

export interface DesignGenerationRequest {
  requirements: any[]
  options?: DesignGenerationOptions
}

export function useDesignAgent(options: UseDesignAgentOptions = {}) {
  const {
    provider = 'openrouter',
    model = 'kimi-k2',
    autoGenerateMarkdown = true
  } = options

  const [state, setState] = useState<DesignAgentState>({
    isLoading: false,
    error: null,
    designDocument: null,
    architecturalAnalysis: null,
    techStackSelection: null,
    components: [],
    dataModel: null,
    apiDesign: null,
    uiDesign: null,
    wireframes: {},
    userJourneyDiagram: null,
    markdownDocument: null,
    qualityScore: null,
    recommendations: []
  })

  const [agent] = useState(() => createDesignAgent(provider, model))

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading, error: null }))
  }, [])

  const setError = useCallback((error: string) => {
    setState(prev => ({ ...prev, error, isLoading: false }))
  }, [])

  const updateState = useCallback((updates: Partial<DesignAgentState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  /**
   * Generate comprehensive design document from requirements
   */
  const generateDesignDocument = useCallback(async (request: DesignGenerationRequest) => {
    try {
      setLoading(true)

      const designDocument = await agent.generateDesignDocument(
        request.requirements,
        request.options
      )

      let markdownDocument: string | null = null
      if (autoGenerateMarkdown) {
        markdownDocument = await agent.generateMarkdownDocument(designDocument)
      }

      updateState({
        designDocument,
        markdownDocument,
        isLoading: false
      })

      return designDocument
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate design document')
      throw error
    }
  }, [agent, autoGenerateMarkdown, setLoading, setError, updateState])

  /**
   * Analyze architecture patterns and recommendations
   */
  const analyzeArchitecture = useCallback(async (requirements: any[]) => {
    try {
      setLoading(true)

      const architecturalAnalysis = await agent.analyzeArchitecture(requirements)

      updateState({
        architecturalAnalysis,
        isLoading: false
      })

      return architecturalAnalysis
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to analyze architecture')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Select optimal technology stack
   */
  const selectTechnologyStack = useCallback(async (
    requirements: any[],
    constraints: string[] = []
  ) => {
    try {
      setLoading(true)

      const techStackSelection = await agent.selectTechnologyStack(requirements, constraints)

      updateState({
        techStackSelection,
        isLoading: false
      })

      return techStackSelection
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to select technology stack')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Design system components
   */
  const designComponents = useCallback(async (
    requirements: any[],
    architecturalPattern: any
  ) => {
    try {
      setLoading(true)

      const components = await agent.designComponents(requirements, architecturalPattern)

      updateState({
        components,
        isLoading: false
      })

      return components
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to design components')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Design data model
   */
  const designDataModel = useCallback(async (
    requirements: any[],
    techStack: TechStack
  ) => {
    try {
      setLoading(true)

      const dataModel = await agent.designDataModel(requirements, techStack)

      updateState({
        dataModel,
        isLoading: false
      })

      return dataModel
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to design data model')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Design API specifications
   */
  const designAPI = useCallback(async (
    requirements: any[],
    components: Component[],
    dataModel: DataModel
  ) => {
    try {
      setLoading(true)

      const apiDesign = await agent.designAPI(requirements, components, dataModel)

      updateState({
        apiDesign,
        isLoading: false
      })

      return apiDesign
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to design API')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Perform comprehensive design analysis
   */
  const performComprehensiveAnalysis = useCallback(async (request: DesignGenerationRequest) => {
    try {
      setLoading(true)

      const analysis = await agent.performDesignAnalysis(
        request.requirements,
        request.options
      )

      let markdownDocument: string | null = null
      if (autoGenerateMarkdown) {
        markdownDocument = await agent.generateMarkdownDocument(analysis.designDocument)
      }

      updateState({
        designDocument: analysis.designDocument,
        architecturalAnalysis: analysis.architecturalAnalysis,
        qualityScore: analysis.qualityScore,
        recommendations: analysis.recommendations,
        wireframes: analysis.wireframes || {},
        userJourneyDiagram: analysis.userJourneyDiagram || null,
        markdownDocument,
        isLoading: false
      })

      return analysis
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to perform comprehensive analysis')
      throw error
    }
  }, [agent, autoGenerateMarkdown, setLoading, setError, updateState])

  /**
   * Design UI and generate wireframes
   */
  const designUI = useCallback(async (
    requirements: any[],
    components: Component[],
    apiDesign: APIDesign
  ) => {
    try {
      setLoading(true)

      const uiDesign = await agent.designUI(requirements, components, apiDesign)

      updateState({
        uiDesign,
        isLoading: false
      })

      return uiDesign
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to design UI')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Generate wireframes for all pages
   */
  const generateWireframes = useCallback(async (
    requirements: any[],
    components: Component[],
    apiDesign: APIDesign
  ) => {
    try {
      setLoading(true)

      const result = await agent.generateCompleteUIDesign(requirements, components, apiDesign)

      updateState({
        uiDesign: result.uiDesign,
        wireframes: result.wireframes,
        userJourneyDiagram: result.userJourneyDiagram,
        isLoading: false
      })

      return result
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate wireframes')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Generate wireframe for a specific page
   */
  const generatePageWireframe = useCallback(async (
    pageName: string,
    pageType: string,
    requirements: any[],
    components: Component[]
  ) => {
    try {
      setLoading(true)

      const wireframe = await agent.generatePageWireframe(pageName, pageType, requirements, components)

      updateState({
        wireframes: { ...state.wireframes, [pageName]: wireframe },
        isLoading: false
      })

      return wireframe
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate page wireframe')
      throw error
    }
  }, [agent, state.wireframes, setLoading, setError, updateState])

  /**
   * Generate user journey diagram
   */
  const generateUserJourneyDiagram = useCallback(async (userJourney: any[]) => {
    try {
      setLoading(true)

      const userJourneyDiagram = await agent.generateUserJourneyDiagram(userJourney)

      updateState({
        userJourneyDiagram,
        isLoading: false
      })

      return userJourneyDiagram
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate user journey diagram')
      throw error
    }
  }, [agent, setLoading, setError, updateState])

  /**
   * Generate markdown documentation
   */
  const generateMarkdownDocument = useCallback(async (designDoc?: DesignDocument) => {
    try {
      const docToUse = designDoc || state.designDocument
      if (!docToUse) {
        throw new Error('No design document available to generate markdown')
      }

      setLoading(true)

      const markdownDocument = await agent.generateMarkdownDocument(docToUse)

      updateState({
        markdownDocument,
        isLoading: false
      })

      return markdownDocument
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate markdown document')
      throw error
    }
  }, [agent, state.designDocument, setLoading, setError, updateState])

  /**
   * Clear all generated data
   */
  const clearDesignData = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      designDocument: null,
      architecturalAnalysis: null,
      techStackSelection: null,
      components: [],
      dataModel: null,
      apiDesign: null,
      uiDesign: null,
      wireframes: {},
      userJourneyDiagram: null,
      markdownDocument: null,
      qualityScore: null,
      recommendations: []
    })
  }, [])

  /**
   * Reset error state
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  return {
    // State
    ...state,
    
    // Actions
    generateDesignDocument,
    analyzeArchitecture,
    selectTechnologyStack,
    designComponents,
    designDataModel,
    designAPI,
    designUI,
    generateWireframes,
    generatePageWireframe,
    generateUserJourneyDiagram,
    performComprehensiveAnalysis,
    generateMarkdownDocument,
    clearDesignData,
    clearError,

    // Computed properties
    hasDesignDocument: !!state.designDocument,
    hasArchitecturalAnalysis: !!state.architecturalAnalysis,
    hasTechStackSelection: !!state.techStackSelection,
    hasComponents: state.components.length > 0,
    hasDataModel: !!state.dataModel,
    hasAPIDesign: !!state.apiDesign,
    hasUIDesign: !!state.uiDesign,
    hasWireframes: Object.keys(state.wireframes).length > 0,
    hasUserJourneyDiagram: !!state.userJourneyDiagram,
    hasMarkdownDocument: !!state.markdownDocument,
    isComplete: !!(
      state.designDocument &&
      state.architecturalAnalysis &&
      state.techStackSelection &&
      state.components.length > 0 &&
      state.dataModel &&
      state.apiDesign &&
      state.uiDesign
    )
  }
}