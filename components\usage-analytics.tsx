"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  DollarSign, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Download,
  RefreshCw,
  Zap,
  Target,
  BarChart3,
  PieChart
} from "lucide-react"

interface UsageData {
  usage: {
    totalCost: number
    totalRequests: number
    totalTokens: number
    averageLatency: number
    successRate: number
    topModels: Array<{ model: string, usage: number, cost: number }>
    dailyBreakdown: Array<{ date: string, cost: number, requests: number }>
  }
  budget: {
    monthlyBudget: number
    currentMonthSpend: number
    remainingBudget: number
    percentUsed: number
    isOverBudget: boolean
    dailyAverage: number
    projectedMonthlySpend: number
  }
  metrics: Record<string, {
    averageLatency: number
    successRate: number
    costPerToken: number
    totalRequests: number
    totalTokens: number
    totalCost: number
    lastUsed: string
  }>
  recommendations: {
    mostEfficient: string[]
    mostReliable: string[]
    bestValue: string[]
    fastest: string[]
  }
}

export default function UsageAnalytics() {
  const [data, setData] = useState<UsageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchUsageData()
  }, [period])

  const fetchUsageData = async () => {
    try {
      setRefreshing(true)
      const response = await fetch(`/api/usage?days=${period}`)
      const usageData = await response.json()
      setData(usageData)
    } catch (error) {
      console.error('Failed to fetch usage data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const exportData = async (format: 'json' | 'csv' = 'csv') => {
    try {
      const response = await fetch(`/api/usage?days=${period}&format=${format}`)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ai-usage-report-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to export data:', error)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading usage analytics...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-yellow-500" />
        <p>Failed to load usage analytics</p>
        <Button onClick={fetchUsageData} className="mt-2">
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI Usage Analytics</h2>
          <p className="text-gray-500">Monitor costs, performance, and usage patterns</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            onClick={fetchUsageData}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => exportData('csv')}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Budget Overview */}
      <Card className={data.budget.isOverBudget ? 'border-red-500' : ''}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Budget Overview
            {data.budget.isOverBudget && (
              <Badge variant="destructive">Over Budget</Badge>
            )}
          </CardTitle>
          <CardDescription>
            Monthly budget tracking and projections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">
                {formatCurrency(data.budget.monthlyBudget)}
              </div>
              <div className="text-xs text-gray-500">Monthly Budget</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${data.budget.isOverBudget ? 'text-red-500' : 'text-green-500'}`}>
                {formatCurrency(data.budget.currentMonthSpend)}
              </div>
              <div className="text-xs text-gray-500">Current Spend</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">
                {formatCurrency(data.budget.dailyAverage)}
              </div>
              <div className="text-xs text-gray-500">Daily Average</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-500">
                {formatCurrency(data.budget.projectedMonthlySpend)}
              </div>
              <div className="text-xs text-gray-500">Projected Monthly</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Budget Usage</span>
              <span>{data.budget.percentUsed.toFixed(1)}%</span>
            </div>
            <Progress 
              value={Math.min(data.budget.percentUsed, 100)} 
              className={`h-2 ${data.budget.percentUsed > 80 ? 'bg-red-100' : ''}`}
            />
            <div className="text-xs text-gray-500">
              {formatCurrency(data.budget.remainingBudget)} remaining this month
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Total Cost
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-500">
                  {formatCurrency(data.usage.totalCost)}
                </div>
                <div className="text-xs text-gray-500">
                  Last {period} days
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Total Requests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-500">
                  {formatNumber(data.usage.totalRequests)}
                </div>
                <div className="text-xs text-gray-500">
                  API calls made
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Avg Latency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-500">
                  {data.usage.averageLatency.toFixed(0)}ms
                </div>
                <div className="text-xs text-gray-500">
                  Response time
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  Success Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-500">
                  {(data.usage.successRate * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">
                  Successful requests
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Daily Usage Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Daily Usage Trend</CardTitle>
              <CardDescription>Cost and request volume over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {data.usage.dailyBreakdown.slice(-7).map((day, index) => (
                  <div key={day.date} className="flex items-center justify-between p-2 rounded bg-gray-50 dark:bg-gray-800">
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-medium">
                        {new Date(day.date).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {day.requests} requests
                      </Badge>
                    </div>
                    <div className="text-sm font-medium">
                      {formatCurrency(day.cost)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Models by Usage</CardTitle>
              <CardDescription>Most frequently used AI models</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.usage.topModels.slice(0, 10).map((model, index) => (
                  <div key={model.model} className="flex items-center justify-between p-3 rounded border">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                      <div>
                        <div className="font-medium text-sm">{model.model}</div>
                        <div className="text-xs text-gray-500">
                          {model.usage} requests
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(model.cost)}</div>
                      <div className="text-xs text-gray-500">
                        {formatCurrency(model.cost / model.usage)} per request
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(data.metrics).slice(0, 6).map(([modelKey, metrics]) => (
              <Card key={modelKey}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">{modelKey}</CardTitle>
                  <CardDescription className="text-xs">
                    Last used: {new Date(metrics.lastUsed).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{metrics.averageLatency.toFixed(0)}ms</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      <span>{(metrics.successRate * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-3 h-3" />
                      <span>{formatCurrency(metrics.totalCost)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <BarChart3 className="w-3 h-3" />
                      <span>{metrics.totalRequests} reqs</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Most Cost Efficient
                </CardTitle>
                <CardDescription>Best cost per token ratio</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.recommendations.mostEfficient.slice(0, 3).map((model, index) => (
                    <div key={model} className="flex items-center justify-between p-2 rounded bg-green-50 dark:bg-green-900/20">
                      <span className="text-sm">{model}</span>
                      <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  Most Reliable
                </CardTitle>
                <CardDescription>Highest success rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.recommendations.mostReliable.slice(0, 3).map((model, index) => (
                    <div key={model} className="flex items-center justify-between p-2 rounded bg-blue-50 dark:bg-blue-900/20">
                      <span className="text-sm">{model}</span>
                      <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Best Value
                </CardTitle>
                <CardDescription>Optimal performance/cost balance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.recommendations.bestValue.slice(0, 3).map((model, index) => (
                    <div key={model} className="flex items-center justify-between p-2 rounded bg-purple-50 dark:bg-purple-900/20">
                      <span className="text-sm">{model}</span>
                      <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Fastest Response
                </CardTitle>
                <CardDescription>Lowest average latency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.recommendations.fastest.slice(0, 3).map((model, index) => (
                    <div key={model} className="flex items-center justify-between p-2 rounded bg-orange-50 dark:bg-orange-900/20">
                      <span className="text-sm">{model}</span>
                      <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}