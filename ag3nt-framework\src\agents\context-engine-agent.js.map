{"version": 3, "file": "context-engine-agent.js", "sourceRoot": "", "sources": ["context-engine-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AAif5C;;GAEG;AACH,MAAa,kBAAmB,SAAQ,sBAAS;IAO/C,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,gBAAgB,EAAE;YACtB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,mBAAmB;oBACnB,oBAAoB;oBACpB,qBAAqB;oBACrB,iBAAiB;oBACjB,gBAAgB;oBAChB,sBAAsB;oBACtB,mBAAmB;oBACnB,sBAAsB;iBACvB;gBACD,cAAc,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,CAAC;gBAClF,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QA1Ba,iBAAY,GAAG;YAC9B,iBAAiB,EAAE,mBAAmB,EAAE,wBAAwB;YAChE,kBAAkB,EAAE,sBAAsB,EAAE,sBAAsB;YAClE,mBAAmB,EAAE,kBAAkB,EAAE,sBAAsB;SAChE,CAAA;IAuBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAA2B,CAAA;QAE/C,OAAO,CAAC,GAAG,CAAC,wCAAwC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAEvE,qCAAqC;QACrC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,wCAAwC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,8DAA8D;QAC9D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,iBAAiB;gBACpB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAC/D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,wBAAwB;gBAC3B,OAAO,MAAM,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAA;YAC9D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D;gBACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAA;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAA;IACjC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,aAAa,EAAE,6DAA6D;YAC5E,kBAAkB,EAAE,oDAAoD;YACxE,kBAAkB,EAAE,mDAAmD;YACvE,cAAc,EAAE,gDAAgD;YAChE,mBAAmB,EAAE,+CAA+C;YACpE,gBAAgB,EAAE,iDAAiD;SACpE,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,qBAAqB,CAAC,KAAU,EAAE,KAAyB;QACvE,MAAM,SAAS,GAAG,MAAM,sBAAS,CAAC,cAAc,CAC9C,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAE/C,MAAM,SAAS,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAA;QAErE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,KAAU;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAE/C,MAAM,KAAK,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA;QAE7D,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QAEjC,OAAO;YACL,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAE/C,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;QAE/D,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,SAAS,GAAG,MAAM,sBAAS,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;QAEzE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,KAAK,CAAA;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAE/C,MAAM,aAAa,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QAE/E,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAA;QAEjD,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,UAAU,GAAG;YACjB,SAAS,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS;YACxC,KAAK,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,KAAK;YAChC,QAAQ,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ;YACtC,SAAS,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS;YACxC,aAAa,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,aAAa;SACjD,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAA;QAEpE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAA;QAEtC,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,eAAe,CAAC,UAAU,CAAC,CAAA;QAEhE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,CAAA;QAErD,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,kBAAkB,CACjD,YAAY,EACZ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AAxQD,gDAwQC;AAG8B,qCAAO"}