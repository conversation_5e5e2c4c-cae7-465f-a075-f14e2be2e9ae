/**
 * AG3NT Framework - Main Framework Class
 *
 * The core orchestrator for the AG3NT autonomous agent framework.
 * Provides a unified interface for:
 * - Agent registration and management
 * - Context engine integration
 * - Workflow orchestration
 * - Multi-agent coordination
 * - Session management
 *
 * This framework rivals CrewAI and LangGraph with superior capabilities:
 * - MCP-enhanced agents with real-time context
 * - Sequential thinking capabilities
 * - Context enrichment pipeline
 * - Advanced agent coordination
 * - Built-in progress tracking
 */
import { EventEmitter } from "events";
import { BaseAgent, AgentState } from "./core/base-agent";
import { AgentRegistry, RegisteredAgent } from "./core/agent-registry";
import { AgentCommunicationProtocol } from "./core/agent-communication";
import { WorkflowCoordinator } from "./core/workflow-coordinator";
import { AdvancedFeaturesManager, type AdvancedFeaturesConfig } from "./advanced";
import { TaskDelegationSystem } from "./coordination/task-delegation-system";
import { ConsensusProtocolEngine } from "./coordination/consensus-protocol-engine";
import { WorkflowHandoffManager } from "./coordination/workflow-handoff-manager";
import { CoordinationPatternRegistry } from "./coordination/coordination-pattern-registry";
import { AgentDiscoveryService } from "./discovery/agent-discovery-service";
import { LoadBalancer } from "./discovery/load-balancer";
import { FailoverManager } from "./discovery/failover-manager";
export interface FrameworkConfig {
    contextEngine?: {
        enableMCP?: boolean;
        enableSequentialThinking?: boolean;
        enableRAG?: boolean;
        enableContextEnrichment?: boolean;
    };
    agents?: {
        maxConcurrentSessions?: number;
        defaultTimeout?: number;
        defaultRetries?: number;
    };
    monitoring?: {
        enableHealthChecks?: boolean;
        healthCheckInterval?: number;
        enableMetrics?: boolean;
    };
    coordination?: {
        enableTaskDelegation?: boolean;
        enableConsensus?: boolean;
        enableWorkflowHandoffs?: boolean;
        enablePatternRegistry?: boolean;
        delegationTimeout?: number;
        consensusTimeout?: number;
        handoffTimeout?: number;
    };
    discovery?: {
        enableAgentDiscovery?: boolean;
        enableLoadBalancing?: boolean;
        enableFailover?: boolean;
        discoveryInterval?: number;
        healthCheckInterval?: number;
        loadBalancingAlgorithm?: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'adaptive';
    };
    advancedFeatures?: AdvancedFeaturesConfig;
}
export interface SessionConfig {
    sessionId?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    tags?: string[];
    timeout?: number;
    agentPreferences?: {
        agentType?: string;
        agentId?: string;
    };
}
export interface ExecutionResult {
    sessionId: string;
    agentId: string;
    agentType: string;
    success: boolean;
    result?: AgentState;
    error?: string;
    duration: number;
    metadata: {
        startTime: string;
        endTime: string;
        stepsCompleted: number;
        totalSteps: number;
    };
}
/**
 * AG3NT Framework - The autonomous agent orchestration framework
 */
export declare class AG3NTFramework extends EventEmitter {
    private registry;
    private communication;
    private workflowCoordinator;
    private contextEngine;
    private advancedFeatures?;
    private delegationSystem?;
    private consensusEngine?;
    private handoffManager?;
    private patternRegistry?;
    private discoveryService?;
    private loadBalancer?;
    private failoverManager?;
    private config;
    private isInitialized;
    private activeSessions;
    constructor(config?: FrameworkConfig);
    /**
     * Initialize the framework
     */
    initialize(): Promise<void>;
    /**
     * Register a custom agent with the framework
     */
    registerAgent(agent: BaseAgent, metadata?: any): Promise<string>;
    /**
     * Execute a task using the best available agent
     */
    execute(agentType: string, input: any, config?: SessionConfig): Promise<ExecutionResult>;
    /**
     * Get framework statistics
     */
    getStats(): {
        framework: {
            initialized: boolean;
            activeSessions: number;
            totalAgents: number;
        };
        agents: ReturnType<AgentRegistry['getStats']>;
        communication: ReturnType<AgentCommunicationProtocol['getStats']>;
        workflows: ReturnType<WorkflowCoordinator['getStats']>;
        contextEngine: {
            enabled: boolean;
            mcpEnabled: boolean;
            ragEnabled: boolean;
            stats?: ReturnType<AG3NTContextEngine['getStats']>;
        };
    };
    /**
     * Get all registered agents
     */
    getAgents(): RegisteredAgent[];
    /**
     * Get agent by ID
     */
    getAgent(agentId: string): RegisteredAgent | null;
    /**
     * Register built-in agents
     */
    private registerBuiltInAgents;
    /**
     * Get advanced features manager
     */
    getAdvancedFeatures(): AdvancedFeaturesManager | undefined;
    /**
     * Get adaptive learning system
     */
    getAdaptiveLearning(): import("./advanced").AdaptiveLearningSystem;
    /**
     * Get temporal database
     */
    getTemporalDatabase(): import("./advanced").TemporalContextDatabase;
    /**
     * Get collaboration system
     */
    getCollaboration(): import("./advanced").RealtimeCollaborationSystem;
    /**
     * Get optimization system
     */
    getOptimization(): import("./advanced").DynamicOptimizationSystem;
    /**
     * Get marketplace
     */
    getMarketplace(): import("./advanced").AgentMarketplace;
    /**
     * Get monitoring system
     */
    getMonitoring(): import("./advanced").AdvancedMonitoringSystem;
    /**
     * Enhanced coordination methods
     */
    /**
     * Get task delegation system
     */
    getDelegationSystem(): TaskDelegationSystem | undefined;
    /**
     * Get consensus protocol engine
     */
    getConsensusEngine(): ConsensusProtocolEngine | undefined;
    /**
     * Get workflow handoff manager
     */
    getHandoffManager(): WorkflowHandoffManager | undefined;
    /**
     * Get coordination pattern registry
     */
    getPatternRegistry(): CoordinationPatternRegistry | undefined;
    /**
     * Delegate task between agents
     */
    delegateTask(fromAgent: string, toAgent: string, task: any, delegationType?: 'hierarchical' | 'peer' | 'emergency' | 'load_balance'): Promise<any>;
    /**
     * Create consensus proposal
     */
    createConsensusProposal(proposerId: string, title: string, description: string, options: any[]): Promise<any>;
    /**
     * Initiate workflow handoff
     */
    initiateHandoff(fromAgent: string, toAgent: string, workflowId: string, taskId: string, state: any): Promise<any>;
    /**
     * Register agent for coordination
     */
    registerAgentForCoordination(agentId: string, capabilities: any[], authority?: number): void;
    /**
     * Get coordination analytics
     */
    getCoordinationAnalytics(): any;
    /**
     * Discovery and load balancing methods
     */
    /**
     * Get agent discovery service
     */
    getDiscoveryService(): AgentDiscoveryService | undefined;
    /**
     * Get load balancer
     */
    getLoadBalancer(): LoadBalancer | undefined;
    /**
     * Get failover manager
     */
    getFailoverManager(): FailoverManager | undefined;
    /**
     * Discover agents with query
     */
    discoverAgents(query?: any): Promise<any>;
    /**
     * Route request to optimal agent
     */
    routeRequest(request: any): Promise<any>;
    /**
     * Create failover plan for agent
     */
    createFailoverPlan(agentId: string): Promise<any>;
    /**
     * Get discovery and load balancing analytics
     */
    getDiscoveryAnalytics(): any;
}
export declare const ag3ntFramework: AG3NTFramework;
export default AG3NTFramework;
//# sourceMappingURL=ag3nt-framework.d.ts.map