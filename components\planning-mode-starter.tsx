"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Bot, 
  Users, 
  Play, 
  Zap, 
  Brain, 
  Target, 
  Clock,
  CheckCircle,
  User,
  Settings
} from "lucide-react"
import { usePlanningSession } from "@/hooks/use-planning-session"
import { PlanningMode } from "@/components/planning-mode-selector"

interface PlanningModeStarterProps {
  className?: string
}

export default function PlanningModeStarter({ className = "" }: PlanningModeStarterProps) {
  const { startPlanning, preferences } = usePlanningSession()
  const [selectedMode, setSelectedMode] = useState<PlanningMode>(preferences.defaultMode)
  const [projectName, setProjectName] = useState('')
  const [projectDescription, setProjectDescription] = useState('')

  const handleStartPlanning = () => {
    if (!projectName.trim()) return
    startPlanning(selectedMode, projectName)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Project Setup */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Target className="w-5 h-5" />
            Start New Planning Session
          </CardTitle>
          <CardDescription className="text-gray-400">
            Configure your project and choose how you want to approach the planning process
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Project Details */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm text-white">Project Name</Label>
              <Input
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="Enter your project name..."
                className="bg-[#111111] border-[#1a1a1a] text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm text-white">Project Description (Optional)</Label>
              <Input
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                placeholder="Brief description of what you're building..."
                className="bg-[#111111] border-[#1a1a1a] text-white"
              />
            </div>
          </div>

          {/* Mode Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-white">Planning Mode</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {/* Autonomous Mode */}
              <Card 
                className={`cursor-pointer transition-all border-2 ${
                  selectedMode === "autonomous" 
                    ? "border-blue-500 bg-blue-500/10" 
                    : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
                }`}
                onClick={() => setSelectedMode("autonomous")}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-600/20 rounded-lg">
                      <Bot className="w-5 h-5 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-white">Autonomous</span>
                        {selectedMode === "autonomous" && (
                          <CheckCircle className="w-4 h-4 text-blue-400" />
                        )}
                      </div>
                      <p className="text-xs text-gray-400 mt-1">AI handles everything automatically</p>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Zap className="w-3 h-3 text-green-400" />
                      <span>Fully automated planning</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Brain className="w-3 h-3 text-purple-400" />
                      <span>AI generates everything</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="w-3 h-3 text-blue-400" />
                      <span>Fastest time to results</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Co-Pilot Mode */}
              <Card 
                className={`cursor-pointer transition-all border-2 ${
                  selectedMode === "copilot" 
                    ? "border-green-500 bg-green-500/10" 
                    : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
                }`}
                onClick={() => setSelectedMode("copilot")}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-600/20 rounded-lg">
                      <Users className="w-5 h-5 text-green-400" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-white">Co-Pilot</span>
                        {selectedMode === "copilot" && (
                          <CheckCircle className="w-4 h-4 text-green-400" />
                        )}
                      </div>
                      <p className="text-xs text-gray-400 mt-1">Collaborative planning with oversight</p>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <User className="w-3 h-3 text-blue-400" />
                      <span>Human-in-the-loop collaboration</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <CheckCircle className="w-3 h-3 text-green-400" />
                      <span>Review each phase</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Settings className="w-3 h-3 text-purple-400" />
                      <span>Fine-tune requirements</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Mode Comparison */}
          <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
            <h4 className="text-white font-medium mb-3 text-sm">Quick Comparison</h4>
            <div className="grid grid-cols-3 gap-4 text-xs">
              <div>
                <div className="text-gray-400 mb-2">Speed</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <Badge className="bg-blue-600/20 text-blue-400 border-blue-600/30 text-xs px-1 py-0">
                      Fast
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <Badge className="bg-yellow-600/20 text-yellow-400 border-yellow-600/30 text-xs px-1 py-0">
                      Medium
                    </Badge>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="text-gray-400 mb-2">Control</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <Badge className="bg-gray-600/20 text-gray-400 border-gray-600/30 text-xs px-1 py-0">
                      Low
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <Badge className="bg-green-600/20 text-green-400 border-green-600/30 text-xs px-1 py-0">
                      High
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <div className="text-gray-400 mb-2">Accuracy</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <Badge className="bg-blue-600/20 text-blue-400 border-blue-600/30 text-xs px-1 py-0">
                      Good
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <Badge className="bg-green-600/20 text-green-400 border-green-600/30 text-xs px-1 py-0">
                      Excellent
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Start Button */}
          <div className="flex items-center justify-center pt-4">
            <Button
              onClick={handleStartPlanning}
              disabled={!projectName.trim()}
              className={`px-8 py-3 ${
                selectedMode === "autonomous" 
                  ? "bg-blue-600 hover:bg-blue-700" 
                  : "bg-green-600 hover:bg-green-700"
              } text-white font-medium`}
            >
              <Play className="w-4 h-4 mr-2" />
              Start {selectedMode === "autonomous" ? "Autonomous" : "Co-Pilot"} Planning
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardContent className="p-4">
          <div className="text-center space-y-2">
            <h4 className="text-sm font-medium text-white">What happens next?</h4>
            <div className="text-xs text-gray-400 space-y-1">
              <p>
                <span className="text-blue-400 font-medium">Autonomous Mode:</span> AI will automatically generate requirements, 
                design architecture, and create implementation tasks. You can watch the process in real-time.
              </p>
              <p>
                <span className="text-green-400 font-medium">Co-Pilot Mode:</span> AI will work with you step-by-step, 
                asking for your input and approval at each phase of the planning process.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}