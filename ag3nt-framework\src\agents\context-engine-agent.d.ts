/**
 * AG3NT Framework - Context Engine Agent
 *
 * The centerpiece agent that provides deep codebase understanding and context.
 * Acts as the "neural network" connecting all agents with intelligent context.
 *
 * Features:
 * - Codebase ingestion and analysis
 * - Relationship and dependency graph building
 * - Real-time context updates
 * - Cross-agent memory and knowledge sharing
 * - Semantic search and retrieval
 * - Temporal graph capabilities
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface ContextEngineInput {
    task: ContextTask;
    codebase: CodebaseInfo;
    query?: ContextQuery;
    update?: ContextUpdate;
}
export interface ContextTask {
    taskId: string;
    type: 'ingest' | 'analyze' | 'query' | 'update' | 'graph_build' | 'search' | 'relate';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    scope: ContextScope;
    requirements: string[];
}
export interface ContextScope {
    files: string[];
    directories: string[];
    languages: string[];
    frameworks: string[];
    depth: 'shallow' | 'medium' | 'deep';
    includeTests: boolean;
    includeDocs: boolean;
}
export interface CodebaseInfo {
    rootPath: string;
    structure: FileStructure;
    metadata: CodebaseMetadata;
    dependencies: DependencyInfo[];
    configuration: ConfigurationInfo;
}
export interface FileStructure {
    files: FileInfo[];
    directories: DirectoryInfo[];
    totalFiles: number;
    totalLines: number;
    languages: LanguageStats[];
}
export interface FileInfo {
    path: string;
    name: string;
    extension: string;
    size: number;
    lines: number;
    language: string;
    lastModified: string;
    checksum: string;
}
export interface DirectoryInfo {
    path: string;
    name: string;
    fileCount: number;
    subdirectories: string[];
    purpose: string;
}
export interface LanguageStats {
    language: string;
    fileCount: number;
    lineCount: number;
    percentage: number;
}
export interface CodebaseMetadata {
    name: string;
    version: string;
    description: string;
    framework: string;
    architecture: string;
    patterns: string[];
    conventions: string[];
}
export interface DependencyInfo {
    name: string;
    version: string;
    type: 'runtime' | 'development' | 'peer' | 'optional';
    source: string;
    vulnerabilities: SecurityVulnerability[];
    usage: DependencyUsage[];
}
export interface SecurityVulnerability {
    id: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    fix: string;
}
export interface DependencyUsage {
    file: string;
    imports: string[];
    usage: string[];
}
export interface ConfigurationInfo {
    buildConfig: any;
    environmentConfig: any;
    deploymentConfig: any;
    testConfig: any;
}
export interface ContextQuery {
    queryId: string;
    type: 'semantic' | 'structural' | 'dependency' | 'usage' | 'similarity';
    query: string;
    filters: QueryFilter[];
    options: QueryOptions;
}
export interface QueryFilter {
    field: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
    value: any;
}
export interface QueryOptions {
    limit: number;
    offset: number;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
    includeContext: boolean;
    includeRelations: boolean;
}
export interface ContextUpdate {
    updateId: string;
    type: 'file_change' | 'dependency_change' | 'structure_change' | 'metadata_change';
    changes: ChangeInfo[];
    timestamp: string;
    source: string;
}
export interface ChangeInfo {
    path: string;
    type: 'added' | 'modified' | 'deleted' | 'moved';
    content?: string;
    metadata?: any;
}
export interface ContextEngineResult {
    taskId: string;
    status: 'completed' | 'failed' | 'partial';
    context: EnhancedContext;
    graph: DependencyGraph;
    insights: ContextInsight[];
    recommendations: ContextRecommendation[];
    metrics: ContextMetrics;
}
export interface EnhancedContext {
    codebase: ProcessedCodebase;
    relationships: Relationship[];
    patterns: DetectedPattern[];
    knowledge: KnowledgeBase;
    temporal: TemporalData;
}
export interface ProcessedCodebase {
    structure: AnalyzedStructure;
    symbols: SymbolTable;
    dependencies: DependencyGraph;
    metrics: CodeMetrics;
}
export interface AnalyzedStructure {
    modules: ModuleInfo[];
    classes: ClassInfo[];
    functions: FunctionInfo[];
    interfaces: InterfaceInfo[];
    types: TypeInfo[];
}
export interface ModuleInfo {
    name: string;
    path: string;
    exports: string[];
    imports: ImportInfo[];
    dependencies: string[];
    complexity: number;
    maintainability: number;
}
export interface ClassInfo {
    name: string;
    module: string;
    methods: MethodInfo[];
    properties: PropertyInfo[];
    inheritance: string[];
    interfaces: string[];
    complexity: number;
}
export interface FunctionInfo {
    name: string;
    module: string;
    parameters: ParameterInfo[];
    returnType: string;
    complexity: number;
    usage: UsageInfo[];
}
export interface InterfaceInfo {
    name: string;
    module: string;
    properties: PropertyInfo[];
    methods: MethodSignature[];
    extends: string[];
}
export interface TypeInfo {
    name: string;
    module: string;
    definition: string;
    usage: UsageInfo[];
}
export interface ImportInfo {
    module: string;
    imports: string[];
    type: 'default' | 'named' | 'namespace' | 'side-effect';
}
export interface MethodInfo {
    name: string;
    parameters: ParameterInfo[];
    returnType: string;
    visibility: 'public' | 'private' | 'protected';
    static: boolean;
    complexity: number;
}
export interface PropertyInfo {
    name: string;
    type: string;
    visibility: 'public' | 'private' | 'protected';
    static: boolean;
    readonly: boolean;
}
export interface ParameterInfo {
    name: string;
    type: string;
    optional: boolean;
    default?: any;
}
export interface MethodSignature {
    name: string;
    parameters: ParameterInfo[];
    returnType: string;
}
export interface UsageInfo {
    file: string;
    line: number;
    context: string;
    type: 'call' | 'reference' | 'import' | 'extend' | 'implement';
}
export interface SymbolTable {
    symbols: Symbol[];
    scopes: Scope[];
    references: Reference[];
}
export interface Symbol {
    name: string;
    type: string;
    location: Location;
    scope: string;
    visibility: string;
}
export interface Scope {
    id: string;
    type: 'global' | 'module' | 'class' | 'function' | 'block';
    parent?: string;
    symbols: string[];
}
export interface Reference {
    symbol: string;
    location: Location;
    type: 'read' | 'write' | 'call';
}
export interface Location {
    file: string;
    line: number;
    column: number;
}
export interface DependencyGraph {
    nodes: GraphNode[];
    edges: GraphEdge[];
    clusters: GraphCluster[];
    metrics: GraphMetrics;
}
export interface GraphNode {
    id: string;
    type: 'module' | 'class' | 'function' | 'file' | 'package';
    name: string;
    metadata: any;
    metrics: NodeMetrics;
}
export interface GraphEdge {
    from: string;
    to: string;
    type: 'depends' | 'imports' | 'calls' | 'extends' | 'implements';
    weight: number;
    metadata: any;
}
export interface GraphCluster {
    id: string;
    name: string;
    nodes: string[];
    type: 'module' | 'feature' | 'layer' | 'domain';
}
export interface GraphMetrics {
    nodeCount: number;
    edgeCount: number;
    density: number;
    complexity: number;
    modularity: number;
}
export interface NodeMetrics {
    inDegree: number;
    outDegree: number;
    betweenness: number;
    closeness: number;
    pageRank: number;
}
export interface Relationship {
    id: string;
    type: 'dependency' | 'inheritance' | 'composition' | 'aggregation' | 'usage';
    source: string;
    target: string;
    strength: number;
    metadata: any;
}
export interface DetectedPattern {
    id: string;
    type: 'design_pattern' | 'anti_pattern' | 'architectural_pattern' | 'code_smell';
    name: string;
    description: string;
    locations: Location[];
    confidence: number;
    impact: 'positive' | 'negative' | 'neutral';
}
export interface KnowledgeBase {
    facts: KnowledgeFact[];
    rules: KnowledgeRule[];
    inferences: KnowledgeInference[];
}
export interface KnowledgeFact {
    id: string;
    subject: string;
    predicate: string;
    object: string;
    confidence: number;
    source: string;
}
export interface KnowledgeRule {
    id: string;
    condition: string;
    conclusion: string;
    confidence: number;
}
export interface KnowledgeInference {
    id: string;
    premises: string[];
    conclusion: string;
    confidence: number;
    reasoning: string;
}
export interface TemporalData {
    snapshots: TemporalSnapshot[];
    changes: TemporalChange[];
    trends: TemporalTrend[];
}
export interface TemporalSnapshot {
    timestamp: string;
    version: string;
    context: any;
    metrics: any;
}
export interface TemporalChange {
    timestamp: string;
    type: string;
    description: string;
    impact: string;
    metadata: any;
}
export interface TemporalTrend {
    metric: string;
    direction: 'increasing' | 'decreasing' | 'stable';
    rate: number;
    confidence: number;
}
export interface CodeMetrics {
    complexity: ComplexityMetrics;
    quality: QualityMetrics;
    maintainability: MaintainabilityMetrics;
    testability: TestabilityMetrics;
}
export interface ComplexityMetrics {
    cyclomatic: number;
    cognitive: number;
    halstead: HalsteadMetrics;
    nesting: number;
}
export interface HalsteadMetrics {
    vocabulary: number;
    length: number;
    difficulty: number;
    effort: number;
    bugs: number;
}
export interface QualityMetrics {
    duplication: number;
    coverage: number;
    debt: number;
    violations: number;
}
export interface MaintainabilityMetrics {
    index: number;
    changeability: number;
    stability: number;
    testability: number;
}
export interface TestabilityMetrics {
    coverage: number;
    testComplexity: number;
    mockability: number;
    isolation: number;
}
export interface ContextInsight {
    id: string;
    type: 'optimization' | 'risk' | 'opportunity' | 'pattern' | 'anomaly';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    confidence: number;
    recommendations: string[];
}
export interface ContextRecommendation {
    id: string;
    type: 'refactor' | 'optimize' | 'security' | 'performance' | 'maintainability';
    priority: 'high' | 'medium' | 'low';
    description: string;
    rationale: string;
    implementation: string;
    effort: string;
    impact: string;
}
export interface ContextMetrics {
    ingestionTime: number;
    analysisTime: number;
    graphBuildTime: number;
    memoryUsage: number;
    accuracy: number;
    completeness: number;
}
/**
 * Context Engine Agent - Deep codebase understanding and intelligence
 */
export declare class ContextEngineAgent extends BaseAgent {
    private readonly contextSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute context engine workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual context step with enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for context engine
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private ingestCodebaseWithMCP;
    private analyzeStructureWithMCP;
    private buildDependencyGraphWithMCP;
    private extractPatternsWithMCP;
    private buildKnowledgeBaseWithMCP;
    private createRelationshipsWithMCP;
    private generateInsightsWithMCP;
    private optimizeContextWithMCP;
    private updateTemporalDataWithMCP;
}
export { ContextEngineAgent as default };
//# sourceMappingURL=context-engine-agent.d.ts.map