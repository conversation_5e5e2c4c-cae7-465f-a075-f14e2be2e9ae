import { UnifiedContextEngine } from "../lib/unified-context-engine-v2"

/**
 * Example: How different agents use the enhanced context engine
 * This demonstrates the multi-agent capabilities of the universal context system
 */

// Example 1: Task Planner Agent using the context engine
export class TaskPlannerAgent {
  private contextEngine: UnifiedContextEngine
  private agentId: string

  constructor(contextEngine: UnifiedContextEngine) {
    this.contextEngine = contextEngine
    
    // Register with context engine
    this.agentId = this.contextEngine.registerAgent('planner', 'task-breakdown-001', {
      requiredCapabilities: ['task_breakdown', 'dependency_analysis', 'timeline_estimation'],
      contextFilters: ['requirements', 'architecture', 'constraints', 'timeline']
    })
  }

  async breakdownTasks(): Promise<any> {
    // Get filtered context specific to task planning
    const context = this.contextEngine.getAgentContext(this.agentId)
    
    // Task planner only gets what it needs:
    // - PRD requirements
    // - Architecture decisions
    // - Global constraints
    // - Timeline information
    
    console.log('Task Planner Context:', {
      hasRequirements: !!context.current.prd,
      hasArchitecture: !!context.current.techStack,
      hasConstraints: !!context.constraints,
      agentType: context.agentMetadata.agentType
    })

    // Perform task breakdown logic here...
    const tasks = this.generateTasks(context)
    
    // Update shared state for other agents
    this.contextEngine.updateSharedState('current_tasks', tasks, this.agentId)
    
    return tasks
  }

  private generateTasks(context: any): any[] {
    // Task generation logic based on filtered context
    return [
      { id: 1, title: 'Setup Project', type: 'setup' },
      { id: 2, title: 'Implement Core Features', type: 'development' }
    ]
  }

  cleanup(): void {
    this.contextEngine.unregisterAgent(this.agentId)
  }
}

// Example 2: Executor Agent using the context engine
export class ExecutorAgent {
  private contextEngine: ContextEngine
  private agentId: string

  constructor(contextEngine: ContextEngine) {
    this.contextEngine = contextEngine
    
    // Register with context engine
    this.agentId = this.contextEngine.registerAgent('executor', 'code-execution-001', {
      requiredCapabilities: ['code_generation', 'file_operations', 'tool_execution'],
      contextFilters: ['implementation_details', 'constraints']
    })
  }

  async executeTask(taskId: string): Promise<any> {
    // Get filtered context specific to code execution
    const context = this.contextEngine.getAgentContext(this.agentId)
    
    // Executor only gets what it needs:
    // - Implementation details (tasks, workflow, filesystem)
    // - Current constraints
    // - Active agent states
    
    console.log('Executor Context:', {
      hasTasks: !!context.current.tasks,
      hasFileSystem: !!context.current.filesystem,
      hasWorkflow: !!context.current.workflow,
      activeAgents: context.agentMetadata.sharedState.active_agents?.length || 0
    })

    // Execute the specific task
    const result = await this.performExecution(taskId, context)
    
    // Update execution status
    this.contextEngine.updateSharedState('execution_status', {
      taskId,
      status: 'completed',
      result
    }, this.agentId)
    
    return result
  }

  private async performExecution(taskId: string, context: any): Promise<any> {
    // Code execution logic based on filtered context
    return { taskId, status: 'executed', files: ['src/component.tsx'] }
  }

  cleanup(): void {
    this.contextEngine.unregisterAgent(this.agentId)
  }
}

// Example 3: Workflow Engine using the context engine
export class WorkflowEngine {
  private contextEngine: ContextEngine
  private agentId: string

  constructor(contextEngine: ContextEngine) {
    this.contextEngine = contextEngine
    
    // Register with context engine
    this.agentId = this.contextEngine.registerAgent('workflow', 'orchestration-001', {
      requiredCapabilities: ['flow_orchestration', 'agent_coordination', 'state_management'],
      contextFilters: ['agent_states', 'timeline', 'constraints']
    })
  }

  async orchestrateWorkflow(): Promise<any> {
    // Get context for workflow orchestration
    const context = this.contextEngine.getAgentContext(this.agentId)
    
    // Workflow engine gets:
    // - Agent states and handoff points
    // - Execution timeline
    // - Global constraints
    
    console.log('Workflow Engine Context:', {
      activeAgents: context.agentStates?.length || 0,
      hasTimeline: !!context.timeline,
      hasConstraints: !!context.constraints
    })

    // Orchestrate the workflow
    const workflowPlan = this.createWorkflowPlan(context)
    
    // Update workflow state
    this.contextEngine.updateSharedState('workflow_plan', workflowPlan, this.agentId)
    this.contextEngine.updateSharedState('workflow_status', 'active', this.agentId)
    
    return workflowPlan
  }

  async handoffToAgent(fromAgentId: string, toAgentType: string, data: any): Promise<void> {
    // Record agent handoff
    const handoffs = this.contextEngine.getAgentContext(this.agentId).agentMetadata.sharedState.agent_handoffs || []
    handoffs.push({
      from: fromAgentId,
      to: toAgentType,
      data,
      timestamp: new Date().toISOString()
    })
    
    this.contextEngine.updateSharedState('agent_handoffs', handoffs, this.agentId)
  }

  private createWorkflowPlan(context: any): any {
    return {
      phases: ['planning', 'execution', 'validation'],
      currentPhase: 'planning',
      agentSequence: ['project-planner', 'planner', 'executor']
    }
  }

  cleanup(): void {
    this.contextEngine.unregisterAgent(this.agentId)
  }
}

// Example 4: How to use the enhanced context engine in a multi-agent scenario
export async function demonstrateMultiAgentContext(): Promise<void> {
  // Initialize context engine with project data
  const contextEngine = new ContextEngine({
    originalPrompt: "Build a task management app",
    projectType: "Web Application",
    features: ["User Authentication", "Task CRUD", "Dashboard"],
    clarifications: { platform: "web", users: "teams" },
    summary: "A collaborative task management application",
    techStack: { frontend: "React", backend: "Node.js", database: "PostgreSQL" },
    prd: { features: ["Login", "Task Management", "Team Collaboration"] },
    wireframes: [{ name: "Dashboard", type: "page" }],
    filesystem: { structure: "modern", folders: ["src", "components"] },
    workflow: { processes: ["authentication", "task_management"] },
    tasks: []
  })

  // Simulate multi-agent workflow
  console.log('=== Multi-Agent Context Demo ===')
  
  // 1. Workflow Engine starts orchestration
  const workflowEngine = new WorkflowEngine(contextEngine)
  await workflowEngine.orchestrateWorkflow()
  
  // 2. Task Planner breaks down work
  const taskPlanner = new TaskPlannerAgent(contextEngine)
  const tasks = await taskPlanner.breakdownTasks()
  
  // 3. Executor performs implementation
  const executor = new ExecutorAgent(contextEngine)
  await executor.executeTask(tasks[0]?.id)
  
  // 4. Workflow Engine manages handoffs
  await workflowEngine.handoffToAgent(taskPlanner.agentId, 'executor', { tasks })
  
  // 5. Check final shared state
  const finalContext = contextEngine.exportContext()
  console.log('Final Context Metadata:', finalContext.metadata)
  
  // Cleanup
  workflowEngine.cleanup()
  taskPlanner.cleanup()
  executor.cleanup()
  
  console.log('=== Demo Complete ===')
}

// Example usage:
// demonstrateMultiAgentContext().catch(console.error)
