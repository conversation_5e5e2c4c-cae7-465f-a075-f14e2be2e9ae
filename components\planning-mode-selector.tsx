"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Brain,
  Users,
  Zap,
  CheckCircle,
  Clock,
  Settings,
  Play,
  Pause,
  ArrowRight,
  Bot,
  User,
  Lightbulb,
  Target,
  FileText,
  ListTodo
} from "lucide-react"

export type PlanningMode = "autonomous" | "copilot"

export interface PlanningSession {
  id: string
  mode: PlanningMode
  status: "idle" | "planning" | "paused" | "completed" | "error"
  progress: number
  currentPhase: "requirements" | "design" | "tasks" | "completed"
  startedAt?: Date
  completedAt?: Date
  projectName?: string
}

interface PlanningModeSelectorProps {
  currentMode: PlanningMode
  onModeChange: (mode: PlanningMode) => void
  session?: PlanningSession
  onStartPlanning: (mode: PlanningMode) => void
  onPausePlanning: () => void
  onResumeePlanning: () => void
  onStopPlanning: () => void
  className?: string
}

export default function PlanningModeSelector({
  currentMode,
  onModeChange,
  session,
  onStartPlanning,
  onPausePlanning,
  onResumeePlanning,
  onStopPlanning,
  className = ""
}: PlanningModeSelectorProps) {
  const [selectedMode, setSelectedMode] = useState<PlanningMode>(currentMode)

  useEffect(() => {
    setSelectedMode(currentMode)
  }, [currentMode])

  const handleModeSelect = (mode: PlanningMode) => {
    setSelectedMode(mode)
    onModeChange(mode)
  }

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case "requirements":
        return <FileText className="w-4 h-4" />
      case "design":
        return <Lightbulb className="w-4 h-4" />
      case "tasks":
        return <ListTodo className="w-4 h-4" />
      case "completed":
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Target className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planning":
        return "bg-blue-500"
      case "paused":
        return "bg-yellow-500"
      case "completed":
        return "bg-green-500"
      case "error":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const formatPhase = (phase: string) => {
    return phase.charAt(0).toUpperCase() + phase.slice(1)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Mode Selection */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Planning Mode Selection
          </CardTitle>
          <CardDescription className="text-[#666]">
            Choose how you want to approach project planning and development
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Mode Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Autonomous Mode */}
            <Card 
              className={`cursor-pointer transition-all border-2 ${
                selectedMode === "autonomous" 
                  ? "border-blue-500 bg-blue-500/10" 
                  : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
              }`}
              onClick={() => handleModeSelect("autonomous")}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-600/20 rounded-lg">
                      <Bot className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">Autonomous Mode</CardTitle>
                      <CardDescription className="text-[#666] text-sm">
                        AI handles everything automatically
                      </CardDescription>
                    </div>
                  </div>
                  {selectedMode === "autonomous" && (
                    <CheckCircle className="w-5 h-5 text-blue-400" />
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2 text-sm text-[#888]">
                  <li className="flex items-center gap-2">
                    <Zap className="w-3 h-3 text-green-400" />
                    Fully automated planning process
                  </li>
                  <li className="flex items-center gap-2">
                    <Brain className="w-3 h-3 text-purple-400" />
                    AI generates requirements, design, and tasks
                  </li>
                  <li className="flex items-center gap-2">
                    <Target className="w-3 h-3 text-orange-400" />
                    Minimal human intervention required
                  </li>
                  <li className="flex items-center gap-2">
                    <Clock className="w-3 h-3 text-blue-400" />
                    Fastest time to implementation
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Co-Pilot Mode */}
            <Card 
              className={`cursor-pointer transition-all border-2 ${
                selectedMode === "copilot" 
                  ? "border-green-500 bg-green-500/10" 
                  : "border-[#1a1a1a] bg-[#111111] hover:border-[#2a2a2a]"
              }`}
              onClick={() => handleModeSelect("copilot")}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-600/20 rounded-lg">
                      <Users className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">Co-Pilot Mode</CardTitle>
                      <CardDescription className="text-[#666] text-sm">
                        Collaborative planning with human oversight
                      </CardDescription>
                    </div>
                  </div>
                  {selectedMode === "copilot" && (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2 text-sm text-[#888]">
                  <li className="flex items-center gap-2">
                    <User className="w-3 h-3 text-blue-400" />
                    Human-in-the-loop collaboration
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-3 h-3 text-green-400" />
                    Review and approve each planning phase
                  </li>
                  <li className="flex items-center gap-2">
                    <Settings className="w-3 h-3 text-purple-400" />
                    Fine-tune requirements and design
                  </li>
                  <li className="flex items-center gap-2">
                    <Target className="w-3 h-3 text-orange-400" />
                    Higher accuracy and customization
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Mode Comparison */}
          <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
            <h4 className="text-white font-medium mb-3">Mode Comparison</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-[#666] mb-2">Speed</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-full h-full bg-blue-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">Fast</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-2/3 h-full bg-green-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">Medium</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="text-[#666] mb-2">Control</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-1/3 h-full bg-blue-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">Low</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-full h-full bg-green-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">High</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="text-[#666] mb-2">Accuracy</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400">Autonomous</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-3/4 h-full bg-blue-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">Good</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-400">Co-Pilot</span>
                    <div className="flex items-center gap-1">
                      <div className="w-16 h-1 bg-[#1a1a1a] rounded">
                        <div className="w-full h-full bg-green-500 rounded"></div>
                      </div>
                      <span className="text-xs text-[#888]">Excellent</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Session Status */}
      {session && (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white flex items-center gap-2">
                  {getPhaseIcon(session.currentPhase)}
                  Planning Session
                </CardTitle>
                <CardDescription className="text-[#666]">
                  {session.projectName || "Current project"} • {formatPhase(session.currentPhase)} Phase
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${getStatusColor(session.status)}`} />
                <Badge className={`${getStatusColor(session.status)} text-white border-0 capitalize`}>
                  {session.status}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Progress */}
            <div>
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-[#666]">Overall Progress</span>
                <span className="text-white">{session.progress}%</span>
              </div>
              <Progress value={session.progress} className="h-2 bg-[#1a1a1a]" />
            </div>

            {/* Phase Indicators */}
            <div className="flex items-center justify-between">
              {["requirements", "design", "tasks", "completed"].map((phase, index) => (
                <div key={phase} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    session.currentPhase === phase 
                      ? "border-blue-500 bg-blue-500/20 text-blue-400" 
                      : index < ["requirements", "design", "tasks", "completed"].indexOf(session.currentPhase)
                        ? "border-green-500 bg-green-500/20 text-green-400"
                        : "border-[#2a2a2a] bg-[#1a1a1a] text-[#666]"
                  }`}>
                    {getPhaseIcon(phase)}
                  </div>
                  {index < 3 && (
                    <div className={`w-12 h-0.5 mx-2 ${
                      index < ["requirements", "design", "tasks", "completed"].indexOf(session.currentPhase)
                        ? "bg-green-500"
                        : "bg-[#2a2a2a]"
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Session Controls */}
            <Separator className="bg-[#1a1a1a]" />
            <div className="flex items-center justify-between">
              <div className="text-sm text-[#666]">
                {session.startedAt && (
                  <span>Started {session.startedAt.toLocaleTimeString()}</span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {session.status === "planning" && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={onPausePlanning}
                    className="bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]"
                  >
                    <Pause className="w-4 h-4 mr-2" />
                    Pause
                  </Button>
                )}
                {session.status === "paused" && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={onResumeePlanning}
                    className="bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Resume
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onStopPlanning}
                  className="bg-transparent border-red-600/30 text-red-400 hover:text-red-300 hover:bg-red-600/10"
                >
                  Stop Session
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Start Planning */}
      {!session || session.status === "idle" || session.status === "completed" ? (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center">
                <div className={`p-3 rounded-full ${
                  selectedMode === "autonomous" ? "bg-blue-600/20" : "bg-green-600/20"
                }`}>
                  {selectedMode === "autonomous" ? (
                    <Bot className={`w-8 h-8 text-blue-400`} />
                  ) : (
                    <Users className={`w-8 h-8 text-green-400`} />
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium text-white mb-2">
                  Ready to start {selectedMode === "autonomous" ? "autonomous" : "co-pilot"} planning
                </h3>
                <p className="text-sm text-[#666] mb-4">
                  {selectedMode === "autonomous" 
                    ? "AI will automatically generate requirements, design, and implementation tasks"
                    : "Work collaboratively with AI to refine each phase of the planning process"
                  }
                </p>
              </div>
              <Button
                onClick={() => onStartPlanning(selectedMode)}
                className={`${
                  selectedMode === "autonomous" 
                    ? "bg-blue-600 hover:bg-blue-700" 
                    : "bg-green-600 hover:bg-green-700"
                } text-white px-6`}
              >
                <Play className="w-4 h-4 mr-2" />
                Start {selectedMode === "autonomous" ? "Autonomous" : "Co-Pilot"} Planning
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : null}
    </div>
  )
}