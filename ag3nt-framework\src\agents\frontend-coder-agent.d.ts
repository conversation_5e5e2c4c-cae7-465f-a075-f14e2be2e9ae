/**
 * AG3NT Framework - Frontend Coder Agent
 *
 * Specialized agent for frontend development tasks.
 * Handles UI/UX implementation, component development, and frontend architecture.
 *
 * Features:
 * - React/Vue/Angular component development
 * - UI/UX implementation from designs
 * - Frontend architecture and state management
 * - Responsive design and accessibility
 * - Performance optimization
 * - Testing and quality assurance
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface FrontendCoderInput {
    task: FrontendTask;
    design: DesignSpecification;
    requirements: FrontendRequirements;
    codebase: CodebaseContext;
}
export interface FrontendTask {
    taskId: string;
    type: 'component' | 'page' | 'feature' | 'refactor' | 'optimization' | 'testing';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    acceptanceCriteria: string[];
    technicalRequirements: string[];
}
export interface DesignSpecification {
    wireframes?: any[];
    mockups?: any[];
    designSystem?: DesignSystem;
    userFlows?: UserFlow[];
    responsiveBreakpoints?: ResponsiveBreakpoint[];
}
export interface DesignSystem {
    colors: ColorPalette;
    typography: Typography;
    spacing: SpacingScale;
    components: ComponentLibrary;
    icons: IconLibrary;
}
export interface ColorPalette {
    primary: string[];
    secondary: string[];
    neutral: string[];
    semantic: {
        success: string;
        warning: string;
        error: string;
        info: string;
    };
}
export interface Typography {
    fontFamilies: string[];
    fontSizes: number[];
    fontWeights: number[];
    lineHeights: number[];
}
export interface SpacingScale {
    unit: number;
    scale: number[];
}
export interface ComponentLibrary {
    [componentName: string]: ComponentSpec;
}
export interface ComponentSpec {
    name: string;
    variants: string[];
    props: ComponentProp[];
    states: string[];
    examples: any[];
}
export interface ComponentProp {
    name: string;
    type: string;
    required: boolean;
    default?: any;
    description: string;
}
export interface IconLibrary {
    style: 'outline' | 'filled' | 'duotone';
    icons: string[];
}
export interface UserFlow {
    flowId: string;
    name: string;
    steps: UserFlowStep[];
    entryPoints: string[];
    exitPoints: string[];
}
export interface UserFlowStep {
    stepId: string;
    action: string;
    screen: string;
    interactions: string[];
    validations: string[];
}
export interface ResponsiveBreakpoint {
    name: string;
    minWidth: number;
    maxWidth?: number;
    columns: number;
    gutters: number;
}
export interface FrontendRequirements {
    framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla';
    language: 'typescript' | 'javascript';
    styling: 'css' | 'scss' | 'styled-components' | 'tailwind' | 'emotion';
    stateManagement?: 'redux' | 'zustand' | 'mobx' | 'context' | 'pinia';
    testing: 'jest' | 'vitest' | 'cypress' | 'playwright';
    bundler: 'webpack' | 'vite' | 'parcel' | 'rollup';
    accessibility: boolean;
    performance: PerformanceRequirements;
    browser: BrowserSupport;
}
export interface PerformanceRequirements {
    targetLCP: number;
    targetFID: number;
    targetCLS: number;
    bundleSize: number;
    codesplitting: boolean;
    lazyLoading: boolean;
}
export interface BrowserSupport {
    chrome: string;
    firefox: string;
    safari: string;
    edge: string;
    mobile: boolean;
}
export interface CodebaseContext {
    projectStructure: ProjectStructure;
    existingComponents: ExistingComponent[];
    dependencies: Dependency[];
    buildConfig: BuildConfig;
}
export interface ProjectStructure {
    srcDirectory: string;
    componentsDirectory: string;
    pagesDirectory: string;
    stylesDirectory: string;
    assetsDirectory: string;
    testsDirectory: string;
}
export interface ExistingComponent {
    name: string;
    path: string;
    props: ComponentProp[];
    dependencies: string[];
    usage: string[];
}
export interface Dependency {
    name: string;
    version: string;
    type: 'dependency' | 'devDependency' | 'peerDependency';
}
export interface BuildConfig {
    entry: string;
    output: string;
    publicPath: string;
    devServer: any;
    optimization: any;
}
export interface FrontendCoderResult {
    taskId: string;
    status: 'completed' | 'failed' | 'needs_review';
    deliverables: Deliverable[];
    codeChanges: CodeChange[];
    testResults: TestResult[];
    performanceMetrics: PerformanceMetrics;
    accessibilityReport: AccessibilityReport;
    documentation: Documentation[];
}
export interface Deliverable {
    type: 'component' | 'page' | 'style' | 'test' | 'documentation';
    name: string;
    path: string;
    content: string;
    dependencies: string[];
}
export interface CodeChange {
    file: string;
    type: 'create' | 'modify' | 'delete';
    changes: string;
    linesAdded: number;
    linesRemoved: number;
}
export interface TestResult {
    testFile: string;
    testSuite: string;
    passed: number;
    failed: number;
    coverage: number;
    duration: number;
}
export interface PerformanceMetrics {
    bundleSize: number;
    loadTime: number;
    renderTime: number;
    interactiveTime: number;
    memoryUsage: number;
}
export interface AccessibilityReport {
    score: number;
    violations: AccessibilityViolation[];
    recommendations: string[];
}
export interface AccessibilityViolation {
    rule: string;
    severity: 'error' | 'warning' | 'info';
    element: string;
    description: string;
    fix: string;
}
export interface Documentation {
    type: 'component' | 'api' | 'usage' | 'changelog';
    title: string;
    content: string;
    examples: any[];
}
/**
 * Frontend Coder Agent - Specialized frontend development
 */
export declare class FrontendCoderAgent extends BaseAgent {
    private readonly codingSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute frontend coding workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual coding step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for frontend development
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzeRequirementsWithMCP;
    private planImplementationWithMCP;
    private setupEnvironmentWithMCP;
    private developComponentsWithMCP;
    private implementStylingWithMCP;
    private addInteractionsWithMCP;
    private optimizePerformanceWithMCP;
    private ensureAccessibilityWithMCP;
    private writeTestsWithMCP;
    private documentCodeWithMCP;
}
export { FrontendCoderAgent as default };
//# sourceMappingURL=frontend-coder-agent.d.ts.map