"use strict";
/**
 * AG3NT Framework - Advanced Features
 *
 * Export all advanced framework features that differentiate AG3NT
 * from competitors like CrewAI and LangGraph.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedFeaturesManager = exports.AdvancedMonitoringSystem = exports.AgentMarketplace = exports.DynamicOptimizationSystem = exports.RealtimeCollaborationSystem = exports.TemporalContextDatabase = exports.AdaptiveLearningSystem = void 0;
// Adaptive Learning System
var adaptive_learning_system_1 = require("./adaptive-learning-system");
Object.defineProperty(exports, "AdaptiveLearningSystem", { enumerable: true, get: function () { return __importDefault(adaptive_learning_system_1).default; } });
// Temporal Context Database
var temporal_context_database_1 = require("./temporal-context-database");
Object.defineProperty(exports, "TemporalContextDatabase", { enumerable: true, get: function () { return __importDefault(temporal_context_database_1).default; } });
// Real-time Collaboration System
var realtime_collaboration_system_1 = require("./realtime-collaboration-system");
Object.defineProperty(exports, "RealtimeCollaborationSystem", { enumerable: true, get: function () { return __importDefault(realtime_collaboration_system_1).default; } });
// Dynamic Optimization System
var dynamic_optimization_system_1 = require("./dynamic-optimization-system");
Object.defineProperty(exports, "DynamicOptimizationSystem", { enumerable: true, get: function () { return __importDefault(dynamic_optimization_system_1).default; } });
// Agent Marketplace
var agent_marketplace_1 = require("./agent-marketplace");
Object.defineProperty(exports, "AgentMarketplace", { enumerable: true, get: function () { return __importDefault(agent_marketplace_1).default; } });
// Advanced Monitoring System
var advanced_monitoring_system_1 = require("./advanced-monitoring-system");
Object.defineProperty(exports, "AdvancedMonitoringSystem", { enumerable: true, get: function () { return __importDefault(advanced_monitoring_system_1).default; } });
/**
 * Advanced Features Manager
 *
 * Centralized manager for all advanced framework features
 */
class AdvancedFeaturesManager {
    constructor(config = {}) {
        this.config = config;
    }
    /**
     * Initialize all advanced features
     */
    async initialize() {
        console.log('🚀 Initializing Advanced Features...');
        // Initialize Adaptive Learning System
        if (this.config.adaptiveLearning?.enabled !== false) {
            this.adaptiveLearning = new adaptive_learning_system_2.default(this.config.adaptiveLearning);
        }
        // Initialize Temporal Context Database
        if (this.config.temporalDatabase?.enabled !== false) {
            this.temporalDatabase = new temporal_context_database_2.default(this.config.temporalDatabase);
        }
        // Initialize Real-time Collaboration
        if (this.config.collaboration?.enabled !== false) {
            this.collaboration = new realtime_collaboration_system_2.default(this.config.collaboration);
        }
        // Initialize Dynamic Optimization
        if (this.config.optimization?.enabled !== false) {
            this.optimization = new dynamic_optimization_system_2.default(this.config.optimization);
        }
        // Initialize Agent Marketplace
        if (this.config.marketplace?.enabled !== false) {
            this.marketplace = new agent_marketplace_2.default(this.config.marketplace);
        }
        // Initialize Advanced Monitoring
        if (this.config.monitoring?.enabled !== false) {
            this.monitoring = new advanced_monitoring_system_2.default(this.config.monitoring);
        }
        console.log('✅ Advanced Features initialized');
    }
    /**
     * Get adaptive learning system
     */
    getAdaptiveLearning() {
        return this.adaptiveLearning;
    }
    /**
     * Get temporal database
     */
    getTemporalDatabase() {
        return this.temporalDatabase;
    }
    /**
     * Get collaboration system
     */
    getCollaboration() {
        return this.collaboration;
    }
    /**
     * Get optimization system
     */
    getOptimization() {
        return this.optimization;
    }
    /**
     * Get marketplace
     */
    getMarketplace() {
        return this.marketplace;
    }
    /**
     * Get monitoring system
     */
    getMonitoring() {
        return this.monitoring;
    }
    /**
     * Record agent execution for learning and monitoring
     */
    recordAgentExecution(agentId, executionData) {
        // Record for adaptive learning
        if (this.adaptiveLearning && executionData.learningRecord) {
            this.adaptiveLearning.recordExecution(executionData.learningRecord);
        }
        // Store context snapshot
        if (this.temporalDatabase && executionData.contextSnapshot) {
            this.temporalDatabase.storeContextSnapshot(executionData.contextSnapshot);
        }
        // Record performance metrics
        if (this.monitoring && executionData.metrics) {
            for (const [metric, value] of Object.entries(executionData.metrics)) {
                this.monitoring.recordMetric(metric, value, { agent_id: agentId });
            }
        }
        // Record optimization performance
        if (this.optimization && executionData.performance) {
            this.optimization.recordPerformance(agentId, executionData.performance);
        }
        // Record marketplace plugin usage
        if (this.marketplace && executionData.pluginUsage) {
            for (const usage of executionData.pluginUsage) {
                this.marketplace.recordPluginExecution(usage.pluginId, usage.executionTime, usage.success, usage.dataProcessed);
            }
        }
    }
    /**
     * Get comprehensive insights across all systems
     */
    async getComprehensiveInsights(agentId) {
        const insights = {
            learning: [],
            optimization: [],
            performance: [],
            collaboration: [],
            marketplace: []
        };
        // Get learning insights
        if (this.adaptiveLearning) {
            insights.learning = this.adaptiveLearning.getLearningInsights(agentId);
        }
        // Get optimization recommendations
        if (this.optimization && agentId) {
            insights.optimization = this.optimization.getOptimizationRecommendations(agentId);
        }
        // Get performance insights
        if (this.monitoring) {
            insights.performance = await this.monitoring.generateInsights();
        }
        // Get collaboration metrics
        if (this.collaboration) {
            // Would get collaboration insights if available
        }
        // Get marketplace insights
        if (this.marketplace) {
            // Would get marketplace insights if available
        }
        return insights;
    }
    /**
     * Shutdown all advanced features
     */
    async shutdown() {
        console.log('🔄 Shutting down Advanced Features...');
        const shutdownPromises = [];
        if (this.adaptiveLearning) {
            shutdownPromises.push(this.adaptiveLearning.shutdown());
        }
        if (this.temporalDatabase) {
            shutdownPromises.push(this.temporalDatabase.shutdown());
        }
        if (this.collaboration) {
            shutdownPromises.push(this.collaboration.shutdown());
        }
        if (this.optimization) {
            shutdownPromises.push(this.optimization.shutdown());
        }
        if (this.marketplace) {
            shutdownPromises.push(this.marketplace.shutdown());
        }
        if (this.monitoring) {
            shutdownPromises.push(this.monitoring.shutdown());
        }
        await Promise.all(shutdownPromises);
        console.log('✅ Advanced Features shutdown complete');
    }
}
exports.AdvancedFeaturesManager = AdvancedFeaturesManager;
const adaptive_learning_system_2 = __importDefault(require("./adaptive-learning-system"));
const temporal_context_database_2 = __importDefault(require("./temporal-context-database"));
const realtime_collaboration_system_2 = __importDefault(require("./realtime-collaboration-system"));
const dynamic_optimization_system_2 = __importDefault(require("./dynamic-optimization-system"));
const agent_marketplace_2 = __importDefault(require("./agent-marketplace"));
const advanced_monitoring_system_2 = __importDefault(require("./advanced-monitoring-system"));
exports.default = AdvancedFeaturesManager;
//# sourceMappingURL=index.js.map