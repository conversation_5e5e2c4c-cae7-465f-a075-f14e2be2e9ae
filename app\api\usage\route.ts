import { NextResponse } from 'next/server'
import { usageTracker } from '@/lib/usage-tracker'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')
    const format = searchParams.get('format') || 'json'

    // Get comprehensive usage statistics
    const usageStats = usageTracker.getUsageStats(days)
    const budgetStatus = usageTracker.getBudgetStatus()
    const modelMetrics = usageTracker.getModelMetrics() as Map<string, any>
    const recommendations = usageTracker.getModelRecommendations()

    const response = {
      usage: usageStats,
      budget: budgetStatus,
      metrics: Object.fromEntries(modelMetrics),
      recommendations,
      period: {
        days,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString()
      }
    }

    // Export as CSV if requested
    if (format === 'csv') {
      const csvData = generateCSVReport(response)
      return new Response(csvData, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="ai-usage-report-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching usage statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch usage statistics' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const { action } = await request.json()

    switch (action) {
      case 'clear-old-data':
        const { daysToKeep = 90 } = await request.json()
        usageTracker.clearOldData(daysToKeep)
        return NextResponse.json({ success: true, message: `Cleared data older than ${daysToKeep} days` })

      case 'export-data':
        const exportData = usageTracker.exportUsageData()
        return NextResponse.json(exportData)

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error processing usage action:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}

function generateCSVReport(data: any): string {
  const lines = []
  
  // Header
  lines.push('AI Usage Report')
  lines.push(`Generated: ${new Date().toISOString()}`)
  lines.push(`Period: ${data.period.days} days`)
  lines.push('')

  // Budget Summary
  lines.push('Budget Summary')
  lines.push('Metric,Value')
  lines.push(`Monthly Budget,$${data.budget.monthlyBudget.toFixed(2)}`)
  lines.push(`Current Spend,$${data.budget.currentMonthSpend.toFixed(2)}`)
  lines.push(`Remaining Budget,$${data.budget.remainingBudget.toFixed(2)}`)
  lines.push(`Percent Used,${data.budget.percentUsed.toFixed(1)}%`)
  lines.push(`Daily Average,$${data.budget.dailyAverage.toFixed(2)}`)
  lines.push(`Projected Monthly,$${data.budget.projectedMonthlySpend.toFixed(2)}`)
  lines.push('')

  // Usage Summary
  lines.push('Usage Summary')
  lines.push('Metric,Value')
  lines.push(`Total Cost,$${data.usage.totalCost.toFixed(2)}`)
  lines.push(`Total Requests,${data.usage.totalRequests}`)
  lines.push(`Total Tokens,${data.usage.totalTokens}`)
  lines.push(`Average Latency,${data.usage.averageLatency.toFixed(0)}ms`)
  lines.push(`Success Rate,${(data.usage.successRate * 100).toFixed(1)}%`)
  lines.push('')

  // Top Models
  lines.push('Top Models by Cost')
  lines.push('Model,Usage Count,Total Cost')
  data.usage.topModels.forEach((model: any) => {
    lines.push(`${model.model},${model.usage},$${model.cost.toFixed(2)}`)
  })
  lines.push('')

  // Daily Breakdown
  lines.push('Daily Usage Breakdown')
  lines.push('Date,Requests,Cost')
  data.usage.dailyBreakdown.forEach((day: any) => {
    lines.push(`${day.date},${day.requests},$${day.cost.toFixed(2)}`)
  })

  return lines.join('\n')
}