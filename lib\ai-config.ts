import { openai } from '@ai-sdk/openai'
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'

// OpenRouter Model Definitions with comprehensive metadata
export interface OpenRouterModel {
  id: string
  name: string
  description: string
  pricing: {
    prompt: number
    completion: number
  }
  context_length: number
  architecture: {
    modality: string
    tokenizer: string
    instruct_type?: string
  }
  top_provider: {
    context_length: number
    max_completion_tokens: number
    is_moderated: boolean
  }
  per_request_limits?: {
    prompt_tokens: number
    completion_tokens: number
  }
}

// Comprehensive OpenRouter model catalog
export const openRouterModels: Record<string, OpenRouterModel> = {
  // OpenAI Models via OpenRouter
  'openai/gpt-4o': {
    id: 'openai/gpt-4o',
    name: 'GPT-4o',
    description: 'OpenAI\'s flagship model with vision capabilities',
    pricing: { prompt: 0.005, completion: 0.015 },
    context_length: 128000,
    architecture: { modality: 'text+vision', tokenizer: 'cl100k_base' },
    top_provider: { context_length: 128000, max_completion_tokens: 4096, is_moderated: true }
  },
  'openai/gpt-4o-mini': {
    id: 'openai/gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: 'Faster, cheaper GPT-4o with vision capabilities',
    pricing: { prompt: 0.00015, completion: 0.0006 },
    context_length: 128000,
    architecture: { modality: 'text+vision', tokenizer: 'cl100k_base' },
    top_provider: { context_length: 128000, max_completion_tokens: 16384, is_moderated: true }
  },
  'openai/gpt-4-turbo': {
    id: 'openai/gpt-4-turbo',
    name: 'GPT-4 Turbo',
    description: 'Latest GPT-4 model with improved performance',
    pricing: { prompt: 0.01, completion: 0.03 },
    context_length: 128000,
    architecture: { modality: 'text+vision', tokenizer: 'cl100k_base' },
    top_provider: { context_length: 128000, max_completion_tokens: 4096, is_moderated: true }
  },
  
  // Anthropic Models via OpenRouter
  'anthropic/claude-3.5-sonnet': {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    description: 'Anthropic\'s most capable model with excellent reasoning',
    pricing: { prompt: 0.003, completion: 0.015 },
    context_length: 200000,
    architecture: { modality: 'text+vision', tokenizer: 'claude' },
    top_provider: { context_length: 200000, max_completion_tokens: 8192, is_moderated: false }
  },
  'anthropic/claude-3.5-haiku': {
    id: 'anthropic/claude-3.5-haiku',
    name: 'Claude 3.5 Haiku',
    description: 'Fast and efficient Claude model for quick tasks',
    pricing: { prompt: 0.0008, completion: 0.004 },
    context_length: 200000,
    architecture: { modality: 'text+vision', tokenizer: 'claude' },
    top_provider: { context_length: 200000, max_completion_tokens: 8192, is_moderated: false }
  },
  
  // Meta Llama Models
  'meta-llama/llama-3.1-405b-instruct': {
    id: 'meta-llama/llama-3.1-405b-instruct',
    name: 'Llama 3.1 405B Instruct',
    description: 'Meta\'s largest and most capable open-source model',
    pricing: { prompt: 0.005, completion: 0.005 },
    context_length: 131072,
    architecture: { modality: 'text', tokenizer: 'llama3', instruct_type: 'llama3' },
    top_provider: { context_length: 131072, max_completion_tokens: 4096, is_moderated: false }
  },
  'meta-llama/llama-3.1-70b-instruct': {
    id: 'meta-llama/llama-3.1-70b-instruct',
    name: 'Llama 3.1 70B Instruct',
    description: 'High-performance open-source model with great reasoning',
    pricing: { prompt: 0.0009, completion: 0.0009 },
    context_length: 131072,
    architecture: { modality: 'text', tokenizer: 'llama3', instruct_type: 'llama3' },
    top_provider: { context_length: 131072, max_completion_tokens: 4096, is_moderated: false }
  },
  'meta-llama/llama-3.1-8b-instruct': {
    id: 'meta-llama/llama-3.1-8b-instruct',
    name: 'Llama 3.1 8B Instruct',
    description: 'Efficient open-source model for general tasks',
    pricing: { prompt: 0.00018, completion: 0.00018 },
    context_length: 131072,
    architecture: { modality: 'text', tokenizer: 'llama3', instruct_type: 'llama3' },
    top_provider: { context_length: 131072, max_completion_tokens: 4096, is_moderated: false }
  },
  
  // Google Models via OpenRouter
  'google/gemini-pro-1.5': {
    id: 'google/gemini-pro-1.5',
    name: 'Gemini Pro 1.5',
    description: 'Google\'s advanced multimodal model',
    pricing: { prompt: 0.0025, completion: 0.0075 },
    context_length: 2097152,
    architecture: { modality: 'text+vision', tokenizer: 'gemini' },
    top_provider: { context_length: 2097152, max_completion_tokens: 8192, is_moderated: true }
  },
  'google/gemini-flash-1.5': {
    id: 'google/gemini-flash-1.5',
    name: 'Gemini Flash 1.5',
    description: 'Fast and efficient Google model',
    pricing: { prompt: 0.00075, completion: 0.003 },
    context_length: 1048576,
    architecture: { modality: 'text+vision', tokenizer: 'gemini' },
    top_provider: { context_length: 1048576, max_completion_tokens: 8192, is_moderated: true }
  },
  
  // Specialized Models
  'deepseek/deepseek-coder': {
    id: 'deepseek/deepseek-coder',
    name: 'DeepSeek Coder',
    description: 'Specialized coding model with excellent programming capabilities',
    pricing: { prompt: 0.00014, completion: 0.00028 },
    context_length: 16384,
    architecture: { modality: 'text', tokenizer: 'deepseek', instruct_type: 'deepseek' },
    top_provider: { context_length: 16384, max_completion_tokens: 4096, is_moderated: false }
  },
  'qwen/qwen-2.5-coder-32b-instruct': {
    id: 'qwen/qwen-2.5-coder-32b-instruct',
    name: 'Qwen 2.5 Coder 32B',
    description: 'Advanced coding model with strong programming abilities',
    pricing: { prompt: 0.0003, completion: 0.0003 },
    context_length: 32768,
    architecture: { modality: 'text', tokenizer: 'qwen', instruct_type: 'chatml' },
    top_provider: { context_length: 32768, max_completion_tokens: 8192, is_moderated: false }
  }
}

// AI Provider Configuration with OpenRouter as primary
export const aiProviders = {
  openrouter: {
    provider: openai,
    name: 'OpenRouter',
    description: 'Access to multiple AI models through OpenRouter',
    models: Object.fromEntries(
      Object.entries(openRouterModels).map(([key, model]) => [
        key.replace('/', '-'),
        openai(model.id, {
          baseURL: 'https://openrouter.ai/api/v1',
          apiKey: process.env.OPENROUTER_API_KEY,
          headers: {
            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
            'X-Title': 'Autonomous AI Development Ecosystem'
          }
        })
      ])
    )
  },
  openai: {
    provider: openai,
    name: 'OpenAI',
    description: 'Direct OpenAI API access',
    models: {
      'gpt-4o': openai('gpt-4o'),
      'gpt-4o-mini': openai('gpt-4o-mini'),
      'gpt-4-turbo': openai('gpt-4-turbo'),
      'gpt-3.5-turbo': openai('gpt-3.5-turbo'),
    }
  },
  anthropic: {
    provider: anthropic,
    name: 'Anthropic',
    description: 'Direct Anthropic API access',
    models: {
      'claude-3-5-sonnet': anthropic('claude-3-5-sonnet-20241022'),
      'claude-3-5-haiku': anthropic('claude-3-5-haiku-20241022'),
      'claude-3-opus': anthropic('claude-3-opus-20240229'),
    }
  },
  google: {
    provider: google,
    name: 'Google',
    description: 'Direct Google AI API access',
    models: {
      'gemini-1.5-pro': google('gemini-1.5-pro'),
      'gemini-1.5-flash': google('gemini-1.5-flash'),
      'gemini-pro': google('gemini-pro'),
    }
  }
}

// Model performance metrics interface
export interface ModelMetrics {
  averageLatency: number
  successRate: number
  costPerToken: number
  totalRequests: number
  totalTokens: number
  totalCost: number
  lastUsed: Date
  qualityScore?: number
}

// Usage tracking interface
export interface UsageStats {
  provider: string
  model: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost: number
  timestamp: Date
  latency: number
  success: boolean
}

// Default configuration with OpenRouter as primary
export const defaultConfig = {
  provider: process.env.DEFAULT_AI_PROVIDER || 'openrouter',
  model: process.env.DEFAULT_AI_MODEL || 'openai-gpt-4o-mini',
  maxTokens: parseInt(process.env.MAX_TOKENS_PER_REQUEST || '4000'),
  maxRequestsPerMinute: parseInt(process.env.MAX_REQUESTS_PER_MINUTE || '10'),
  fallbackProvider: 'openai',
  fallbackModel: 'gpt-4o-mini'
}

// Get AI model instance with fallback support
export function getAIModel(provider: string = defaultConfig.provider, modelName: string = defaultConfig.model) {
  const providerConfig = aiProviders[provider as keyof typeof aiProviders]
  if (!providerConfig) {
    console.warn(`Unsupported AI provider: ${provider}, falling back to ${defaultConfig.fallbackProvider}`)
    return getAIModel(defaultConfig.fallbackProvider, defaultConfig.fallbackModel)
  }

  const model = providerConfig.models[modelName as keyof typeof providerConfig.models]
  if (!model) {
    console.warn(`Unsupported model: ${modelName} for provider: ${provider}, falling back to default`)
    const fallbackModel = Object.keys(providerConfig.models)[0]
    return providerConfig.models[fallbackModel as keyof typeof providerConfig.models]
  }

  return model
}

// Get model metadata for OpenRouter models
export function getModelMetadata(modelId: string): OpenRouterModel | null {
  return openRouterModels[modelId] || null
}

// Calculate cost for a request
export function calculateCost(modelId: string, promptTokens: number, completionTokens: number): number {
  const metadata = getModelMetadata(modelId)
  if (!metadata) return 0
  
  const promptCost = (promptTokens / 1000) * metadata.pricing.prompt
  const completionCost = (completionTokens / 1000) * metadata.pricing.completion
  return promptCost + completionCost
}

// Enhanced available models with metadata
export const availableModels = Object.entries(aiProviders).flatMap(([providerName, config]) =>
  Object.keys(config.models).map(modelName => {
    const originalModelId = providerName === 'openrouter' 
      ? modelName.replace('-', '/') 
      : `${providerName}/${modelName}`
    const metadata = getModelMetadata(originalModelId)
    
    return {
      provider: providerName,
      model: modelName,
      displayName: metadata?.name || `${config.name} - ${modelName}`,
      description: metadata?.description || `${config.description}`,
      value: `${providerName}:${modelName}`,
      pricing: metadata?.pricing,
      contextLength: metadata?.context_length,
      modality: metadata?.architecture.modality || 'text',
      isRecommended: providerName === 'openrouter' && ['openai-gpt-4o-mini', 'anthropic-claude-3.5-haiku'].includes(modelName),
      category: getModelCategory(modelName, metadata)
    }
  })
).sort((a, b) => {
  // Sort by: recommended first, then by provider (openrouter first), then by name
  if (a.isRecommended && !b.isRecommended) return -1
  if (!a.isRecommended && b.isRecommended) return 1
  if (a.provider === 'openrouter' && b.provider !== 'openrouter') return -1
  if (a.provider !== 'openrouter' && b.provider === 'openrouter') return 1
  return a.displayName.localeCompare(b.displayName)
})

// Categorize models for better UI organization
function getModelCategory(modelName: string, metadata: OpenRouterModel | null): string {
  if (!metadata) return 'general'
  
  if (modelName.includes('coder') || modelName.includes('code')) return 'coding'
  if (metadata.architecture.modality.includes('vision')) return 'multimodal'
  if (metadata.context_length > 100000) return 'long-context'
  if (metadata.pricing.prompt < 0.001) return 'efficient'
  return 'general'
}

// Get recommended models for different use cases
export function getRecommendedModels() {
  return {
    coding: availableModels.filter(m => m.category === 'coding' || 
      ['deepseek-deepseek-coder', 'qwen-qwen-2.5-coder-32b-instruct', 'openai-gpt-4o'].includes(m.model)),
    general: availableModels.filter(m => m.isRecommended),
    efficient: availableModels.filter(m => m.category === 'efficient'),
    multimodal: availableModels.filter(m => m.category === 'multimodal'),
    longContext: availableModels.filter(m => m.category === 'long-context')
  }
}

// Validate environment variables with comprehensive checks
export function validateEnvironment() {
  const checks = {
    openrouter: !!process.env.OPENROUTER_API_KEY,
    openai: !!process.env.OPENAI_API_KEY,
    anthropic: !!process.env.ANTHROPIC_API_KEY,
    google: !!process.env.GOOGLE_GENERATIVE_AI_API_KEY
  }
  
  const availableProviders = Object.entries(checks)
    .filter(([_, available]) => available)
    .map(([provider]) => provider)
  
  if (availableProviders.length === 0) {
    console.error('No AI provider API keys found! Please set at least one API key.')
    return false
  }
  
  if (!checks.openrouter) {
    console.warn('OPENROUTER_API_KEY not found. OpenRouter features will be limited.')
  }
  
  console.log(`Available AI providers: ${availableProviders.join(', ')}`)
  return true
}

// Provider health check
export async function checkProviderHealth(provider: string): Promise<boolean> {
  try {
    const providerConfig = aiProviders[provider as keyof typeof aiProviders]
    if (!providerConfig) return false
    
    // For OpenRouter, we can check the API status
    if (provider === 'openrouter') {
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
        }
      })
      return response.ok
    }
    
    return true
  } catch (error) {
    console.error(`Health check failed for provider ${provider}:`, error)
    return false
  }
}
