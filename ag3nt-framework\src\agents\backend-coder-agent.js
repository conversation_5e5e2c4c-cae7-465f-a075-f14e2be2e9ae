"use strict";
/**
 * AG3NT Framework - Backend Coder Agent
 *
 * Specialized agent for backend development tasks.
 * Handles API development, database design, server architecture, and backend services.
 *
 * Features:
 * - REST/GraphQL API development
 * - Database design and optimization
 * - Microservices architecture
 * - Authentication and authorization
 * - Performance optimization
 * - Security implementation
 * - Testing and documentation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.BackendCoderAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Backend Coder Agent - Specialized backend development
 */
class BackendCoderAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('backend-coder', {
            capabilities: {
                requiredCapabilities: [
                    'backend_development',
                    'api_design',
                    'database_design',
                    'security_implementation',
                    'performance_optimization',
                    'microservices_architecture',
                    'backend_testing'
                ],
                contextFilters: ['backend', 'api', 'database', 'security', 'code'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.codingSteps = [
            'analyze_requirements', 'design_architecture', 'setup_environment',
            'implement_apis', 'design_database', 'implement_services',
            'add_security', 'optimize_performance', 'write_tests', 'document_apis'
        ];
    }
    /**
     * Execute backend coding workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`⚙️ Starting backend development: ${input.task.title}`);
        // Execute coding steps sequentially
        for (const stepId of this.codingSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            if (stepResult.needsReview) {
                state.results.status = 'needs_review';
                state.results.reviewReason = stepResult.reviewReason;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed and no review required
        if (!state.needsInput && state.results.status !== 'needs_review') {
            state.completed = true;
            console.log(`✅ Backend development completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual coding step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_requirements':
                return await this.analyzeRequirementsWithMCP(enhancedState, input);
            case 'design_architecture':
                return await this.designArchitectureWithMCP(enhancedState);
            case 'setup_environment':
                return await this.setupEnvironmentWithMCP(enhancedState);
            case 'implement_apis':
                return await this.implementAPIsWithMCP(enhancedState);
            case 'design_database':
                return await this.designDatabaseWithMCP(enhancedState);
            case 'implement_services':
                return await this.implementServicesWithMCP(enhancedState);
            case 'add_security':
                return await this.addSecurityWithMCP(enhancedState);
            case 'optimize_performance':
                return await this.optimizePerformanceWithMCP(enhancedState);
            case 'write_tests':
                return await this.writeTestsWithMCP(enhancedState);
            case 'document_apis':
                return await this.documentAPIsWithMCP(enhancedState);
            default:
                throw new Error(`Unknown backend coding step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.codingSteps.length;
    }
    /**
     * Get relevant documentation for backend development
     */
    async getRelevantDocumentation() {
        return {
            backendDevelopment: 'Modern backend development practices and patterns',
            apiDesign: 'RESTful API design principles and GraphQL best practices',
            databaseDesign: 'Database design patterns and optimization techniques',
            security: 'Backend security implementation and best practices',
            performance: 'Backend performance optimization and scaling strategies',
            testing: 'Backend testing strategies and test-driven development'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeRequirementsWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeBackendRequirements(input.task, input.architecture, input.requirements, input.codebase);
        this.state.results.requirementsAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async designArchitectureWithMCP(state) {
        const requirementsAnalysis = this.state.results.requirementsAnalysis;
        const architectureDesign = await ai_service_1.aiService.designBackendArchitecture(requirementsAnalysis, this.state.input.architecture);
        this.state.results.architectureDesign = architectureDesign;
        return {
            results: architectureDesign,
            needsInput: false,
            completed: false
        };
    }
    async setupEnvironmentWithMCP(state) {
        const architectureDesign = this.state.results.architectureDesign;
        const environmentSetup = await ai_service_1.aiService.setupBackendEnvironment(architectureDesign, this.state.input.requirements);
        this.state.results.environmentSetup = environmentSetup;
        return {
            results: environmentSetup,
            needsInput: false,
            completed: false
        };
    }
    async implementAPIsWithMCP(state) {
        const architectureDesign = this.state.results.architectureDesign;
        const apis = await ai_service_1.aiService.implementBackendAPIs(architectureDesign, this.state.input.requirements);
        this.state.results.apis = apis;
        return {
            results: apis,
            needsInput: false,
            completed: false
        };
    }
    async designDatabaseWithMCP(state) {
        const architectureDesign = this.state.results.architectureDesign;
        const database = await ai_service_1.aiService.designBackendDatabase(architectureDesign, this.state.input.architecture.database);
        this.state.results.database = database;
        return {
            results: database,
            needsInput: false,
            completed: false
        };
    }
    async implementServicesWithMCP(state) {
        const apis = this.state.results.apis;
        const database = this.state.results.database;
        const services = await ai_service_1.aiService.implementBackendServices(apis, database, this.state.input.requirements);
        this.state.results.services = services;
        return {
            results: services,
            needsInput: false,
            completed: false
        };
    }
    async addSecurityWithMCP(state) {
        const services = this.state.results.services;
        const security = await ai_service_1.aiService.addBackendSecurity(services, this.state.input.requirements.security);
        this.state.results.security = security;
        return {
            results: security,
            needsInput: false,
            completed: false
        };
    }
    async optimizePerformanceWithMCP(state) {
        const services = this.state.results.services;
        const optimization = await ai_service_1.aiService.optimizeBackendPerformance(services, this.state.input.requirements.performance);
        this.state.results.optimization = optimization;
        return {
            results: optimization,
            needsInput: false,
            completed: false
        };
    }
    async writeTestsWithMCP(state) {
        const services = this.state.results.services;
        const apis = this.state.results.apis;
        const tests = await ai_service_1.aiService.writeBackendTests(services, apis, this.state.input.requirements);
        this.state.results.tests = tests;
        return {
            results: tests,
            needsInput: false,
            completed: false
        };
    }
    async documentAPIsWithMCP(state) {
        const apis = this.state.results.apis;
        const services = this.state.results.services;
        const documentation = await ai_service_1.aiService.documentBackendAPIs(apis, services, this.state.input.task);
        this.state.results.documentation = documentation;
        return {
            results: documentation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.BackendCoderAgent = BackendCoderAgent;
exports.default = BackendCoderAgent;
//# sourceMappingURL=backend-coder-agent.js.map