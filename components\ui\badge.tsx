import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 ring-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:ring-gray-700",
        secondary:
          "bg-gray-800 text-gray-100 ring-gray-700 dark:bg-gray-700 dark:text-gray-200 dark:ring-gray-600",
        success:
          "bg-green-50 text-green-800 ring-green-200 dark:bg-green-900/20 dark:text-green-400 dark:ring-green-800",
        warning:
          "bg-yellow-50 text-yellow-800 ring-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:ring-yellow-800",
        destructive:
          "bg-red-50 text-red-800 ring-red-200 dark:bg-red-900/20 dark:text-red-400 dark:ring-red-800",
        outline:
          "bg-transparent text-gray-700 ring-gray-300 dark:text-gray-300 dark:ring-gray-600",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
