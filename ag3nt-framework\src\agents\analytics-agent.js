"use strict";
/**
 * AG3NT Framework - Analytics/Telemetry Agent
 *
 * Specialized agent for monitoring agent/system health, collecting metrics,
 * and detecting anomalies in the multi-agent system.
 *
 * Features:
 * - System health monitoring
 * - Performance metrics collection
 * - Anomaly detection
 * - Predictive analytics
 * - Real-time alerting
 * - Dashboard generation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.AnalyticsAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Analytics/Telemetry Agent - System monitoring and intelligence
 */
class AnalyticsAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('analytics', {
            capabilities: {
                requiredCapabilities: [
                    'metrics_collection',
                    'health_monitoring',
                    'anomaly_detection',
                    'predictive_analytics',
                    'alert_management',
                    'dashboard_creation',
                    'report_generation'
                ],
                contextFilters: ['analytics', 'metrics', 'monitoring', 'health', 'performance'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.analyticsSteps = [
            'collect_metrics', 'analyze_health', 'detect_anomalies',
            'generate_insights', 'create_predictions', 'setup_alerts',
            'generate_reports', 'create_dashboards', 'validate_analytics'
        ];
    }
    /**
     * Execute analytics workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`📊 Starting analytics workflow: ${input.task.title}`);
        // Execute analytics steps sequentially
        for (const stepId of this.analyticsSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Analytics workflow completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual analytics step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'collect_metrics':
                return await this.collectMetricsWithMCP(enhancedState, input);
            case 'analyze_health':
                return await this.analyzeHealthWithMCP(enhancedState);
            case 'detect_anomalies':
                return await this.detectAnomaliesWithMCP(enhancedState);
            case 'generate_insights':
                return await this.generateInsightsWithMCP(enhancedState);
            case 'create_predictions':
                return await this.createPredictionsWithMCP(enhancedState);
            case 'setup_alerts':
                return await this.setupAlertsWithMCP(enhancedState);
            case 'generate_reports':
                return await this.generateReportsWithMCP(enhancedState);
            case 'create_dashboards':
                return await this.createDashboardsWithMCP(enhancedState);
            case 'validate_analytics':
                return await this.validateAnalyticsWithMCP(enhancedState);
            default:
                throw new Error(`Unknown analytics step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.analyticsSteps.length;
    }
    /**
     * Get relevant documentation for analytics
     */
    async getRelevantDocumentation() {
        return {
            analytics: 'Analytics and telemetry best practices',
            monitoring: 'System monitoring and observability',
            anomalyDetection: 'Anomaly detection algorithms and techniques',
            predictiveAnalytics: 'Predictive analytics and forecasting',
            alerting: 'Alert management and escalation strategies',
            dashboards: 'Dashboard design and visualization best practices'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async collectMetricsWithMCP(state, input) {
        const collection = await ai_service_1.aiService.collectSystemMetrics(input.system, input.metrics.collection, input.task.scope);
        this.state.results.metricsCollection = collection;
        return {
            results: collection,
            needsInput: false,
            completed: false
        };
    }
    async analyzeHealthWithMCP(state) {
        const metricsCollection = this.state.results.metricsCollection;
        const healthAnalysis = await ai_service_1.aiService.analyzeSystemHealth(metricsCollection);
        this.state.results.healthAnalysis = healthAnalysis;
        return {
            results: healthAnalysis,
            needsInput: false,
            completed: false
        };
    }
    async detectAnomaliesWithMCP(state) {
        const healthAnalysis = this.state.results.healthAnalysis;
        const anomalies = await ai_service_1.aiService.detectSystemAnomalies(healthAnalysis, this.state.input.requirements.analysis);
        this.state.results.anomalies = anomalies;
        return {
            results: anomalies,
            needsInput: false,
            completed: false
        };
    }
    async generateInsightsWithMCP(state) {
        const anomalies = this.state.results.anomalies;
        const insights = await ai_service_1.aiService.generateAnalyticsInsights(anomalies, this.state.results.healthAnalysis);
        this.state.results.insights = insights;
        return {
            results: insights,
            needsInput: false,
            completed: false
        };
    }
    async createPredictionsWithMCP(state) {
        const insights = this.state.results.insights;
        const predictions = await ai_service_1.aiService.createSystemPredictions(insights, this.state.input.requirements.analysis);
        this.state.results.predictions = predictions;
        return {
            results: predictions,
            needsInput: false,
            completed: false
        };
    }
    async setupAlertsWithMCP(state) {
        const predictions = this.state.results.predictions;
        const alerts = await ai_service_1.aiService.setupAnalyticsAlerts(predictions, this.state.input.requirements.alerting);
        this.state.results.alerts = alerts;
        return {
            results: alerts,
            needsInput: false,
            completed: false
        };
    }
    async generateReportsWithMCP(state) {
        const allResults = {
            health: this.state.results.healthAnalysis,
            anomalies: this.state.results.anomalies,
            insights: this.state.results.insights,
            predictions: this.state.results.predictions
        };
        const reports = await ai_service_1.aiService.generateAnalyticsReports(allResults, this.state.input.requirements.reporting);
        this.state.results.reports = reports;
        return {
            results: reports,
            needsInput: false,
            completed: false
        };
    }
    async createDashboardsWithMCP(state) {
        const reports = this.state.results.reports;
        const dashboards = await ai_service_1.aiService.createAnalyticsDashboards(reports);
        this.state.results.dashboards = dashboards;
        return {
            results: dashboards,
            needsInput: false,
            completed: false
        };
    }
    async validateAnalyticsWithMCP(state) {
        const dashboards = this.state.results.dashboards;
        const validation = await ai_service_1.aiService.validateAnalyticsSetup(dashboards, this.state.input.task);
        this.state.results.validation = validation;
        return {
            results: validation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.AnalyticsAgent = AnalyticsAgent;
exports.default = AnalyticsAgent;
//# sourceMappingURL=analytics-agent.js.map