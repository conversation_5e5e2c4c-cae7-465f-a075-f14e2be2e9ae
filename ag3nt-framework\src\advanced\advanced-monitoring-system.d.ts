/**
 * AG3NT Framework - Advanced Monitoring & Analytics System
 *
 * Sophisticated monitoring system that provides real-time insights,
 * automatic performance optimization, and predictive analytics.
 *
 * Features:
 * - Real-time performance monitoring
 * - Predictive analytics and forecasting
 * - Automatic anomaly detection
 * - Performance optimization recommendations
 * - Custom dashboards and alerts
 * - Multi-dimensional analytics
 */
import { EventEmitter } from "events";
export interface MonitoringConfig {
    enabled: boolean;
    realTimeEnabled: boolean;
    samplingRate: number;
    retentionPeriod: number;
    alertingEnabled: boolean;
    predictiveAnalytics: boolean;
    autoOptimization: boolean;
    dashboardEnabled: boolean;
}
export interface MetricDefinition {
    name: string;
    type: 'counter' | 'gauge' | 'histogram' | 'summary';
    description: string;
    unit: string;
    labels: string[];
    aggregations: string[];
    retention: number;
}
export interface MetricValue {
    metric: string;
    value: number;
    timestamp: number;
    labels: Record<string, string>;
    metadata?: any;
}
export interface MonitoringDashboard {
    id: string;
    name: string;
    description: string;
    panels: DashboardPanel[];
    layout: DashboardLayout;
    filters: DashboardFilter[];
    autoRefresh: number;
    permissions: string[];
}
export interface DashboardPanel {
    id: string;
    title: string;
    type: 'line' | 'bar' | 'pie' | 'gauge' | 'table' | 'heatmap' | 'stat';
    query: MetricQuery;
    visualization: VisualizationConfig;
    position: PanelPosition;
    alerts: PanelAlert[];
}
export interface MetricQuery {
    metrics: string[];
    timeRange: TimeRange;
    filters: QueryFilter[];
    groupBy: string[];
    aggregation: string;
    interval: string;
}
export interface TimeRange {
    start: number;
    end: number;
    relative?: string;
}
export interface QueryFilter {
    label: string;
    operator: 'eq' | 'ne' | 'regex' | 'in' | 'nin';
    value: any;
}
export interface VisualizationConfig {
    colors: string[];
    thresholds: Threshold[];
    axes: AxisConfig[];
    legend: LegendConfig;
    tooltip: TooltipConfig;
}
export interface Threshold {
    value: number;
    color: string;
    label: string;
    operator: 'gt' | 'lt' | 'gte' | 'lte';
}
export interface AxisConfig {
    axis: 'x' | 'y';
    label: string;
    scale: 'linear' | 'log' | 'time';
    min?: number;
    max?: number;
}
export interface LegendConfig {
    show: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
    alignment: 'start' | 'center' | 'end';
}
export interface TooltipConfig {
    show: boolean;
    format: string;
    precision: number;
}
export interface PanelPosition {
    x: number;
    y: number;
    width: number;
    height: number;
}
export interface PanelAlert {
    id: string;
    condition: AlertCondition;
    severity: 'info' | 'warning' | 'critical';
    channels: string[];
    enabled: boolean;
}
export interface AlertCondition {
    metric: string;
    operator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq' | 'ne';
    threshold: number;
    duration: number;
    frequency: number;
}
export interface DashboardLayout {
    columns: number;
    rowHeight: number;
    margin: number;
    responsive: boolean;
}
export interface DashboardFilter {
    name: string;
    label: string;
    type: 'select' | 'multiselect' | 'text' | 'date';
    options?: string[];
    default?: any;
}
export interface PerformanceInsight {
    id: string;
    type: 'optimization' | 'anomaly' | 'trend' | 'prediction' | 'recommendation';
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    confidence: number;
    impact: number;
    evidence: InsightEvidence[];
    recommendations: InsightRecommendation[];
    timestamp: number;
}
export interface InsightEvidence {
    type: 'metric' | 'event' | 'correlation' | 'pattern';
    description: string;
    data: any;
    confidence: number;
}
export interface InsightRecommendation {
    action: string;
    description: string;
    expectedImpact: number;
    effort: 'low' | 'medium' | 'high';
    priority: 'low' | 'medium' | 'high' | 'critical';
    implementation: string;
}
export interface PredictiveModel {
    id: string;
    name: string;
    type: 'linear_regression' | 'arima' | 'lstm' | 'prophet' | 'ensemble';
    target: string;
    features: string[];
    accuracy: number;
    lastTrained: number;
    predictions: Prediction[];
}
export interface Prediction {
    timestamp: number;
    value: number;
    confidence: number;
    bounds: {
        lower: number;
        upper: number;
    };
    factors: PredictionFactor[];
}
export interface PredictionFactor {
    feature: string;
    importance: number;
    contribution: number;
    trend: 'increasing' | 'decreasing' | 'stable';
}
export interface AnomalyDetection {
    id: string;
    algorithm: 'isolation_forest' | 'one_class_svm' | 'statistical' | 'lstm_autoencoder';
    sensitivity: number;
    trainingPeriod: number;
    detectedAnomalies: Anomaly[];
    model: any;
}
export interface Anomaly {
    id: string;
    timestamp: number;
    metric: string;
    value: number;
    expectedValue: number;
    deviation: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    context: AnomalyContext;
    resolved: boolean;
}
export interface AnomalyContext {
    relatedMetrics: string[];
    correlations: Correlation[];
    events: ContextualEvent[];
    patterns: DetectedPattern[];
}
export interface Correlation {
    metric: string;
    coefficient: number;
    significance: number;
    timelag: number;
}
export interface ContextualEvent {
    type: string;
    description: string;
    timestamp: number;
    impact: number;
}
export interface DetectedPattern {
    type: 'seasonal' | 'trend' | 'cyclical' | 'irregular';
    description: string;
    confidence: number;
    period?: number;
}
/**
 * Advanced Monitoring & Analytics System
 */
export declare class AdvancedMonitoringSystem extends EventEmitter {
    private config;
    private metrics;
    private metricDefinitions;
    private dashboards;
    private insights;
    private predictiveModels;
    private anomalyDetectors;
    private isMonitoring;
    private monitoringTimer?;
    constructor(config?: Partial<MonitoringConfig>);
    /**
     * Initialize monitoring system
     */
    private initialize;
    /**
     * Record metric value
     */
    recordMetric(name: string, value: number, labels?: Record<string, string>, metadata?: any): void;
    /**
     * Query metrics
     */
    queryMetrics(query: MetricQuery): MetricValue[];
    /**
     * Create dashboard
     */
    createDashboard(dashboard: Partial<MonitoringDashboard>): MonitoringDashboard;
    /**
     * Generate performance insights
     */
    generateInsights(): Promise<PerformanceInsight[]>;
    /**
     * Get dashboard data
     */
    getDashboardData(dashboardId: string): any;
    /**
     * Private helper methods
     */
    private registerDefaultMetrics;
    private createDefaultDashboard;
    private startMonitoring;
    private collectSystemMetrics;
    private checkForAnomalies;
    private evaluateFilter;
    private evaluatePanelAlerts;
    private analyzeTrends;
    private detectAnomalies;
    private generatePredictions;
    private generateOptimizationRecommendations;
    /**
     * Shutdown monitoring system
     */
    shutdown(): Promise<void>;
}
export default AdvancedMonitoringSystem;
//# sourceMappingURL=advanced-monitoring-system.d.ts.map