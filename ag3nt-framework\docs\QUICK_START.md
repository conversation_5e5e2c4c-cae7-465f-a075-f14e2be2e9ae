# AG3NT Framework - Quick Start Guide

Get up and running with the AG3NT Framework in minutes!

## 🚀 Installation

```bash
npm install ag3nt-framework
```

## 📦 Basic Setup

### 1. Import the Framework

```typescript
import { AG3NTFramework, createPlanningAgent, createExecutorAgent } from 'ag3nt-framework'
```

### 2. Initialize the Framework

```typescript
const framework = new AG3NTFramework({
  contextEngine: {
    enableMCP: true,
    enableSequentialThinking: true,
    enableRAG: true
  },
  coordination: {
    enableTaskDelegation: true,
    enableConsensus: true,
    enableWorkflowHandoffs: true
  },
  discovery: {
    enableAgentDiscovery: true,
    enableLoadBalancing: true,
    enableFailover: true
  }
})

await framework.initialize()
```

### 3. Register Agents

```typescript
// Create and register agents
const planningAgent = createPlanningAgent()
const executorAgent = createExecutorAgent()

await framework.registerAgent(planningAgent)
await framework.registerAgent(executorAgent)
```

### 4. Execute Tasks

```typescript
// Execute a task
const result = await framework.execute('planning', {
  type: 'project_planning',
  description: 'Plan a modern web application',
  requirements: {
    frontend: 'react',
    backend: 'nestjs',
    database: 'postgresql'
  }
})

console.log('Planning completed:', result)
```

## 🎯 Complete Example

```typescript
import {
  AG3NTFramework,
  createPlanningAgent,
  createExecutorAgent,
  createFrontendCoderAgent,
  createBackendCoderAgent
} from 'ag3nt-framework'

async function main() {
  // Initialize framework
  const framework = new AG3NTFramework({
    contextEngine: { enableMCP: true },
    coordination: { enableTaskDelegation: true },
    discovery: { enableLoadBalancing: true }
  })
  
  await framework.initialize()
  
  // Register agents
  const agents = [
    createPlanningAgent(),
    createExecutorAgent(),
    createFrontendCoderAgent(),
    createBackendCoderAgent()
  ]
  
  for (const agent of agents) {
    await framework.registerAgent(agent)
  }
  
  // Execute project development
  const result = await framework.execute('planning', {
    type: 'full_project',
    description: 'Build a task management application',
    features: ['user_auth', 'task_crud', 'real_time_updates']
  })
  
  console.log('Project development completed:', result)
  
  // Cleanup
  await framework.shutdown()
}

main().catch(console.error)
```

## 🔧 Configuration Options

### Context Engine
```typescript
contextEngine: {
  enableMCP: true,              // Model Context Protocol
  enableSequentialThinking: true, // Advanced reasoning
  enableRAG: true,              // Retrieval Augmented Generation
  enableNeo4j: false,           // Graph database (optional)
  enableTemporalDatabase: false // Temporal context (optional)
}
```

### Coordination
```typescript
coordination: {
  enableTaskDelegation: true,   // Hierarchical task delegation
  enableConsensus: true,        // Democratic decision making
  enableWorkflowHandoffs: true, // Formal state transitions
  enablePatternRegistry: true,  // Reusable patterns
  delegationTimeout: 300000,    // 5 minutes
  consensusTimeout: 300000,     // 5 minutes
  handoffTimeout: 300000        // 5 minutes
}
```

### Discovery & Load Balancing
```typescript
discovery: {
  enableAgentDiscovery: true,   // Automatic agent discovery
  enableLoadBalancing: true,    // Intelligent load balancing
  enableFailover: true,         // Automatic failover
  discoveryInterval: 30000,     // 30 seconds
  healthCheckInterval: 10000,   // 10 seconds
  loadBalancingAlgorithm: 'adaptive' // round_robin, weighted, adaptive
}
```

### Advanced Features
```typescript
advancedFeatures: {
  adaptiveLearning: { enabled: true },    // Self-improving agents
  temporalDatabase: { enabled: true },    // Time-aware context
  collaboration: { enabled: true },       // Multi-agent collaboration
  optimization: { enabled: true },        // Performance optimization
  monitoring: { enabled: true }           // Real-time monitoring
}
```

## 🎭 Running Examples

### Master Demo
```bash
npm run demo:master
```

### Specific Demos
```bash
npm run demo:workflows      # Multi-agent workflows
npm run demo:coordination   # Agent coordination
npm run demo:discovery      # Discovery and load balancing
npm run demo:benchmarks     # Performance benchmarks
```

## 📊 Monitoring & Analytics

### Get Framework Analytics
```typescript
const analytics = framework.getCoordinationAnalytics()
console.log('Coordination metrics:', analytics)

const discoveryAnalytics = framework.getDiscoveryAnalytics()
console.log('Discovery metrics:', discoveryAnalytics)
```

### Monitor Agent Health
```typescript
const discoveryService = framework.getDiscoveryService()
const stats = discoveryService.getDiscoveryStats()
console.log('Agent statistics:', stats)
```

### Load Balancing Metrics
```typescript
const loadBalancer = framework.getLoadBalancer()
const metrics = loadBalancer.getMetrics()
console.log('Load balancing metrics:', metrics)
```

## 🐛 Troubleshooting

### Common Issues

**Framework won't initialize**
- Check Node.js version (18+ required)
- Verify all dependencies are installed
- Check for port conflicts

**Agents not discovered**
- Ensure discovery is enabled in config
- Check agent registration
- Verify health check intervals

**Load balancing not working**
- Confirm multiple agents are registered
- Check load balancing algorithm
- Verify agent health status

### Debug Mode
```typescript
const framework = new AG3NTFramework({
  // ... other config
  monitoring: {
    enableHealthChecks: true,
    enableMetrics: true
  }
})
```

### Environment Variables
```bash
# Enable debug logging
AG3NT_LOG_LEVEL=debug
AG3NT_ENABLE_DEBUG=true

# Performance tuning
AG3NT_MAX_AGENTS=50
AG3NT_LOAD_BALANCE_ALGORITHM=adaptive
```

## 📚 Next Steps

1. **Read the Documentation**
   - [API Reference](./API_REFERENCE.md)
   - [Agent Development Guide](./AGENT_DEVELOPMENT.md)
   - [Workflow Creation](./WORKFLOW_GUIDE.md)

2. **Explore Examples**
   - Check the `examples/` directory
   - Run the demonstration scripts
   - Study the benchmark comparisons

3. **Build Your First Agent**
   - Extend the `BaseAgent` class
   - Implement your custom logic
   - Register with the framework

4. **Create Custom Workflows**
   - Use the workflow templates
   - Define coordination patterns
   - Add analytics and monitoring

## 🆘 Getting Help

- **Documentation**: Check the `docs/` directory
- **Examples**: Review the `examples/` directory
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub Discussions for questions

---

**Welcome to the future of autonomous development!** 🚀
