import { UsageStats, ModelMetrics, calculateCost, getModelMetadata } from './ai-config'

// In-memory storage for usage statistics (in production, use a database)
class UsageTracker {
  private usageHistory: UsageStats[] = []
  private modelMetrics: Map<string, ModelMetrics> = new Map()
  private dailyUsage: Map<string, number> = new Map() // date -> cost
  private monthlyBudget: number = parseFloat(process.env.MONTHLY_AI_BUDGET || '100')

  // Track a new usage event
  trackUsage(stats: UsageStats): void {
    this.usageHistory.push(stats)
    this.updateModelMetrics(stats)
    this.updateDailyUsage(stats)
    
    // Keep only last 1000 entries to prevent memory issues
    if (this.usageHistory.length > 1000) {
      this.usageHistory = this.usageHistory.slice(-1000)
    }
  }

  // Update model performance metrics
  private updateModelMetrics(stats: UsageStats): void {
    const key = `${stats.provider}:${stats.model}`
    const existing = this.modelMetrics.get(key)
    
    if (existing) {
      // Update existing metrics
      const totalRequests = existing.totalRequests + 1
      const totalTokens = existing.totalTokens + stats.totalTokens
      const totalCost = existing.totalCost + stats.cost
      const averageLatency = (existing.averageLatency * existing.totalRequests + stats.latency) / totalRequests
      const successRate = stats.success 
        ? (existing.successRate * existing.totalRequests + 1) / totalRequests
        : (existing.successRate * existing.totalRequests) / totalRequests

      this.modelMetrics.set(key, {
        ...existing,
        averageLatency,
        successRate,
        totalRequests,
        totalTokens,
        totalCost,
        costPerToken: totalCost / totalTokens,
        lastUsed: stats.timestamp
      })
    } else {
      // Create new metrics
      this.modelMetrics.set(key, {
        averageLatency: stats.latency,
        successRate: stats.success ? 1 : 0,
        costPerToken: stats.cost / stats.totalTokens,
        totalRequests: 1,
        totalTokens: stats.totalTokens,
        totalCost: stats.cost,
        lastUsed: stats.timestamp
      })
    }
  }

  // Update daily usage tracking
  private updateDailyUsage(stats: UsageStats): void {
    const dateKey = stats.timestamp.toISOString().split('T')[0]
    const currentUsage = this.dailyUsage.get(dateKey) || 0
    this.dailyUsage.set(dateKey, currentUsage + stats.cost)
  }

  // Get usage statistics for a specific time period
  getUsageStats(days: number = 30): {
    totalCost: number
    totalRequests: number
    totalTokens: number
    averageLatency: number
    successRate: number
    topModels: Array<{ model: string, usage: number, cost: number }>
    dailyBreakdown: Array<{ date: string, cost: number, requests: number }>
  } {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)
    
    const recentUsage = this.usageHistory.filter(u => u.timestamp >= cutoffDate)
    
    const totalCost = recentUsage.reduce((sum, u) => sum + u.cost, 0)
    const totalRequests = recentUsage.length
    const totalTokens = recentUsage.reduce((sum, u) => sum + u.totalTokens, 0)
    const averageLatency = recentUsage.reduce((sum, u) => sum + u.latency, 0) / totalRequests || 0
    const successRate = recentUsage.filter(u => u.success).length / totalRequests || 0

    // Top models by usage
    const modelUsage = new Map<string, { usage: number, cost: number }>()
    recentUsage.forEach(u => {
      const key = `${u.provider}:${u.model}`
      const existing = modelUsage.get(key) || { usage: 0, cost: 0 }
      modelUsage.set(key, {
        usage: existing.usage + 1,
        cost: existing.cost + u.cost
      })
    })

    const topModels = Array.from(modelUsage.entries())
      .map(([model, stats]) => ({ model, ...stats }))
      .sort((a, b) => b.cost - a.cost)
      .slice(0, 10)

    // Daily breakdown
    const dailyStats = new Map<string, { cost: number, requests: number }>()
    recentUsage.forEach(u => {
      const dateKey = u.timestamp.toISOString().split('T')[0]
      const existing = dailyStats.get(dateKey) || { cost: 0, requests: 0 }
      dailyStats.set(dateKey, {
        cost: existing.cost + u.cost,
        requests: existing.requests + 1
      })
    })

    const dailyBreakdown = Array.from(dailyStats.entries())
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date))

    return {
      totalCost,
      totalRequests,
      totalTokens,
      averageLatency,
      successRate,
      topModels,
      dailyBreakdown
    }
  }

  // Get model performance metrics
  getModelMetrics(modelKey?: string): ModelMetrics | Map<string, ModelMetrics> {
    if (modelKey) {
      return this.modelMetrics.get(modelKey) || {
        averageLatency: 0,
        successRate: 0,
        costPerToken: 0,
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        lastUsed: new Date()
      }
    }
    return this.modelMetrics
  }

  // Check if approaching budget limits
  getBudgetStatus(): {
    monthlyBudget: number
    currentMonthSpend: number
    remainingBudget: number
    percentUsed: number
    isOverBudget: boolean
    dailyAverage: number
    projectedMonthlySpend: number
  } {
    const now = new Date()
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const monthUsage = this.usageHistory.filter(u => u.timestamp >= monthStart)
    const currentMonthSpend = monthUsage.reduce((sum, u) => sum + u.cost, 0)
    
    const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
    const daysPassed = now.getDate()
    const dailyAverage = currentMonthSpend / daysPassed
    const projectedMonthlySpend = dailyAverage * daysInMonth

    return {
      monthlyBudget: this.monthlyBudget,
      currentMonthSpend,
      remainingBudget: this.monthlyBudget - currentMonthSpend,
      percentUsed: (currentMonthSpend / this.monthlyBudget) * 100,
      isOverBudget: currentMonthSpend > this.monthlyBudget,
      dailyAverage,
      projectedMonthlySpend
    }
  }

  // Get model recommendations based on performance and cost
  getModelRecommendations(): {
    mostEfficient: string[]
    mostReliable: string[]
    bestValue: string[]
    fastest: string[]
  } {
    const metrics = Array.from(this.modelMetrics.entries())
      .filter(([_, m]) => m.totalRequests >= 5) // Only consider models with sufficient data
      .map(([key, metrics]) => ({ key, ...metrics }))

    const mostEfficient = metrics
      .sort((a, b) => a.costPerToken - b.costPerToken)
      .slice(0, 3)
      .map(m => m.key)

    const mostReliable = metrics
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 3)
      .map(m => m.key)

    const bestValue = metrics
      .map(m => ({ ...m, valueScore: m.successRate / m.costPerToken }))
      .sort((a, b) => b.valueScore - a.valueScore)
      .slice(0, 3)
      .map(m => m.key)

    const fastest = metrics
      .sort((a, b) => a.averageLatency - b.averageLatency)
      .slice(0, 3)
      .map(m => m.key)

    return {
      mostEfficient,
      mostReliable,
      bestValue,
      fastest
    }
  }

  // Export usage data for analysis
  exportUsageData(): {
    summary: ReturnType<typeof this.getUsageStats>
    metrics: Array<{ model: string } & ModelMetrics>
    budget: ReturnType<typeof this.getBudgetStatus>
    recommendations: ReturnType<typeof this.getModelRecommendations>
  } {
    const summary = this.getUsageStats()
    const metrics = Array.from(this.modelMetrics.entries())
      .map(([model, metrics]) => ({ model, ...metrics }))
    const budget = this.getBudgetStatus()
    const recommendations = this.getModelRecommendations()

    return {
      summary,
      metrics,
      budget,
      recommendations
    }
  }

  // Clear old data (for maintenance)
  clearOldData(daysToKeep: number = 90): void {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    
    this.usageHistory = this.usageHistory.filter(u => u.timestamp >= cutoffDate)
    
    // Clear daily usage older than cutoff
    const cutoffDateStr = cutoffDate.toISOString().split('T')[0]
    for (const [date] of this.dailyUsage) {
      if (date < cutoffDateStr) {
        this.dailyUsage.delete(date)
      }
    }
  }
}

// Singleton instance
export const usageTracker = new UsageTracker()

// Helper function to track API usage
export function trackAPIUsage(
  provider: string,
  model: string,
  promptTokens: number,
  completionTokens: number,
  latency: number,
  success: boolean = true
): void {
  const totalTokens = promptTokens + completionTokens
  const modelId = provider === 'openrouter' ? model.replace('-', '/') : `${provider}/${model}`
  const cost = calculateCost(modelId, promptTokens, completionTokens)

  const stats: UsageStats = {
    provider,
    model,
    promptTokens,
    completionTokens,
    totalTokens,
    cost,
    timestamp: new Date(),
    latency,
    success
  }

  usageTracker.trackUsage(stats)
}

// Middleware function for API routes to automatically track usage
export function withUsageTracking<T extends (...args: any[]) => Promise<any>>(
  handler: T,
  provider: string,
  model: string
): T {
  return (async (...args: any[]) => {
    const startTime = Date.now()
    let success = true
    let promptTokens = 0
    let completionTokens = 0

    try {
      const result = await handler(...args)
      
      // Extract token usage from result if available
      if (result && typeof result === 'object') {
        if ('usage' in result) {
          promptTokens = result.usage?.promptTokens || 0
          completionTokens = result.usage?.completionTokens || 0
        }
      }

      return result
    } catch (error) {
      success = false
      throw error
    } finally {
      const latency = Date.now() - startTime
      trackAPIUsage(provider, model, promptTokens, completionTokens, latency, success)
    }
  }) as T
}