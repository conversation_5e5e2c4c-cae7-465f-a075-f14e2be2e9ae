{"version": 3, "file": "frontend-coder-agent.js", "sourceRoot": "", "sources": ["frontend-coder-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AAgP5C;;GAEG;AACH,MAAa,kBAAmB,SAAQ,sBAAS;IAO/C,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,gBAAgB,EAAE;YACtB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,sBAAsB;oBACtB,mBAAmB;oBACnB,wBAAwB;oBACxB,mBAAmB;oBACnB,0BAA0B;oBAC1B,0BAA0B;oBAC1B,kBAAkB;iBACnB;gBACD,cAAc,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;gBAClE,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QAzBa,gBAAW,GAAG;YAC7B,sBAAsB,EAAE,qBAAqB,EAAE,mBAAmB;YAClE,oBAAoB,EAAE,mBAAmB,EAAE,kBAAkB;YAC7D,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,EAAE,eAAe;SAC/E,CAAA;IAsBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAA2B,CAAA;QAE/C,OAAO,CAAC,GAAG,CAAC,qCAAqC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAEpE,oCAAoC;QACpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC3B,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,cAAc,CAAA;gBACrC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAA;gBACpD,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,8DAA8D;QAC9D,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;YACjE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,qCAAqC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACtE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,2CAA2C;QAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YACpE,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;YAC3D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,aAAa;gBAChB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAA;YACpD,KAAK,eAAe;gBAClB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAA;YACtD;gBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;IAChC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,mBAAmB,EAAE,oDAAoD;YACzE,qBAAqB,EAAE,kDAAkD;YACzE,gBAAgB,EAAE,oDAAoD;YACtE,aAAa,EAAE,kDAAkD;YACjE,WAAW,EAAE,8CAA8C;YAC3D,OAAO,EAAE,gDAAgD;SAC1D,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,0BAA0B,CAAC,KAAU,EAAE,KAAyB;QAC5E,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,2BAA2B,CAC1D,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,QAAQ,CACf,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,oBAAoB,GAAG,QAAQ,CAAA;QAEnD,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,oBAAoB,CAAA;QAErE,MAAM,kBAAkB,GAAG,MAAM,sBAAS,CAAC,0BAA0B,CACnE,oBAAoB,EACpB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAC3B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;QAE3D,OAAO;YACL,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,kBAAkB,CAAA;QAEjE,MAAM,gBAAgB,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CAC/D,kBAAkB,EAClB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAC/B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QAEvD,OAAO;YACL,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAU;QAC/C,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,kBAAkB,CAAA;QAEjE,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,yBAAyB,CAC1D,kBAAkB,EAClB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,MAAM,EACxB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAC/B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CACtD,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,EACrC,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CACvC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CAC1D,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAClC,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAC/C,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,2BAA2B,CAC9D,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAC3C,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,aAAa,GAAG,MAAM,sBAAS,CAAC,2BAA2B,CAC/D,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAC7C,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAA;QAEjD,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAU;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,KAAK,GAAG,MAAM,sBAAS,CAAC,kBAAkB,CAC9C,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CACvC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QAEjC,OAAO;YACL,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAU;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,aAAa,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACxD,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAA;QAEjD,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AAlTD,gDAkTC;AAG8B,qCAAO"}