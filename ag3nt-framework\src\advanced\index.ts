/**
 * AG3NT Framework - Advanced Features
 * 
 * Export all advanced framework features that differentiate AG3NT
 * from competitors like CrewAI and LangGraph.
 */

// Adaptive Learning System
export { default as AdaptiveLearningSystem } from './adaptive-learning-system'
export type {
  LearningConfiguration,
  ExecutionRecord,
  LearningPattern,
  OptimizationRecommendation,
  LearningInsight,
  AdaptationResult
} from './adaptive-learning-system'

// Temporal Context Database
export { default as TemporalContextDatabase } from './temporal-context-database'
export type {
  TemporalDatabaseConfig,
  TemporalNode,
  TemporalRelationship,
  TemporalQuery,
  QueryResult,
  EvolutionAnalysis,
  ContextSnapshot
} from './temporal-context-database'

// Real-time Collaboration System
export { default as RealtimeCollaborationSystem } from './realtime-collaboration-system'
export type {
  CollaborationConfig,
  CollaborativeSession,
  AgentParticipant,
  Contribution,
  Conflict,
  SharedWorkspace,
  ConsensusRequest,
  CollaborationMetrics
} from './realtime-collaboration-system'

// Dynamic Optimization System
export { default as DynamicOptimizationSystem } from './dynamic-optimization-system'
export type {
  OptimizationConfig,
  AgentConfiguration,
  OptimizationCandidate,
  OptimizationExperiment,
  OptimizationResult,
  OptimizationInsight
} from './dynamic-optimization-system'

// Agent Marketplace
export { default as AgentMarketplace } from './agent-marketplace'
export type {
  MarketplaceConfig,
  AgentPlugin,
  InstalledPlugin,
  PluginInstallation,
  MarketplaceQuery,
  MarketplaceResult,
  PluginUpdate
} from './agent-marketplace'

// Advanced Monitoring System
export { default as AdvancedMonitoringSystem } from './advanced-monitoring-system'
export type {
  MonitoringConfig,
  MetricDefinition,
  MetricValue,
  MonitoringDashboard,
  PerformanceInsight,
  PredictiveModel,
  AnomalyDetection
} from './advanced-monitoring-system'

/**
 * Advanced Features Manager
 * 
 * Centralized manager for all advanced framework features
 */
export class AdvancedFeaturesManager {
  private adaptiveLearning?: AdaptiveLearningSystem
  private temporalDatabase?: TemporalContextDatabase
  private collaboration?: RealtimeCollaborationSystem
  private optimization?: DynamicOptimizationSystem
  private marketplace?: AgentMarketplace
  private monitoring?: AdvancedMonitoringSystem

  constructor(private config: AdvancedFeaturesConfig = {}) {}

  /**
   * Initialize all advanced features
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing Advanced Features...')

    // Initialize Adaptive Learning System
    if (this.config.adaptiveLearning?.enabled !== false) {
      this.adaptiveLearning = new AdaptiveLearningSystem(this.config.adaptiveLearning)
    }

    // Initialize Temporal Context Database
    if (this.config.temporalDatabase?.enabled !== false) {
      this.temporalDatabase = new TemporalContextDatabase(this.config.temporalDatabase)
    }

    // Initialize Real-time Collaboration
    if (this.config.collaboration?.enabled !== false) {
      this.collaboration = new RealtimeCollaborationSystem(this.config.collaboration)
    }

    // Initialize Dynamic Optimization
    if (this.config.optimization?.enabled !== false) {
      this.optimization = new DynamicOptimizationSystem(this.config.optimization)
    }

    // Initialize Agent Marketplace
    if (this.config.marketplace?.enabled !== false) {
      this.marketplace = new AgentMarketplace(this.config.marketplace)
    }

    // Initialize Advanced Monitoring
    if (this.config.monitoring?.enabled !== false) {
      this.monitoring = new AdvancedMonitoringSystem(this.config.monitoring)
    }

    console.log('✅ Advanced Features initialized')
  }

  /**
   * Get adaptive learning system
   */
  getAdaptiveLearning(): AdaptiveLearningSystem | undefined {
    return this.adaptiveLearning
  }

  /**
   * Get temporal database
   */
  getTemporalDatabase(): TemporalContextDatabase | undefined {
    return this.temporalDatabase
  }

  /**
   * Get collaboration system
   */
  getCollaboration(): RealtimeCollaborationSystem | undefined {
    return this.collaboration
  }

  /**
   * Get optimization system
   */
  getOptimization(): DynamicOptimizationSystem | undefined {
    return this.optimization
  }

  /**
   * Get marketplace
   */
  getMarketplace(): AgentMarketplace | undefined {
    return this.marketplace
  }

  /**
   * Get monitoring system
   */
  getMonitoring(): AdvancedMonitoringSystem | undefined {
    return this.monitoring
  }

  /**
   * Record agent execution for learning and monitoring
   */
  recordAgentExecution(agentId: string, executionData: AgentExecutionData): void {
    // Record for adaptive learning
    if (this.adaptiveLearning && executionData.learningRecord) {
      this.adaptiveLearning.recordExecution(executionData.learningRecord)
    }

    // Store context snapshot
    if (this.temporalDatabase && executionData.contextSnapshot) {
      this.temporalDatabase.storeContextSnapshot(executionData.contextSnapshot)
    }

    // Record performance metrics
    if (this.monitoring && executionData.metrics) {
      for (const [metric, value] of Object.entries(executionData.metrics)) {
        this.monitoring.recordMetric(metric, value, { agent_id: agentId })
      }
    }

    // Record optimization performance
    if (this.optimization && executionData.performance) {
      this.optimization.recordPerformance(agentId, executionData.performance)
    }

    // Record marketplace plugin usage
    if (this.marketplace && executionData.pluginUsage) {
      for (const usage of executionData.pluginUsage) {
        this.marketplace.recordPluginExecution(
          usage.pluginId,
          usage.executionTime,
          usage.success,
          usage.dataProcessed
        )
      }
    }
  }

  /**
   * Get comprehensive insights across all systems
   */
  async getComprehensiveInsights(agentId?: string): Promise<ComprehensiveInsights> {
    const insights: ComprehensiveInsights = {
      learning: [],
      optimization: [],
      performance: [],
      collaboration: [],
      marketplace: []
    }

    // Get learning insights
    if (this.adaptiveLearning) {
      insights.learning = this.adaptiveLearning.getLearningInsights(agentId)
    }

    // Get optimization recommendations
    if (this.optimization && agentId) {
      insights.optimization = this.optimization.getOptimizationRecommendations(agentId)
    }

    // Get performance insights
    if (this.monitoring) {
      insights.performance = await this.monitoring.generateInsights()
    }

    // Get collaboration metrics
    if (this.collaboration) {
      // Would get collaboration insights if available
    }

    // Get marketplace insights
    if (this.marketplace) {
      // Would get marketplace insights if available
    }

    return insights
  }

  /**
   * Shutdown all advanced features
   */
  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down Advanced Features...')

    const shutdownPromises: Promise<void>[] = []

    if (this.adaptiveLearning) {
      shutdownPromises.push(this.adaptiveLearning.shutdown())
    }

    if (this.temporalDatabase) {
      shutdownPromises.push(this.temporalDatabase.shutdown())
    }

    if (this.collaboration) {
      shutdownPromises.push(this.collaboration.shutdown())
    }

    if (this.optimization) {
      shutdownPromises.push(this.optimization.shutdown())
    }

    if (this.marketplace) {
      shutdownPromises.push(this.marketplace.shutdown())
    }

    if (this.monitoring) {
      shutdownPromises.push(this.monitoring.shutdown())
    }

    await Promise.all(shutdownPromises)
    console.log('✅ Advanced Features shutdown complete')
  }
}

// Configuration interfaces
export interface AdvancedFeaturesConfig {
  adaptiveLearning?: Partial<LearningConfiguration>
  temporalDatabase?: Partial<TemporalDatabaseConfig>
  collaboration?: Partial<CollaborationConfig>
  optimization?: Partial<OptimizationConfig>
  marketplace?: Partial<MarketplaceConfig>
  monitoring?: Partial<MonitoringConfig>
}

export interface AgentExecutionData {
  learningRecord?: ExecutionRecord
  contextSnapshot?: ContextSnapshot
  metrics?: Record<string, number>
  performance?: any
  pluginUsage?: PluginUsageRecord[]
}

export interface PluginUsageRecord {
  pluginId: string
  executionTime: number
  success: boolean
  dataProcessed: number
}

export interface ComprehensiveInsights {
  learning: LearningInsight[]
  optimization: OptimizationRecommendation[]
  performance: PerformanceInsight[]
  collaboration: any[]
  marketplace: any[]
}

// Re-export types for convenience
import type { LearningConfiguration, ExecutionRecord, LearningInsight, OptimizationRecommendation } from './adaptive-learning-system'
import type { TemporalDatabaseConfig, ContextSnapshot } from './temporal-context-database'
import type { CollaborationConfig } from './realtime-collaboration-system'
import type { OptimizationConfig } from './dynamic-optimization-system'
import type { MarketplaceConfig } from './agent-marketplace'
import type { MonitoringConfig, PerformanceInsight } from './advanced-monitoring-system'
import AdaptiveLearningSystem from './adaptive-learning-system'
import TemporalContextDatabase from './temporal-context-database'
import RealtimeCollaborationSystem from './realtime-collaboration-system'
import DynamicOptimizationSystem from './dynamic-optimization-system'
import AgentMarketplace from './agent-marketplace'
import AdvancedMonitoringSystem from './advanced-monitoring-system'

export default AdvancedFeaturesManager
