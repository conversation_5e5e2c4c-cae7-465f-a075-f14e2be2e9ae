"use client"

import { useState } from "react"
import PlanningModeSelector from "@/components/planning-mode-selector"
import PlanningPreferences from "@/components/planning-preferences"
import { usePlanningSession } from "@/hooks/use-planning-session"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import UsageAnalytics from "@/components/usage-analytics"
import {
  Activity,
  Brain,
  Code,
  Database,
  GitBranch,
  Play,
  Pause,
  Settings,
  Users,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
  Bot,
  Target,
} from "lucide-react"

interface Agent {
  id: string
  name: string
  type: string
  status: "active" | "idle" | "error"
  currentTask?: string
  completedTasks: number
  icon: any
}

interface Project {
  id: string
  name: string
  status: "planning" | "development" | "testing" | "completed"
  progress: number
  tasksTotal: number
  tasksCompleted: number
  agents: string[]
}

interface AdminDashboardProps {
  onBack: () => void
}

export default function AdminDashboard({ onBack }: AdminDashboardProps) {
  const {
    currentSession,
    preferences,
    startPlanning,
    pausePlanning,
    resumePlanning,
    stopPlanning,
    updatePreferences,
    getSessionMetrics
  } = usePlanningSession()

  const [currentMode, setCurrentMode] = useState<"autonomous" | "copilot">(preferences.defaultMode)

  const handleModeChange = (mode: "autonomous" | "copilot") => {
    setCurrentMode(mode)
    updatePreferences({ defaultMode: mode })
  }

  const [agents, setAgents] = useState<Agent[]>([
    {
      id: "1",
      name: "Project Planning Agent",
      type: "planning",
      status: "active",
      currentTask: "Analyzing new e-commerce project requirements",
      completedTasks: 12,
      icon: Brain,
    },
    {
      id: "2",
      name: "Context Engine",
      type: "analysis",
      status: "active",
      currentTask: "Ingesting React codebase (2.3M LOC)",
      completedTasks: 45,
      icon: Database,
    },
    {
      id: "3",
      name: "Task Planning Agent",
      type: "coordination",
      status: "active",
      currentTask: "Breaking down authentication feature",
      completedTasks: 28,
      icon: Users,
    },
    {
      id: "4",
      name: "Frontend Coding Agent",
      type: "coding",
      status: "active",
      currentTask: "Implementing user dashboard components",
      completedTasks: 67,
      icon: Code,
    },
    {
      id: "5",
      name: "Backend Coding Agent",
      type: "coding",
      status: "idle",
      completedTasks: 34,
      icon: GitBranch,
    },
    {
      id: "6",
      name: "Testing Agent",
      type: "testing",
      status: "active",
      currentTask: "Running integration tests for API endpoints",
      completedTasks: 23,
      icon: CheckCircle,
    },
  ])

  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      name: "E-commerce Platform",
      status: "development",
      progress: 65,
      tasksTotal: 45,
      tasksCompleted: 29,
      agents: ["1", "2", "3", "4"],
    },
    {
      id: "2",
      name: "Analytics Dashboard",
      status: "testing",
      progress: 85,
      tasksTotal: 32,
      tasksCompleted: 27,
      agents: ["2", "4", "6"],
    },
    {
      id: "3",
      name: "Mobile App Backend",
      status: "planning",
      progress: 15,
      tasksTotal: 28,
      tasksCompleted: 4,
      agents: ["1", "3"],
    },
  ])

  const [systemMetrics, setSystemMetrics] = useState({
    totalProjects: 3,
    activeAgents: 5,
    tasksCompleted: 239,
    systemUptime: "99.8%",
    avgResponseTime: "120ms",
    codeQualityScore: 94,
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500"
      case "idle":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      case "planning":
        return "bg-blue-500"
      case "development":
        return "bg-red-500"
      case "testing":
        return "bg-orange-500"
      case "completed":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Play className="w-4 h-4" />
      case "idle":
        return <Pause className="w-4 h-4" />
      case "error":
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  return (
    <div className="min-h-screen bg-[#000000] text-[#e5e5e5] p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-lg"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Editor
            </Button>
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  AI Development Ecosystem
                </h1>
                <p className="text-[#666] text-sm">System monitoring and agent management</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-green-600/20 text-green-400 border-green-600/30 hover:bg-green-600/30">
              System Online
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              className="text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-lg"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-400" />
                <div>
                  <p className="text-sm text-[#666]">Active Projects</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.totalProjects}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-green-400" />
                <div>
                  <p className="text-sm text-[#666]">Active Agents</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.activeAgents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-purple-400" />
                <div>
                  <p className="text-sm text-[#666]">Tasks Completed</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.tasksCompleted}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-orange-400" />
                <div>
                  <p className="text-sm text-[#666]">System Uptime</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.systemUptime}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-indigo-400" />
                <div>
                  <p className="text-sm text-[#666]">Avg Response</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.avgResponseTime}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Code className="w-5 h-5 text-red-400" />
                <div>
                  <p className="text-sm text-[#666]">Code Quality</p>
                  <p className="text-2xl font-bold text-white">{systemMetrics.codeQualityScore}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Core Components */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-white">Core Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-600/20 rounded-lg">
                    <Brain className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">Planner</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Breaks down tasks into steps</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-600/20 rounded-lg">
                    <Play className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">Executor</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Runs code edits, tool calls, or commands</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-600/20 rounded-lg">
                    <Database className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">Memory Store</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Tracks context, history, and results</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-600/20 rounded-lg">
                    <Zap className="w-5 h-5 text-orange-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">LLM Connector</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Interfaces with large language models</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-600/20 rounded-lg">
                    <Settings className="w-5 h-5 text-red-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">Tool Registry</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Catalog of callable tools/plugins</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-indigo-600/20 rounded-lg">
                    <GitBranch className="w-5 h-5 text-indigo-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">Workflow Engine</CardTitle>
                    <CardDescription className="text-[#666] text-xs">Orchestrates multi-step, branching flows</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-cyan-600/20 rounded-lg">
                    <Users className="w-5 h-5 text-cyan-400" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-sm">User Interface</CardTitle>
                    <CardDescription className="text-[#666] text-xs">IDE plugin, CLI, or web interface</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="planning" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-[#1a1a1a] border-[#2a2a2a]">
            <TabsTrigger value="planning" className="data-[state=active]:bg-[#0a0a0a] data-[state=active]:text-white text-[#666]">Planning</TabsTrigger>
            <TabsTrigger value="agents" className="data-[state=active]:bg-[#0a0a0a] data-[state=active]:text-white text-[#666]">AI Agents</TabsTrigger>
            <TabsTrigger value="projects" className="data-[state=active]:bg-[#0a0a0a] data-[state=active]:text-white text-[#666]">Projects</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-[#0a0a0a] data-[state=active]:text-white text-[#666]">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="planning" className="space-y-6">
            <PlanningModeSelector
              currentMode={currentMode}
              onModeChange={handleModeChange}
              session={currentSession}
              onStartPlanning={startPlanning}
              onPausePlanning={pausePlanning}
              onResumeePlanning={resumePlanning}
              onStopPlanning={stopPlanning}
            />
            
            <PlanningPreferences
              preferences={preferences}
              onUpdatePreferences={updatePreferences}
              onReset={() => {
                // Reset to default preferences
                updatePreferences({
                  defaultMode: "copilot",
                  autoSave: true,
                  interventionPoints: {
                    requirementsReview: true,
                    designReview: true,
                    tasksReview: true
                  },
                  notifications: {
                    phaseCompletion: true,
                    errorAlerts: true,
                    progressUpdates: false
                  }
                })
              }}
            />
          </TabsContent>

          <TabsContent value="agents" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map((agent) => {
                const IconComponent = agent.icon
                return (
                  <Card key={agent.id} className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-600/20 rounded-lg">
                            <IconComponent className="w-5 h-5 text-blue-400" />
                          </div>
                          <div>
                            <CardTitle className="text-lg text-white">{agent.name}</CardTitle>
                            <CardDescription className="capitalize text-[#666]">{agent.type}</CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                          <div className="text-[#666]">{getStatusIcon(agent.status)}</div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {agent.currentTask && (
                          <div>
                            <p className="text-sm font-medium text-[#888]">Current Task:</p>
                            <p className="text-sm text-[#666]">{agent.currentTask}</p>
                          </div>
                        )}
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-[#666]">Completed Tasks:</span>
                          <Badge className="bg-[#1a1a1a] text-[#888] border-[#2a2a2a]">{agent.completedTasks}</Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="flex-1 bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]">
                            View Logs
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1 bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]">
                            Configure
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>

          <TabsContent value="projects" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {projects.map((project) => (
                <Card key={project.id} className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg hover:shadow-xl transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl text-white">{project.name}</CardTitle>
                        <CardDescription className="capitalize text-[#666]">{project.status} phase</CardDescription>
                      </div>
                      <Badge className={`${getStatusColor(project.status)} text-white border-0`}>{project.status}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span className="text-[#666]">Progress</span>
                        <span className="text-white">{project.progress}%</span>
                      </div>
                      <Progress value={project.progress} className="h-2 bg-[#1a1a1a]" />
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-[#666]">Tasks:</span>
                      <span className="text-white">
                        {project.tasksCompleted}/{project.tasksTotal} completed
                      </span>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-[#888] mb-2">Active Agents:</p>
                      <div className="flex flex-wrap gap-2">
                        {project.agents.map((agentId) => {
                          const agent = agents.find((a) => a.id === agentId)
                          return agent ? (
                            <Badge key={agentId} className="text-xs bg-[#1a1a1a] text-[#888] border-[#2a2a2a]">
                              {agent.name.split(" ")[0]}
                            </Badge>
                          ) : null
                        })}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700 text-white">
                        View Details
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1 bg-transparent border-[#2a2a2a] text-[#888] hover:text-white hover:bg-[#1a1a1a]">
                        Manage Tasks
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <UsageAnalytics />
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
              <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
                <CardHeader>
                  <CardTitle className="text-white">System Performance</CardTitle>
                  <CardDescription className="text-[#666]">Real-time system metrics and health indicators</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#666]">CPU Usage</span>
                      <span className="text-sm font-medium text-white">45%</span>
                    </div>
                    <Progress value={45} className="h-2 bg-[#1a1a1a]" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#666]">Memory Usage</span>
                      <span className="text-sm font-medium text-white">62%</span>
                    </div>
                    <Progress value={62} className="h-2 bg-[#1a1a1a]" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#666]">Network I/O</span>
                      <span className="text-sm font-medium text-white">28%</span>
                    </div>
                    <Progress value={28} className="h-2 bg-[#1a1a1a]" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#666]">Database Load</span>
                      <span className="text-sm font-medium text-white">71%</span>
                    </div>
                    <Progress value={71} className="h-2 bg-[#1a1a1a]" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-[#0a0a0a] border-[#1a1a1a] shadow-lg">
                <CardHeader>
                  <CardTitle className="text-white">Agent Productivity</CardTitle>
                  <CardDescription className="text-[#666]">Task completion rates and efficiency metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    {agents.slice(0, 4).map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                          <span className="text-sm font-medium text-white">{agent.name.split(" ")[0]}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-[#666]">{agent.completedTasks} tasks</span>
                          <Badge className="text-xs bg-[#1a1a1a] text-[#888] border-[#2a2a2a]">
                            {Math.floor(Math.random() * 20 + 80)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
