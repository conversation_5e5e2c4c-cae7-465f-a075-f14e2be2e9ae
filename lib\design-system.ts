// Vercel Geist Design System Configuration
// Based on Vercel's design principles and Geist UI

export const geistColors = {
  // Geist Gray Scale
  gray: {
    50: '#fafafa',
    100: '#f5f5f5', 
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },
  
  // Geist Accent Colors
  blue: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // Status Colors
  success: '#00d924',
  warning: '#f5a623',
  error: '#e60026',
  
  // Vercel Brand
  vercel: {
    black: '#000000',
    white: '#ffffff',
    gray: '#666666',
  }
}

export const geistSpacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '1rem',       // 16px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '3rem',    // 48px
  '3xl': '4rem',    // 64px
}

export const geistBorderRadius = {
  none: '0',
  sm: '0.25rem',    // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',
}

export const geistShadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
}

// Component-specific design tokens
export const geistComponents = {
  badge: {
    variants: {
      default: {
        bg: geistColors.gray[100],
        text: geistColors.gray[900],
        border: geistColors.gray[200],
      },
      secondary: {
        bg: geistColors.gray[800],
        text: geistColors.gray[100],
        border: geistColors.gray[700],
      },
      success: {
        bg: '#dcfce7',
        text: '#166534',
        border: '#bbf7d0',
      },
      warning: {
        bg: '#fef3c7',
        text: '#92400e',
        border: '#fde68a',
      },
      error: {
        bg: '#fee2e2',
        text: '#991b1b',
        border: '#fecaca',
      },
      outline: {
        bg: 'transparent',
        text: geistColors.gray[700],
        border: geistColors.gray[300],
      },
      // Dark theme variants
      dark: {
        default: {
          bg: geistColors.gray[800],
          text: geistColors.gray[100],
          border: geistColors.gray[700],
        },
        secondary: {
          bg: geistColors.gray[700],
          text: geistColors.gray[200],
          border: geistColors.gray[600],
        },
        outline: {
          bg: 'transparent',
          text: geistColors.gray[300],
          border: geistColors.gray[600],
        },
      }
    }
  },
  
  tabs: {
    list: {
      bg: geistColors.gray[100],
      border: geistColors.gray[200],
      dark: {
        bg: geistColors.gray[900],
        border: geistColors.gray[800],
      }
    },
    trigger: {
      inactive: {
        text: geistColors.gray[600],
        hover: {
          text: geistColors.gray[900],
          bg: geistColors.gray[50],
        }
      },
      active: {
        text: geistColors.gray[900],
        bg: geistColors.white,
        border: geistColors.gray[200],
      },
      dark: {
        inactive: {
          text: geistColors.gray[400],
          hover: {
            text: geistColors.gray[100],
            bg: geistColors.gray[800],
          }
        },
        active: {
          text: geistColors.white,
          bg: geistColors.gray[800],
          border: geistColors.gray[700],
        }
      }
    }
  },
  
  card: {
    bg: geistColors.white,
    border: geistColors.gray[200],
    shadow: geistShadows.sm,
    dark: {
      bg: geistColors.gray[900],
      border: geistColors.gray[800],
    }
  }
}

// Utility functions for consistent styling
export const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ')
}

export const getGeistBadgeClasses = (variant: keyof typeof geistComponents.badge.variants = 'default', isDark = true) => {
  const colors = isDark 
    ? geistComponents.badge.variants.dark?.[variant as keyof typeof geistComponents.badge.variants.dark] || geistComponents.badge.variants[variant]
    : geistComponents.badge.variants[variant]
  
  return cn(
    'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
    `bg-[${colors.bg}] text-[${colors.text}] ring-[${colors.border}]`
  )
}

export const getGeistTabClasses = (isActive: boolean, isDark = true) => {
  const theme = isDark ? geistComponents.tabs.trigger.dark : geistComponents.tabs.trigger
  const state = isActive ? theme.active : theme.inactive
  
  return cn(
    'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
    isActive 
      ? `bg-[${state.bg}] text-[${state.text}] shadow-sm`
      : `text-[${state.text}] hover:bg-[${state.hover?.bg}] hover:text-[${state.hover?.text}]`
  )
}
