import { NextResponse } from 'next/server'
import { availableModels, validateEnvironment, getRecommendedModels, checkProviderHealth, aiProviders } from '@/lib/ai-config'
import { usageTracker } from '@/lib/usage-tracker'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const includeMetrics = searchParams.get('metrics') === 'true'
    const category = searchParams.get('category')
    
    // Validate environment
    const isValid = validateEnvironment()
    
    // Filter models based on available API keys
    const filteredModels = availableModels.filter(model => {
      switch (model.provider) {
        case 'openrouter':
          return !!process.env.OPENROUTER_API_KEY
        case 'openai':
          return !!process.env.OPENAI_API_KEY
        case 'anthropic':
          return !!process.env.ANTHROPIC_API_KEY
        case 'google':
          return !!process.env.GOOGLE_GENERATIVE_AI_API_KEY
        default:
          return false
      }
    })

    // Filter by category if specified
    let models = filteredModels
    if (category) {
      models = filteredModels.filter(m => m.category === category)
    }

    // Get provider health status
    const providerHealth = {}
    for (const provider of Object.keys(aiProviders)) {
      try {
        providerHealth[provider] = await checkProviderHealth(provider)
      } catch {
        providerHealth[provider] = false
      }
    }

    // Get recommendations
    const recommendations = getRecommendedModels()

    // Include usage metrics if requested
    let metrics = {}
    if (includeMetrics) {
      const modelMetrics = usageTracker.getModelMetrics() as Map<string, any>
      metrics = Object.fromEntries(modelMetrics)
    }

    return NextResponse.json({
      models,
      environmentValid: isValid,
      availableProviders: [...new Set(filteredModels.map(m => m.provider))],
      providerHealth,
      recommendations,
      categories: {
        coding: filteredModels.filter(m => m.category === 'coding').length,
        general: filteredModels.filter(m => m.category === 'general').length,
        efficient: filteredModels.filter(m => m.category === 'efficient').length,
        multimodal: filteredModels.filter(m => m.category === 'multimodal').length,
        longContext: filteredModels.filter(m => m.category === 'long-context').length
      },
      ...(includeMetrics && { metrics })
    })
  } catch (error) {
    console.error('Error fetching models:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available models' },
      { status: 500 }
    )
  }
}

// Get detailed information about a specific model
export async function POST(request: Request) {
  try {
    const { modelId } = await request.json()
    
    if (!modelId) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      )
    }

    const model = availableModels.find(m => m.value === modelId)
    if (!model) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }

    // Get usage metrics for this specific model
    const metrics = usageTracker.getModelMetrics(modelId)
    
    return NextResponse.json({
      model,
      metrics,
      recommendations: usageTracker.getModelRecommendations()
    })
  } catch (error) {
    console.error('Error fetching model details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch model details' },
      { status: 500 }
    )
  }
}
