# Vercel AI SDK Setup Guide

## 🚀 Quick Setup

### 1. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.local.example .env.local
   ```

2. Add your API keys to `.env.local`:
   ```env
   # Required - Get from https://platform.openai.com/api-keys
   OPENAI_API_KEY=sk-your-openai-key-here
   
   # Optional - Get from https://console.anthropic.com/
   ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
   
   # Optional - Get from https://aistudio.google.com/app/apikey
   GOOGLE_GENERATIVE_AI_API_KEY=your-google-key-here
   ```

### 2. Test the Integration

1. Start the development server:
   ```bash
   pnpm dev
   ```

2. Visit the demo page:
   ```
   http://localhost:3000/ai-demo
   ```

3. Try the different AI features:
   - **Chat**: Real-time conversation with AI
   - **Code Generation**: Generate complete applications
   - **Assistant**: AI-powered task planning

## 🔧 Configuration Options

### Model Selection

The system automatically detects available models based on your API keys:

- **OpenAI Models**: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-3.5-turbo
- **Anthropic Models**: claude-3-5-sonnet, claude-3-5-haiku, claude-3-opus  
- **Google Models**: gemini-1.5-pro, gemini-1.5-flash, gemini-pro

### Environment Variables

```env
# AI Provider Configuration
DEFAULT_AI_MODEL=gpt-4o-mini          # Default model to use
DEFAULT_AI_PROVIDER=openai            # Default provider
MAX_TOKENS_PER_REQUEST=4000           # Maximum tokens per request
MAX_REQUESTS_PER_MINUTE=10            # Rate limiting
```

## 🎯 Usage Examples

### Basic Chat Integration

```tsx
import AIChat from '@/components/ai-chat'

export default function MyPage() {
  return (
    <div className="h-screen">
      <AIChat 
        systemPrompt="You are a helpful assistant."
        defaultModel="gpt-4o-mini"
      />
    </div>
  )
}
```

### Code Generation

```tsx
import AICodeGenerator from '@/components/ai/ai-code-generator'

export default function CodePage() {
  return <AICodeGenerator />
}
```

### AI Assistant

```tsx
import AIAssistant from '@/components/ai-assistant'

export default function AssistantPage() {
  return (
    <AIAssistant 
      onTaskGenerated={(tasks) => console.log(tasks)}
      systemPrompt="You are a project planning assistant."
    />
  )
}
```

## 🛠 Customization

### Custom System Prompts

Tailor the AI behavior for your specific use case:

```tsx
const systemPrompts = {
  codeReview: "You are a senior developer reviewing code. Focus on best practices, security, and performance.",
  documentation: "You are a technical writer. Create clear, comprehensive documentation.",
  debugging: "You are a debugging expert. Help identify and fix issues in code."
}

<AIChat systemPrompt={systemPrompts.codeReview} />
```

### Adding New Providers

1. Install the provider package:
   ```bash
   pnpm add @ai-sdk/provider-name
   ```

2. Update `lib/ai-config.ts`:
   ```tsx
   import { newProvider } from '@ai-sdk/provider-name'
   
   export const aiProviders = {
     // ... existing providers
     newProvider: {
       provider: newProvider,
       models: {
         'model-name': newProvider('model-name'),
       }
     }
   }
   ```

## 🔒 Security Best Practices

1. **API Keys**: Never expose API keys in client-side code
2. **Rate Limiting**: Configure appropriate limits for your use case
3. **Input Validation**: Validate all user inputs before sending to AI
4. **Error Handling**: Implement proper error handling for API failures

## 🐛 Troubleshooting

### Common Issues

1. **"Missing API Key" Error**
   - Ensure your `.env.local` file has the correct API key
   - Restart the development server after adding keys

2. **"Rate Limit Exceeded"**
   - Check your API usage limits
   - Implement request queuing if needed

3. **Model Not Available**
   - Verify the model name is correct
   - Check if you have access to the specific model

### Debug Mode

Enable debug logging by adding to your `.env.local`:
```env
DEBUG=ai-sdk:*
```

## 📚 Next Steps

1. **Explore Components**: Check out all AI components in `/components/ai/`
2. **Read Documentation**: Review `README-AI-SDK.md` for detailed API reference
3. **Customize**: Adapt the components for your specific needs
4. **Deploy**: Configure environment variables for production deployment

## 🆘 Support

- **Documentation**: See `README-AI-SDK.md`
- **Examples**: Visit `/ai-demo` page
- **Vercel AI SDK Docs**: https://sdk.vercel.ai/
- **GitHub Issues**: Report bugs and feature requests

---

**Ready to build amazing AI-powered applications!** 🚀
