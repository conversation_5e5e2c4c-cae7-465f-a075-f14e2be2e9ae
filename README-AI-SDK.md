# Vercel AI SDK Integration

This project has been enhanced with the Vercel AI SDK to provide powerful AI capabilities including chat, code generation, and intelligent assistance.

## 🚀 Features

- **Real-time AI Chat**: Stream responses from multiple AI providers (OpenAI, Anthropic, Google)
- **Code Generation**: Generate complete, production-ready code with AI assistance
- **Intelligent Assistant**: AI-powered task planning and execution
- **Multi-Provider Support**: Switch between different AI models seamlessly
- **Structured Output**: Extract and organize AI responses into actionable formats

## 📦 Installed Packages

```bash
# Core AI SDK packages
ai                    # Main AI SDK
@ai-sdk/openai       # OpenAI provider
@ai-sdk/anthropic    # Anthropic (Claude) provider
@ai-sdk/google       # Google AI provider
@ai-sdk/react        # React hooks and utilities
```

## 🔧 Configuration

### Environment Variables

Copy `.env.local.example` to `.env.local` and add your API keys:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional (for additional providers)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# Configuration
DEFAULT_AI_MODEL=gpt-4o-mini
DEFAULT_AI_PROVIDER=openai
MAX_TOKENS_PER_REQUEST=4000
```

### Available Models

The system automatically detects available models based on your API keys:

- **OpenAI**: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-3.5-turbo
- **Anthropic**: claude-3-5-sonnet, claude-3-5-haiku, claude-3-opus
- **Google**: gemini-1.5-pro, gemini-1.5-flash, gemini-pro

## 🎯 Usage Examples

### 1. Basic AI Chat

```tsx
import AIChat from '@/components/ai-chat'

export default function ChatPage() {
  return (
    <div className="h-screen">
      <AIChat 
        systemPrompt="You are a helpful coding assistant."
        defaultModel="gpt-4o-mini"
        defaultProvider="openai"
      />
    </div>
  )
}
```

### 2. AI Assistant with Task Management

```tsx
import AIAssistant from '@/components/ai-assistant'

export default function AssistantPage() {
  const handleTaskGenerated = (tasks) => {
    console.log('Generated tasks:', tasks)
    // Handle the generated tasks
  }

  return (
    <AIAssistant 
      onTaskGenerated={handleTaskGenerated}
      systemPrompt="You are a project management AI. Help break down complex tasks."
    />
  )
}
```

### 3. Code Generation

```tsx
import AICodeGenerator from '@/components/ai/ai-code-generator'

export default function CodeGenPage() {
  return (
    <div className="container mx-auto p-4">
      <AICodeGenerator />
    </div>
  )
}
```

### 4. Custom AI Integration

```tsx
import { useChat } from '@ai-sdk/react'

export default function CustomAI() {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    body: {
      provider: 'openai',
      model: 'gpt-4o-mini',
      system: 'You are a specialized AI assistant.',
    }
  })

  return (
    <div>
      {/* Your custom UI */}
    </div>
  )
}
```

## 🛠 API Routes

### `/api/chat` - Main Chat Endpoint

Handles streaming AI responses with support for multiple providers.

**Request:**
```json
{
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "provider": "openai",
  "model": "gpt-4o-mini",
  "system": "You are a helpful assistant.",
  "maxTokens": 4000,
  "temperature": 0.7
}
```

### `/api/models` - Available Models

Returns list of available models based on configured API keys.

**Response:**
```json
{
  "models": [
    {
      "provider": "openai",
      "model": "gpt-4o-mini",
      "displayName": "OpenAI - gpt-4o-mini",
      "value": "openai:gpt-4o-mini"
    }
  ],
  "environmentValid": true,
  "availableProviders": ["openai"]
}
```

## 🎨 Components

### AIChat
- Real-time streaming chat interface
- Model selection dropdown
- Message history with timestamps
- Copy, like/dislike functionality
- Error handling and retry

### AIAssistant  
- Task planning and management
- Integrated chat for AI assistance
- Progress tracking
- Task extraction from AI responses

### AICodeGenerator
- Multi-file code generation
- Language/framework selection
- Tabbed interface for input/output
- Download individual or all files
- Syntax highlighting ready

## 🔒 Security Notes

- API keys are server-side only
- Rate limiting configured per environment
- Input validation on all endpoints
- Error handling without exposing sensitive data

## 🚀 Getting Started

1. Install dependencies: `pnpm install`
2. Copy environment file: `cp .env.local.example .env.local`
3. Add your API keys to `.env.local`
4. Start development: `pnpm dev`
5. Visit the components in your app

## 📚 Next Steps

- Explore the enhanced components in `/components/ai/`
- Customize system prompts for your use case
- Add additional AI providers as needed
- Implement custom tools and function calling
- Add vector embeddings for RAG capabilities
