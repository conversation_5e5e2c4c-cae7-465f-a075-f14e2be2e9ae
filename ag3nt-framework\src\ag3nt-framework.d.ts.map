{"version": 3, "file": "ag3nt-framework.d.ts", "sourceRoot": "", "sources": ["ag3nt-framework.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;GAiBG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AACrC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAA;AACzD,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAA;AACtE,OAAO,EAAE,0BAA0B,EAAE,MAAM,4BAA4B,CAAA;AACvE,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAA;AAGjE,OAAO,EAAE,uBAAuB,EAAE,KAAK,sBAAsB,EAAE,MAAM,YAAY,CAAA;AACjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAA;AAC5E,OAAO,EAAE,uBAAuB,EAAE,MAAM,0CAA0C,CAAA;AAClF,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAA;AAChF,OAAO,EAAE,2BAA2B,EAAE,MAAM,8CAA8C,CAAA;AAC1F,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAA;AAC3E,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAA;AACxD,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAA;AAE9D,MAAM,WAAW,eAAe;IAC9B,aAAa,CAAC,EAAE;QACd,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,wBAAwB,CAAC,EAAE,OAAO,CAAA;QAClC,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,uBAAuB,CAAC,EAAE,OAAO,CAAA;KAClC,CAAA;IACD,MAAM,CAAC,EAAE;QACP,qBAAqB,CAAC,EAAE,MAAM,CAAA;QAC9B,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;KACxB,CAAA;IACD,UAAU,CAAC,EAAE;QACX,kBAAkB,CAAC,EAAE,OAAO,CAAA;QAC5B,mBAAmB,CAAC,EAAE,MAAM,CAAA;QAC5B,aAAa,CAAC,EAAE,OAAO,CAAA;KACxB,CAAA;IACD,YAAY,CAAC,EAAE;QACb,oBAAoB,CAAC,EAAE,OAAO,CAAA;QAC9B,eAAe,CAAC,EAAE,OAAO,CAAA;QACzB,sBAAsB,CAAC,EAAE,OAAO,CAAA;QAChC,qBAAqB,CAAC,EAAE,OAAO,CAAA;QAC/B,iBAAiB,CAAC,EAAE,MAAM,CAAA;QAC1B,gBAAgB,CAAC,EAAE,MAAM,CAAA;QACzB,cAAc,CAAC,EAAE,MAAM,CAAA;KACxB,CAAA;IACD,SAAS,CAAC,EAAE;QACV,oBAAoB,CAAC,EAAE,OAAO,CAAA;QAC9B,mBAAmB,CAAC,EAAE,OAAO,CAAA;QAC7B,cAAc,CAAC,EAAE,OAAO,CAAA;QACxB,iBAAiB,CAAC,EAAE,MAAM,CAAA;QAC1B,mBAAmB,CAAC,EAAE,MAAM,CAAA;QAC5B,sBAAsB,CAAC,EAAE,aAAa,GAAG,sBAAsB,GAAG,mBAAmB,GAAG,UAAU,CAAA;KACnG,CAAA;IACD,gBAAgB,CAAC,EAAE,sBAAsB,CAAA;CAC1C;AAED,MAAM,WAAW,aAAa;IAC5B,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,QAAQ,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IACjD,IAAI,CAAC,EAAE,MAAM,EAAE,CAAA;IACf,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,gBAAgB,CAAC,EAAE;QACjB,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,OAAO,CAAC,EAAE,MAAM,CAAA;KACjB,CAAA;CACF;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,CAAC,EAAE,UAAU,CAAA;IACnB,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE;QACR,SAAS,EAAE,MAAM,CAAA;QACjB,OAAO,EAAE,MAAM,CAAA;QACf,cAAc,EAAE,MAAM,CAAA;QACtB,UAAU,EAAE,MAAM,CAAA;KACnB,CAAA;CACF;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,QAAQ,CAAe;IAC/B,OAAO,CAAC,aAAa,CAA4B;IACjD,OAAO,CAAC,mBAAmB,CAAqB;IAChD,OAAO,CAAC,aAAa,CAAoC;IACzD,OAAO,CAAC,gBAAgB,CAAC,CAAyB;IAGlD,OAAO,CAAC,gBAAgB,CAAC,CAAsB;IAC/C,OAAO,CAAC,eAAe,CAAC,CAAyB;IACjD,OAAO,CAAC,cAAc,CAAC,CAAwB;IAC/C,OAAO,CAAC,eAAe,CAAC,CAA6B;IAGrD,OAAO,CAAC,gBAAgB,CAAC,CAAuB;IAChD,OAAO,CAAC,YAAY,CAAC,CAAc;IACnC,OAAO,CAAC,eAAe,CAAC,CAAiB;IAEzC,OAAO,CAAC,MAAM,CAAiB;IAC/B,OAAO,CAAC,aAAa,CAAiB;IACtC,OAAO,CAAC,cAAc,CAIR;gBAEF,MAAM,GAAE,eAAoB;IA0DxC;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAwIjC;;OAEG;IACG,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;IAmEtE;;OAEG;IACG,OAAO,CACX,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,GAAG,EACV,MAAM,GAAE,aAAkB,GACzB,OAAO,CAAC,eAAe,CAAC;IAkF3B;;OAEG;IACH,QAAQ,IAAI;QACV,SAAS,EAAE;YACT,WAAW,EAAE,OAAO,CAAA;YACpB,cAAc,EAAE,MAAM,CAAA;YACtB,WAAW,EAAE,MAAM,CAAA;SACpB,CAAA;QACD,MAAM,EAAE,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAA;QAC7C,aAAa,EAAE,UAAU,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAA;QACjE,SAAS,EAAE,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAA;QACtD,aAAa,EAAE;YACb,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,OAAO,CAAA;YACnB,UAAU,EAAE,OAAO,CAAA;YACnB,KAAK,CAAC,EAAE,UAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAA;SACnD,CAAA;KACF;IAmBD;;OAEG;IACH,SAAS,IAAI,eAAe,EAAE;IAI9B;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,GAAG,eAAe,GAAG,IAAI;IAiCjD;;OAEG;YACW,qBAAqB;IA+JnC;;OAEG;IACH,mBAAmB,IAAI,uBAAuB,GAAG,SAAS;IAI1D;;OAEG;IACH,mBAAmB;IAInB;;OAEG;IACH,mBAAmB;IAInB;;OAEG;IACH,gBAAgB;IAIhB;;OAEG;IACH,eAAe;IAIf;;OAEG;IACH,cAAc;IAId;;OAEG;IACH,aAAa;IAIb;;OAEG;IAEH;;OAEG;IACH,mBAAmB,IAAI,oBAAoB,GAAG,SAAS;IAIvD;;OAEG;IACH,kBAAkB,IAAI,uBAAuB,GAAG,SAAS;IAIzD;;OAEG;IACH,iBAAiB,IAAI,sBAAsB,GAAG,SAAS;IAIvD;;OAEG;IACH,kBAAkB,IAAI,2BAA2B,GAAG,SAAS;IAI7D;;OAEG;IACG,YAAY,CAChB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,IAAI,EAAE,GAAG,EACT,cAAc,GAAE,cAAc,GAAG,MAAM,GAAG,WAAW,GAAG,cAA+B,GACtF,OAAO,CAAC,GAAG,CAAC;IAuBf;;OAEG;IACG,uBAAuB,CAC3B,UAAU,EAAE,MAAM,EAClB,KAAK,EAAE,MAAM,EACb,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,GAAG,EAAE,GACb,OAAO,CAAC,GAAG,CAAC;IA6Bf;;OAEG;IACG,eAAe,CACnB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,GAAG,GACT,OAAO,CAAC,GAAG,CAAC;IA6Bf;;OAEG;IACH,4BAA4B,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE,SAAS,GAAE,MAAU,GAAG,IAAI;IAyC/F;;OAEG;IACH,wBAAwB,IAAI,GAAG;IAS/B;;OAEG;IAEH;;OAEG;IACH,mBAAmB,IAAI,qBAAqB,GAAG,SAAS;IAIxD;;OAEG;IACH,eAAe,IAAI,YAAY,GAAG,SAAS;IAI3C;;OAEG;IACH,kBAAkB,IAAI,eAAe,GAAG,SAAS;IAIjD;;OAEG;IACG,cAAc,CAAC,KAAK,GAAE,GAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;IAiBnD;;OAEG;IACG,YAAY,CAAC,OAAO,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAO9C;;OAEG;IACG,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAOvD;;OAEG;IACH,qBAAqB,IAAI,GAAG;CAkC7B;AAGD,eAAO,MAAM,cAAc,gBAAuB,CAAA;AAGlD,eAAe,cAAc,CAAA"}