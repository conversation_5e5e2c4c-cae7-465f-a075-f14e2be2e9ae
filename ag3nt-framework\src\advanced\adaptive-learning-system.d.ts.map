{"version": 3, "file": "adaptive-learning-system.d.ts", "sourceRoot": "", "sources": ["adaptive-learning-system.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAErC,MAAM,WAAW,qBAAqB;IACpC,OAAO,EAAE,OAAO,CAAA;IAChB,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,gBAAgB,EAAE,OAAO,CAAA;IACzB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,kBAAkB,EAAE,cAAc,GAAG,UAAU,GAAG,YAAY,CAAA;CAC/D;AAED,MAAM,WAAW,eAAe;IAC9B,WAAW,EAAE,MAAM,CAAA;IACnB,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;IACjB,IAAI,EAAE,UAAU,CAAA;IAChB,WAAW,EAAE,kBAAkB,CAAA;IAC/B,OAAO,EAAE,gBAAgB,CAAA;IACzB,OAAO,EAAE,gBAAgB,CAAA;IACzB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,UAAU,EAAE,MAAM,CAAA;IAClB,YAAY,EAAE,GAAG,CAAA;IACjB,WAAW,EAAE,GAAG,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,MAAM,CAAA;IAClB,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,CAAA;IACb,aAAa,EAAE,aAAa,CAAA;IAC5B,gBAAgB,EAAE,MAAM,CAAA;IACxB,SAAS,EAAE,MAAM,CAAA;IACjB,cAAc,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,aAAa;IAC5B,GAAG,EAAE,MAAM,CAAA;IACX,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;CACb;AAED,MAAM,WAAW,gBAAgB;IAC/B,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,WAAW,EAAE,GAAG,CAAA;IAChB,eAAe,EAAE,GAAG,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS,CAAA;IACzC,OAAO,EAAE,GAAG,CAAA;IACZ,QAAQ,EAAE,YAAY,CAAA;IACtB,MAAM,EAAE,cAAc,EAAE,CAAA;IACxB,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,YAAY,EAAE,MAAM,CAAA;IACpB,cAAc,EAAE,OAAO,CAAA;CACxB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IAChD,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,EAAE,MAAM,CAAA;IACjB,IAAI,EAAE,aAAa,GAAG,SAAS,GAAG,cAAc,GAAG,SAAS,CAAA;IAC5D,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,gBAAgB,EAAE,CAAA;IAC9B,QAAQ,EAAE,cAAc,EAAE,CAAA;IAC1B,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,GAAG,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,WAAW,EAAE,MAAM,CAAA;IACnB,cAAc,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,0BAA0B;IACzC,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;IACjB,YAAY,EAAE,GAAG,CAAA;IACjB,gBAAgB,EAAE,GAAG,CAAA;IACrB,mBAAmB,EAAE,MAAM,CAAA;IAC3B,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;CACrC;AAED,MAAM,WAAW,iBAAiB;IAChC,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,qBAAqB,CAAA;IAChC,aAAa,EAAE,MAAM,CAAA;IACrB,YAAY,EAAE,MAAM,CAAA;IACpB,aAAa,CAAC,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,qBAAqB;IACpC,IAAI,EAAE,SAAS,GAAG,cAAc,GAAG,UAAU,GAAG,eAAe,CAAA;IAC/D,OAAO,EAAE,GAAG,CAAA;IACZ,OAAO,EAAE,GAAG,CAAA;IACZ,WAAW,EAAE,kBAAkB,CAAA;IAC/B,UAAU,EAAE,MAAM,EAAE,CAAA;CACrB;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,OAAO,GAAG,SAAS,GAAG,aAAa,GAAG,MAAM,CAAA;IAClD,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAA;IACjC,UAAU,EAAE,OAAO,CAAA;IACnB,eAAe,EAAE,MAAM,EAAE,CAAA;IACzB,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,eAAe,EAAE,CAAA;IAC9B,cAAc,EAAE,MAAM,CAAA;IACtB,cAAc,EAAE,cAAc,CAAA;IAC9B,YAAY,EAAE,YAAY,CAAA;IAC1B,cAAc,EAAE,cAAc,CAAA;CAC/B;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,WAAW,GAAG,UAAU,GAAG,eAAe,GAAG,YAAY,CAAA;IAC/D,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,gBAAgB,CAAA;IACxB,SAAS,EAAE,MAAM,CAAA;IACjB,eAAe,EAAE,MAAM,CAAA;CACxB;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,GAAG,CAAA;IACT,EAAE,EAAE,GAAG,CAAA;IACP,UAAU,EAAE,WAAW,GAAG,WAAW,GAAG,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAA;IACpE,SAAS,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,cAAc;IAC7B,WAAW,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;IACtC,KAAK,EAAE,cAAc,EAAE,CAAA;IACvB,WAAW,EAAE,cAAc,EAAE,CAAA;IAC7B,aAAa,EAAE,MAAM,EAAE,CAAA;CACxB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,MAAM,CAAA;IACd,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,MAAM,EAAE,CAAA;CACrB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,CAAA;IAChB,aAAa,EAAE,MAAM,CAAA;IACrB,IAAI,EAAE,MAAM,CAAA;CACb;AAED,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,eAAe,EAAE,CAAA;IAC3B,KAAK,EAAE,YAAY,EAAE,CAAA;IACrB,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,OAAO,CAAA;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,CAAA;IAClB,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,GAAG,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE,MAAM,EAAE,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,kBAAkB,EAAE,CAAA;IAC5B,OAAO,EAAE,mBAAmB,EAAE,CAAA;CAC/B;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,EAAE,CAAA;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,MAAM,CAAA;IACZ,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,OAAO,EAAE,MAAM,EAAE,CAAA;CAClB;AAED;;GAEG;AACH,qBAAa,sBAAuB,SAAQ,YAAY;IACtD,OAAO,CAAC,MAAM,CAAuB;IACrC,OAAO,CAAC,gBAAgB,CAA4C;IACpE,OAAO,CAAC,gBAAgB,CAA4C;IACpE,OAAO,CAAC,2BAA2B,CAAuD;IAC1F,OAAO,CAAC,aAAa,CAAkD;IACvE,OAAO,CAAC,gBAAgB,CAAwB;IAChD,OAAO,CAAC,UAAU,CAAiB;gBAEvB,MAAM,GAAE,OAAO,CAAC,qBAAqB,CAAM;IAkBvD;;OAEG;IACH,eAAe,CAAC,MAAM,EAAE,eAAe,GAAG,IAAI;IAqB9C;;OAEG;IACG,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;IAuDlE;;OAEG;IACG,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC;IA4CvG;;OAEG;IACH,mBAAmB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,eAAe,EAAE;IAUxD;;OAEG;IACH,8BAA8B,CAAC,OAAO,EAAE,MAAM,GAAG,0BAA0B,EAAE;IAI7E;;OAEG;IACH,wBAAwB,CAAC,SAAS,EAAE,MAAM,GAAG,qBAAqB,EAAE;IAIpE;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAQ5B;;OAEG;YACW,uBAAuB;IAerC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAS1B;;OAEG;YACW,0BAA0B;IA8CxC;;OAEG;YACW,sBAAsB;IAgCpC;;OAEG;YACW,+BAA+B;IAoC7C;;OAEG;YACW,wBAAwB;IActC;;OAEG;YACW,wBAAwB;IAgDtC;;OAEG;YACW,mCAAmC;IAuBjD;;OAEG;YACW,cAAc;IAqB5B;;OAEG;IACH,OAAO,CAAC,YAAY;IAYpB,OAAO,CAAC,8BAA8B;IAkBtC,OAAO,CAAC,iCAAiC;IAazC,OAAO,CAAC,wBAAwB;IAKhC,OAAO,CAAC,0BAA0B;YAKpB,qBAAqB;YASrB,kBAAkB;YASlB,oBAAoB;IAUlC,OAAO,CAAC,kBAAkB;IAO1B;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAKhC;AAED,eAAe,sBAAsB,CAAA"}