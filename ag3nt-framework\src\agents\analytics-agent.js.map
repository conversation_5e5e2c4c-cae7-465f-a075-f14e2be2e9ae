{"version": 3, "file": "analytics-agent.js", "sourceRoot": "", "sources": ["analytics-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AA6xB5C;;GAEG;AACH,MAAa,cAAe,SAAQ,sBAAS;IAO3C,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,WAAW,EAAE;YACjB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,oBAAoB;oBACpB,mBAAmB;oBACnB,mBAAmB;oBACnB,sBAAsB;oBACtB,kBAAkB;oBAClB,oBAAoB;oBACpB,mBAAmB;iBACpB;gBACD,cAAc,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,CAAC;gBAC/E,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QAzBa,mBAAc,GAAG;YAChC,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB;YACvD,mBAAmB,EAAE,oBAAoB,EAAE,cAAc;YACzD,kBAAkB,EAAE,mBAAmB,EAAE,oBAAoB;SAC9D,CAAA;IAsBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAuB,CAAA;QAE3C,OAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAElE,uCAAuC;QACvC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACpE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,2CAA2C;QAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,iBAAiB;gBACpB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAC/D,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAA;YACvD,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;YAC3D,KAAK,cAAc;gBACjB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;YACrD,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;YAC3D;gBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAA;IACnC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS,EAAE,wCAAwC;YACnD,UAAU,EAAE,qCAAqC;YACjD,gBAAgB,EAAE,6CAA6C;YAC/D,mBAAmB,EAAE,sCAAsC;YAC3D,QAAQ,EAAE,4CAA4C;YACtD,UAAU,EAAE,mDAAmD;SAChE,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,qBAAqB,CAAC,KAAU,EAAE,KAAqB;QACnE,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACrD,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,OAAO,CAAC,UAAU,EACxB,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAA;QAElD,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,KAAU;QAC3C,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAE/D,MAAM,cAAc,GAAG,MAAM,sBAAS,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAA;QAE7E,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,cAAc,GAAG,cAAc,CAAA;QAEnD,OAAO;YACL,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,cAAc,CAAA;QAEzD,MAAM,SAAS,GAAG,MAAM,sBAAS,CAAC,qBAAqB,CACrD,cAAc,EACd,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CACxC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,CAAA;QAE/C,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,yBAAyB,CACxD,SAAS,EACT,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,cAAc,CACnC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAU;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,WAAW,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CACzD,QAAQ,EACR,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CACxC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAA;QAE7C,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAU;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAA;QAEnD,MAAM,MAAM,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACjD,WAAW,EACX,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CACxC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAA;QAEnC,OAAO;YACL,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,cAAc;YAC1C,SAAS,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS;YACxC,QAAQ,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ;YACtC,WAAW,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW;SAC7C,CAAA;QAED,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CACtD,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CACzC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,CAAA;QAE3C,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;QAErE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAU;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,CAAA;QAEjD,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,sBAAsB,CACvD,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AApRD,wCAoRC;AAG0B,iCAAO"}