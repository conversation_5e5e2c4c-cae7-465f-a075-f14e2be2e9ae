import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { 
  getAIModel, 
  validateEnvironment, 
  availableModels, 
  getRecommendedModels,
  calculateCost,
  getModelMetadata,
  checkProviderHealth
} from '../ai-config'
import { usageTracker, trackAPIUsage } from '../usage-tracker'

// Mock environment variables
const mockEnv = {
  OPENROUTER_API_KEY: 'test-openrouter-key',
  OPENAI_API_KEY: 'test-openai-key',
  ANTHROPIC_API_KEY: 'test-anthropic-key',
  NEXT_PUBLIC_APP_URL: 'http://localhost:3000'
}

describe('OpenRouter Integration', () => {
  beforeEach(() => {
    // Reset environment variables
    Object.keys(mockEnv).forEach(key => {
      process.env[key] = mockEnv[key as keyof typeof mockEnv]
    })
  })

  describe('AI Configuration', () => {
    it('should validate environment correctly', () => {
      expect(validateEnvironment()).toBe(true)
      
      delete process.env.OPENROUTER_API_KEY
      expect(validateEnvironment()).toBe(true) // Should still pass with other providers
      
      // Remove all API keys
      Object.keys(mockEnv).forEach(key => {
        if (key.includes('API_KEY')) {
          delete process.env[key]
        }
      })
      expect(validateEnvironment()).toBe(false)
    })

    it('should get OpenRouter models correctly', () => {
      const model = getAIModel('openrouter', 'openai-gpt-4o-mini')
      expect(model).toBeDefined()
    })

    it('should fallback to default provider when invalid provider specified', () => {
      const model = getAIModel('invalid-provider', 'invalid-model')
      expect(model).toBeDefined() // Should fallback to default
    })

    it('should return available models with OpenRouter priority', () => {
      expect(availableModels.length).toBeGreaterThan(0)
      
      // OpenRouter models should be prioritized
      const openRouterModels = availableModels.filter(m => m.provider === 'openrouter')
      expect(openRouterModels.length).toBeGreaterThan(0)
      
      // Check that recommended models are marked
      const recommendedModels = availableModels.filter(m => m.isRecommended)
      expect(recommendedModels.length).toBeGreaterThan(0)
    })

    it('should categorize models correctly', () => {
      const categories = ['coding', 'multimodal', 'efficient', 'long-context', 'general']
      const modelCategories = [...new Set(availableModels.map(m => m.category))]
      
      modelCategories.forEach(category => {
        expect(categories).toContain(category)
      })
    })

    it('should get model recommendations', () => {
      const recommendations = getRecommendedModels()
      
      expect(recommendations.coding).toBeDefined()
      expect(recommendations.general).toBeDefined()
      expect(recommendations.efficient).toBeDefined()
      expect(recommendations.multimodal).toBeDefined()
      expect(recommendations.longContext).toBeDefined()
      
      expect(Array.isArray(recommendations.coding)).toBe(true)
    })
  })

  describe('Model Metadata and Pricing', () => {
    it('should get model metadata for OpenRouter models', () => {
      const metadata = getModelMetadata('openai/gpt-4o-mini')
      expect(metadata).toBeDefined()
      expect(metadata?.pricing).toBeDefined()
      expect(metadata?.context_length).toBeGreaterThan(0)
    })

    it('should calculate costs correctly', () => {
      const cost = calculateCost('openai/gpt-4o-mini', 1000, 500)
      expect(cost).toBeGreaterThan(0)
      expect(typeof cost).toBe('number')
    })

    it('should return 0 cost for unknown models', () => {
      const cost = calculateCost('unknown/model', 1000, 500)
      expect(cost).toBe(0)
    })
  })

  describe('Usage Tracking', () => {
    beforeEach(() => {
      // Clear usage data before each test
      usageTracker.clearOldData(0)
    })

    it('should track API usage correctly', () => {
      trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 100, 50, 1000, true)
      
      const stats = usageTracker.getUsageStats(1)
      expect(stats.totalRequests).toBe(1)
      expect(stats.totalTokens).toBe(150)
      expect(stats.successRate).toBe(1)
    })

    it('should track failed requests', () => {
      trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 100, 0, 2000, false)
      
      const stats = usageTracker.getUsageStats(1)
      expect(stats.totalRequests).toBe(1)
      expect(stats.successRate).toBe(0)
    })

    it('should calculate budget status', () => {
      // Track some usage
      trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 1000, 500, 1000, true)
      
      const budgetStatus = usageTracker.getBudgetStatus()
      expect(budgetStatus.monthlyBudget).toBeGreaterThan(0)
      expect(budgetStatus.currentMonthSpend).toBeGreaterThanOrEqual(0)
      expect(typeof budgetStatus.percentUsed).toBe('number')
    })

    it('should provide model recommendations based on usage', () => {
      // Track usage for multiple models
      trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 100, 50, 800, true)
      trackAPIUsage('openrouter', 'anthropic-claude-3.5-haiku', 100, 50, 1200, true)
      trackAPIUsage('openrouter', 'meta-llama-llama-3.1-8b-instruct', 100, 50, 600, true)
      
      const recommendations = usageTracker.getModelRecommendations()
      expect(recommendations.fastest).toBeDefined()
      expect(recommendations.mostReliable).toBeDefined()
      expect(recommendations.mostEfficient).toBeDefined()
      expect(recommendations.bestValue).toBeDefined()
    })

    it('should export usage data correctly', () => {
      trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 100, 50, 1000, true)
      
      const exportData = usageTracker.exportUsageData()
      expect(exportData.summary).toBeDefined()
      expect(exportData.metrics).toBeDefined()
      expect(exportData.budget).toBeDefined()
      expect(exportData.recommendations).toBeDefined()
    })
  })

  describe('Provider Health Checks', () => {
    it('should check OpenRouter health', async () => {
      // Mock fetch for health check
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: [] })
      }) as jest.MockedFunction<typeof fetch>

      const isHealthy = await checkProviderHealth('openrouter')
      expect(typeof isHealthy).toBe('boolean')
    })

    it('should handle health check failures gracefully', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'))

      const isHealthy = await checkProviderHealth('openrouter')
      expect(isHealthy).toBe(false)
    })
  })

  describe('Model Selection Logic', () => {
    it('should prioritize OpenRouter models in selection', () => {
      const openRouterModels = availableModels.filter(m => m.provider === 'openrouter')
      const firstModel = availableModels[0]
      
      // First model should be OpenRouter or recommended
      expect(
        firstModel.provider === 'openrouter' || firstModel.isRecommended
      ).toBe(true)
    })

    it('should include pricing information for OpenRouter models', () => {
      const openRouterModels = availableModels.filter(m => m.provider === 'openrouter')
      
      openRouterModels.forEach(model => {
        if (model.pricing) {
          expect(model.pricing.prompt).toBeGreaterThan(0)
          expect(model.pricing.completion).toBeGreaterThan(0)
        }
      })
    })

    it('should categorize coding models correctly', () => {
      const codingModels = availableModels.filter(m => m.category === 'coding')
      
      // Should include known coding models
      const codingModelNames = codingModels.map(m => m.model)
      expect(codingModelNames.some(name => 
        name.includes('coder') || name.includes('code')
      )).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle missing API keys gracefully', () => {
      delete process.env.OPENROUTER_API_KEY
      delete process.env.OPENAI_API_KEY
      delete process.env.ANTHROPIC_API_KEY
      
      expect(() => getAIModel('openrouter', 'openai-gpt-4o-mini')).not.toThrow()
    })

    it('should handle invalid model requests', () => {
      expect(() => getAIModel('openrouter', 'nonexistent-model')).not.toThrow()
    })
  })
})

describe('Integration with UI Components', () => {
  it('should provide data in format expected by ModelSelector', () => {
    const models = availableModels
    
    models.forEach(model => {
      expect(model).toHaveProperty('provider')
      expect(model).toHaveProperty('model')
      expect(model).toHaveProperty('displayName')
      expect(model).toHaveProperty('value')
      expect(model).toHaveProperty('category')
      expect(model).toHaveProperty('isRecommended')
    })
  })

  it('should provide data in format expected by UsageAnalytics', () => {
    trackAPIUsage('openrouter', 'openai-gpt-4o-mini', 100, 50, 1000, true)
    
    const stats = usageTracker.getUsageStats()
    expect(stats).toHaveProperty('totalCost')
    expect(stats).toHaveProperty('totalRequests')
    expect(stats).toHaveProperty('totalTokens')
    expect(stats).toHaveProperty('averageLatency')
    expect(stats).toHaveProperty('successRate')
    expect(stats).toHaveProperty('topModels')
    expect(stats).toHaveProperty('dailyBreakdown')
  })
})