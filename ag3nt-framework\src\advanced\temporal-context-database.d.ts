/**
 * AG3NT Framework - Temporal Context Database
 *
 * Advanced temporal graph database system that maintains full execution history,
 * enabling agents to learn from past decisions and providing complete audit trails.
 *
 * Features:
 * - Temporal graph storage with versioning
 * - Full execution history tracking
 * - Context evolution analysis
 * - Time-travel queries
 * - Relationship tracking over time
 * - Performance analytics
 */
import { EventEmitter } from "events";
export interface TemporalDatabaseConfig {
    maxHistorySize: number;
    compressionEnabled: boolean;
    indexingStrategy: 'time' | 'entity' | 'hybrid';
    retentionPolicy: RetentionPolicy;
    snapshotInterval: number;
    queryOptimization: boolean;
}
export interface RetentionPolicy {
    shortTerm: number;
    mediumTerm: number;
    longTerm: number;
    compressionRatio: number;
}
export interface TemporalNode {
    id: string;
    type: string;
    properties: Record<string, any>;
    timestamp: number;
    version: number;
    metadata: NodeMetadata;
    relationships: TemporalRelationship[];
}
export interface NodeMetadata {
    createdBy: string;
    createdAt: number;
    updatedBy: string;
    updatedAt: number;
    tags: string[];
    importance: number;
    accessCount: number;
}
export interface TemporalRelationship {
    id: string;
    type: string;
    fromNode: string;
    toNode: string;
    properties: Record<string, any>;
    validFrom: number;
    validTo?: number;
    strength: number;
    metadata: RelationshipMetadata;
}
export interface RelationshipMetadata {
    createdBy: string;
    createdAt: number;
    confidence: number;
    source: string;
    verified: boolean;
}
export interface TemporalSnapshot {
    id: string;
    timestamp: number;
    nodes: TemporalNode[];
    relationships: TemporalRelationship[];
    metadata: SnapshotMetadata;
    statistics: SnapshotStatistics;
}
export interface SnapshotMetadata {
    version: string;
    description: string;
    trigger: 'scheduled' | 'manual' | 'event';
    size: number;
    compression: number;
}
export interface SnapshotStatistics {
    nodeCount: number;
    relationshipCount: number;
    avgNodeDegree: number;
    graphDensity: number;
    componentCount: number;
}
export interface TemporalQuery {
    queryId: string;
    type: 'point_in_time' | 'time_range' | 'evolution' | 'pattern';
    timeConstraints: TimeConstraints;
    nodeFilters: NodeFilter[];
    relationshipFilters: RelationshipFilter[];
    aggregations: Aggregation[];
    orderBy: OrderBy[];
    limit?: number;
}
export interface TimeConstraints {
    startTime?: number;
    endTime?: number;
    pointInTime?: number;
    timeWindow?: number;
    granularity: 'millisecond' | 'second' | 'minute' | 'hour' | 'day';
}
export interface NodeFilter {
    field: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'range';
    value: any;
    caseSensitive?: boolean;
}
export interface RelationshipFilter {
    type?: string;
    strength?: {
        min: number;
        max: number;
    };
    timeRange?: {
        start: number;
        end: number;
    };
    properties?: Record<string, any>;
}
export interface Aggregation {
    function: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'distinct';
    field: string;
    groupBy?: string[];
    alias?: string;
}
export interface OrderBy {
    field: string;
    direction: 'asc' | 'desc';
    nullsFirst?: boolean;
}
export interface QueryResult {
    queryId: string;
    executionTime: number;
    resultCount: number;
    nodes: TemporalNode[];
    relationships: TemporalRelationship[];
    aggregations: Record<string, any>;
    metadata: QueryMetadata;
}
export interface QueryMetadata {
    cacheHit: boolean;
    indexesUsed: string[];
    optimizations: string[];
    warnings: string[];
}
export interface EvolutionAnalysis {
    entityId: string;
    timeRange: {
        start: number;
        end: number;
    };
    changes: EntityChange[];
    patterns: EvolutionPattern[];
    statistics: EvolutionStatistics;
}
export interface EntityChange {
    timestamp: number;
    changeType: 'created' | 'updated' | 'deleted' | 'relationship_added' | 'relationship_removed';
    field?: string;
    oldValue?: any;
    newValue?: any;
    changeBy: string;
    reason?: string;
}
export interface EvolutionPattern {
    type: 'periodic' | 'trend' | 'anomaly' | 'correlation';
    description: string;
    confidence: number;
    frequency?: number;
    correlation?: string;
}
export interface EvolutionStatistics {
    totalChanges: number;
    changeFrequency: number;
    stabilityScore: number;
    volatilityScore: number;
    growthRate: number;
}
export interface ContextSnapshot {
    timestamp: number;
    agentId: string;
    executionId: string;
    context: ExecutionContext;
    performance: PerformanceSnapshot;
    relationships: ContextRelationship[];
}
export interface ExecutionContext {
    task: any;
    environment: any;
    dependencies: any;
    constraints: any;
    userPreferences: any;
}
export interface PerformanceSnapshot {
    accuracy: number;
    efficiency: number;
    resourceUsage: any;
    userSatisfaction: number;
    duration: number;
}
export interface ContextRelationship {
    type: string;
    target: string;
    strength: number;
    metadata: any;
}
export interface AnalyticsQuery {
    type: 'performance_trend' | 'usage_pattern' | 'relationship_analysis' | 'anomaly_detection';
    timeRange: {
        start: number;
        end: number;
    };
    entities: string[];
    metrics: string[];
    aggregation: string;
}
export interface AnalyticsResult {
    queryType: string;
    timeRange: {
        start: number;
        end: number;
    };
    data: AnalyticsDataPoint[];
    insights: AnalyticsInsight[];
    recommendations: string[];
}
export interface AnalyticsDataPoint {
    timestamp: number;
    entity: string;
    metrics: Record<string, number>;
    metadata: any;
}
export interface AnalyticsInsight {
    type: string;
    description: string;
    confidence: number;
    impact: 'high' | 'medium' | 'low';
    evidence: any[];
}
/**
 * Temporal Context Database - Advanced context persistence with time-travel capabilities
 */
export declare class TemporalContextDatabase extends EventEmitter {
    private config;
    private nodes;
    private relationships;
    private snapshots;
    private indexes;
    private queryCache;
    private isInitialized;
    constructor(config?: Partial<TemporalDatabaseConfig>);
    /**
     * Initialize the temporal database
     */
    private initialize;
    /**
     * Store context snapshot
     */
    storeContextSnapshot(snapshot: ContextSnapshot): Promise<void>;
    /**
     * Store temporal node
     */
    storeNode(node: TemporalNode): Promise<void>;
    /**
     * Store temporal relationship
     */
    storeRelationship(relationship: TemporalRelationship): Promise<void>;
    /**
     * Query temporal data
     */
    query(query: TemporalQuery): Promise<QueryResult>;
    /**
     * Analyze entity evolution over time
     */
    analyzeEvolution(entityId: string, timeRange: {
        start: number;
        end: number;
    }): Promise<EvolutionAnalysis>;
    /**
     * Perform analytics queries
     */
    analytics(query: AnalyticsQuery): Promise<AnalyticsResult>;
    /**
     * Get latest version of node
     */
    getLatestNode(nodeId: string): Promise<TemporalNode | null>;
    /**
     * Get node at specific time
     */
    getNodeAtTime(nodeId: string, timestamp: number): Promise<TemporalNode | null>;
    /**
     * Create database snapshot
     */
    createSnapshot(description?: string): Promise<TemporalSnapshot>;
    /**
     * Private helper methods
     */
    private createIndexes;
    private updateIndexes;
    private calculateImportance;
    private startSnapshotScheduler;
    private startRetentionEnforcement;
    private enforceRetentionPolicy;
    private generateCacheKey;
    private queryPointInTime;
    private queryTimeRange;
    private queryEvolution;
    private queryPattern;
    private applyNodeFilters;
    private applyRelationshipFilters;
    private applyOrdering;
    private calculateAggregations;
    private getNestedProperty;
    private evaluateFilter;
    private detectEvolutionPatterns;
    private calculateEvolutionStatistics;
    private calculateComponentCount;
    private analyzePerformanceTrend;
    private analyzeUsagePattern;
    private analyzeRelationships;
    private detectAnomalies;
    /**
     * Shutdown database
     */
    shutdown(): Promise<void>;
}
export default TemporalContextDatabase;
//# sourceMappingURL=temporal-context-database.d.ts.map