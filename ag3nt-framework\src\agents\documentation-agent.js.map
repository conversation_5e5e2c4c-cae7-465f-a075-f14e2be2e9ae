{"version": 3, "file": "documentation-agent.js", "sourceRoot": "", "sources": ["documentation-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AAue5C;;GAEG;AACH,MAAa,kBAAmB,SAAQ,sBAAS;IAO/C,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,eAAe,EAAE;YACrB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,0BAA0B;oBAC1B,mBAAmB;oBACnB,4BAA4B;oBAC5B,sBAAsB;oBACtB,iBAAiB;oBACjB,2BAA2B;oBAC3B,oBAAoB;iBACrB;gBACD,cAAc,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,CAAC;gBAC1E,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QAzBa,uBAAkB,GAAG;YACpC,kBAAkB,EAAE,sBAAsB,EAAE,oBAAoB;YAChE,mBAAmB,EAAE,0BAA0B,EAAE,mBAAmB;YACpE,oBAAoB,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,cAAc;SACjF,CAAA;IAsBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAA2B,CAAA;QAE/C,OAAO,CAAC,GAAG,CAAC,uCAAuC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAEtE,2CAA2C;QAC3C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,uCAAuC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACxE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,2CAA2C;QAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAChE,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;YAC3D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAA;YAChE,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAA;YAC3D,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,cAAc;gBACjB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;YACrD;gBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAA;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,aAAa,EAAE,sDAAsD;YACrE,gBAAgB,EAAE,8CAA8C;YAChE,yBAAyB,EAAE,8CAA8C;YACzE,cAAc,EAAE,mDAAmD;YACnE,gBAAgB,EAAE,8CAA8C;SACjE,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,sBAAsB,CAAC,KAAU,EAAE,KAAyB;QACxE,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,+BAA+B,CAC9D,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,QAAQ,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAAA;QAE3C,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAA;QAExE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAA;QAEnD,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAU;QAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,CAAA;QAC7D,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,kBAAkB,CAAA;QAEjE,MAAM,IAAI,GAAG,MAAM,sBAAS,CAAC,yBAAyB,CACpD,gBAAgB,EAChB,kBAAkB,EAClB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAC/B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAA;QAE5C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAElD,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CACtD,IAAI,EACJ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAChC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,6BAA6B,CAAC,KAAU;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAElD,MAAM,gBAAgB,GAAG,MAAM,sBAAS,CAAC,+BAA+B,CACtE,IAAI,EACJ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CACxC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QAEvD,OAAO;YACL,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAElD,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,eAAe,CAChD,IAAI,EACJ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CACtC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAU;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAA;QAElD,MAAM,SAAS,GAAG,MAAM,sBAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;QAE5D,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAA;QAEzC,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO;YAChC,YAAY,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB;YAClD,MAAM,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU;YACtC,SAAS,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,SAAS;SACzC,CAAA;QAED,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,2BAA2B,CACzD,OAAO,EACP,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,QAAQ,CAC3B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAA;QAEtC,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,4BAA4B,CAC1D,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CACvC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAU;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAA;QAEtC,MAAM,WAAW,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACtD,UAAU,EACV,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CACtC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAA;QAE7C,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AAvSD,gDAuSC;AAG8B,qCAAO"}