import { generateText, streamText } from 'ai'
import { getAIModel } from './ai-config'
import { agentTools } from './agent-tools'

export interface AgentTask {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  steps: AgentStep[]
  result?: any
  error?: string
  createdAt: string
  updatedAt: string
}

export interface AgentStep {
  id: string
  description: string
  tool?: string
  parameters?: any
  result?: any
  status: 'pending' | 'running' | 'completed' | 'failed'
  timestamp: string
}

export interface AgentMemory {
  conversations: Array<{
    id: string
    messages: Array<{ role: string; content: string }>
    timestamp: string
  }>
  facts: Array<{
    id: string
    content: string
    source: string
    timestamp: string
  }>
  preferences: Record<string, any>
}

export class AIAgent {
  private memory: AgentMemory
  private model: any
  private systemPrompt: string

  constructor(
    provider = 'openai',
    model = 'gpt-4o',
    systemPrompt = `You are an AI development assistant agent. You can:
    - Read and write files
    - Execute commands
    - Install packages
    - Fetch web content
    - Manage tasks
    - Remember information across conversations
    
    Always break down complex tasks into smaller steps and execute them systematically.
    Use the available tools to accomplish tasks efficiently.
    Provide clear explanations of what you're doing and why.`
  ) {
    this.model = getAIModel(provider, model)
    this.systemPrompt = systemPrompt
    this.memory = {
      conversations: [],
      facts: [],
      preferences: {}
    }
  }

  async executeTask(taskDescription: string): Promise<AgentTask> {
    const task: AgentTask = {
      id: `task-${Date.now()}`,
      title: taskDescription,
      description: taskDescription,
      status: 'running',
      steps: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    try {
      // First, plan the task
      const planningPrompt = `
        Task: ${taskDescription}
        
        Break this task down into specific, actionable steps.
        Consider what tools you'll need to use and in what order.
        Respond with a JSON array of steps, each with:
        - description: What this step does
        - tool: Which tool to use (if any)
        - reasoning: Why this step is necessary
      `

      const planResult = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: planningPrompt,
      })

      // Parse the plan (in a real implementation, you'd use structured output)
      console.log('Agent Plan:', planResult.text)

      // Execute the task with tools
      const executionResult = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: `Execute this task: ${taskDescription}`,
        tools: agentTools,
        maxToolRoundtrips: 5,
      })

      task.result = executionResult.text
      task.status = 'completed'
      task.updatedAt = new Date().toISOString()

      // Store in memory
      this.addToMemory('task_completion', {
        task: taskDescription,
        result: executionResult.text,
        timestamp: new Date().toISOString()
      })

      return task

    } catch (error) {
      task.status = 'failed'
      task.error = error.message
      task.updatedAt = new Date().toISOString()
      return task
    }
  }

  async chat(message: string, conversationId?: string): Promise<string> {
    try {
      // Add context from memory
      const memoryContext = this.getRelevantMemory(message)
      const contextualPrompt = `
        ${memoryContext ? `Relevant context from memory: ${memoryContext}` : ''}
        
        User message: ${message}
      `

      const result = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: contextualPrompt,
        tools: agentTools,
        maxToolRoundtrips: 3,
      })

      // Store conversation in memory
      this.addConversation(conversationId || `conv-${Date.now()}`, [
        { role: 'user', content: message },
        { role: 'assistant', content: result.text }
      ])

      return result.text

    } catch (error) {
      return `I encountered an error: ${error.message}`
    }
  }

  async streamChat(message: string, conversationId?: string) {
    const memoryContext = this.getRelevantMemory(message)
    const contextualPrompt = `
      ${memoryContext ? `Relevant context from memory: ${memoryContext}` : ''}
      
      User message: ${message}
    `

    return streamText({
      model: this.model,
      system: this.systemPrompt,
      prompt: contextualPrompt,
      tools: agentTools,
      maxToolRoundtrips: 3,
    })
  }

  private addToMemory(type: string, data: any) {
    this.memory.facts.push({
      id: `fact-${Date.now()}`,
      content: JSON.stringify({ type, data }),
      source: 'agent',
      timestamp: new Date().toISOString()
    })
  }

  private addConversation(id: string, messages: Array<{ role: string; content: string }>) {
    this.memory.conversations.push({
      id,
      messages,
      timestamp: new Date().toISOString()
    })
  }

  private getRelevantMemory(query: string): string | null {
    // Simple keyword matching - in a real implementation, use vector search
    const relevantFacts = this.memory.facts.filter(fact => 
      fact.content.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 3)

    if (relevantFacts.length === 0) return null

    return relevantFacts.map(fact => fact.content).join('\n')
  }

  getMemory(): AgentMemory {
    return this.memory
  }

  setPreference(key: string, value: any) {
    this.memory.preferences[key] = value
  }
}
