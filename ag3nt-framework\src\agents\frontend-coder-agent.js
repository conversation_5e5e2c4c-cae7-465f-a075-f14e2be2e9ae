"use strict";
/**
 * AG3NT Framework - Frontend Coder Agent
 *
 * Specialized agent for frontend development tasks.
 * Handles UI/UX implementation, component development, and frontend architecture.
 *
 * Features:
 * - React/Vue/Angular component development
 * - UI/UX implementation from designs
 * - Frontend architecture and state management
 * - Responsive design and accessibility
 * - Performance optimization
 * - Testing and quality assurance
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.FrontendCoderAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Frontend Coder Agent - Specialized frontend development
 */
class FrontendCoderAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('frontend-coder', {
            capabilities: {
                requiredCapabilities: [
                    'frontend_development',
                    'ui_implementation',
                    'component_architecture',
                    'responsive_design',
                    'performance_optimization',
                    'accessibility_compliance',
                    'frontend_testing'
                ],
                contextFilters: ['frontend', 'ui', 'components', 'design', 'code'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.codingSteps = [
            'analyze_requirements', 'plan_implementation', 'setup_environment',
            'develop_components', 'implement_styling', 'add_interactions',
            'optimize_performance', 'ensure_accessibility', 'write_tests', 'document_code'
        ];
    }
    /**
     * Execute frontend coding workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`💻 Starting frontend development: ${input.task.title}`);
        // Execute coding steps sequentially
        for (const stepId of this.codingSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            if (stepResult.needsReview) {
                state.results.status = 'needs_review';
                state.results.reviewReason = stepResult.reviewReason;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed and no review required
        if (!state.needsInput && state.results.status !== 'needs_review') {
            state.completed = true;
            console.log(`✅ Frontend development completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual coding step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_requirements':
                return await this.analyzeRequirementsWithMCP(enhancedState, input);
            case 'plan_implementation':
                return await this.planImplementationWithMCP(enhancedState);
            case 'setup_environment':
                return await this.setupEnvironmentWithMCP(enhancedState);
            case 'develop_components':
                return await this.developComponentsWithMCP(enhancedState);
            case 'implement_styling':
                return await this.implementStylingWithMCP(enhancedState);
            case 'add_interactions':
                return await this.addInteractionsWithMCP(enhancedState);
            case 'optimize_performance':
                return await this.optimizePerformanceWithMCP(enhancedState);
            case 'ensure_accessibility':
                return await this.ensureAccessibilityWithMCP(enhancedState);
            case 'write_tests':
                return await this.writeTestsWithMCP(enhancedState);
            case 'document_code':
                return await this.documentCodeWithMCP(enhancedState);
            default:
                throw new Error(`Unknown frontend coding step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.codingSteps.length;
    }
    /**
     * Get relevant documentation for frontend development
     */
    async getRelevantDocumentation() {
        return {
            frontendDevelopment: 'Modern frontend development practices and patterns',
            componentArchitecture: 'Component-based architecture and design patterns',
            responsiveDesign: 'Responsive web design and mobile-first development',
            accessibility: 'Web accessibility guidelines and WCAG compliance',
            performance: 'Frontend performance optimization techniques',
            testing: 'Frontend testing strategies and best practices'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeRequirementsWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeFrontendRequirements(input.task, input.design, input.requirements, input.codebase);
        this.state.results.requirementsAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async planImplementationWithMCP(state) {
        const requirementsAnalysis = this.state.results.requirementsAnalysis;
        const implementationPlan = await ai_service_1.aiService.planFrontendImplementation(requirementsAnalysis, this.state.input.codebase);
        this.state.results.implementationPlan = implementationPlan;
        return {
            results: implementationPlan,
            needsInput: false,
            completed: false
        };
    }
    async setupEnvironmentWithMCP(state) {
        const implementationPlan = this.state.results.implementationPlan;
        const environmentSetup = await ai_service_1.aiService.setupFrontendEnvironment(implementationPlan, this.state.input.requirements);
        this.state.results.environmentSetup = environmentSetup;
        return {
            results: environmentSetup,
            needsInput: false,
            completed: false
        };
    }
    async developComponentsWithMCP(state) {
        const implementationPlan = this.state.results.implementationPlan;
        const components = await ai_service_1.aiService.developFrontendComponents(implementationPlan, this.state.input.design, this.state.input.requirements);
        this.state.results.components = components;
        return {
            results: components,
            needsInput: false,
            completed: false
        };
    }
    async implementStylingWithMCP(state) {
        const components = this.state.results.components;
        const styling = await ai_service_1.aiService.implementFrontendStyling(components, this.state.input.design.designSystem, this.state.input.requirements.styling);
        this.state.results.styling = styling;
        return {
            results: styling,
            needsInput: false,
            completed: false
        };
    }
    async addInteractionsWithMCP(state) {
        const components = this.state.results.components;
        const interactions = await ai_service_1.aiService.addFrontendInteractions(components, this.state.input.design.userFlows, this.state.input.requirements.stateManagement);
        this.state.results.interactions = interactions;
        return {
            results: interactions,
            needsInput: false,
            completed: false
        };
    }
    async optimizePerformanceWithMCP(state) {
        const components = this.state.results.components;
        const optimization = await ai_service_1.aiService.optimizeFrontendPerformance(components, this.state.input.requirements.performance);
        this.state.results.optimization = optimization;
        return {
            results: optimization,
            needsInput: false,
            completed: false
        };
    }
    async ensureAccessibilityWithMCP(state) {
        const components = this.state.results.components;
        const accessibility = await ai_service_1.aiService.ensureFrontendAccessibility(components, this.state.input.requirements.accessibility);
        this.state.results.accessibility = accessibility;
        return {
            results: accessibility,
            needsInput: false,
            completed: false
        };
    }
    async writeTestsWithMCP(state) {
        const components = this.state.results.components;
        const tests = await ai_service_1.aiService.writeFrontendTests(components, this.state.input.requirements.testing);
        this.state.results.tests = tests;
        return {
            results: tests,
            needsInput: false,
            completed: false
        };
    }
    async documentCodeWithMCP(state) {
        const components = this.state.results.components;
        const documentation = await ai_service_1.aiService.documentFrontendCode(components, this.state.input.task);
        this.state.results.documentation = documentation;
        return {
            results: documentation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.FrontendCoderAgent = FrontendCoderAgent;
exports.default = FrontendCoderAgent;
//# sourceMappingURL=frontend-coder-agent.js.map