import { useState, useCallback } from 'react'

interface UserStory {
  id: string
  title: string
  asA: string
  iWant: string
  soThat: string
  acceptanceCriteria: string[]
  priority: 'low' | 'medium' | 'high' | 'critical'
  storyPoints: number
  status: 'draft' | 'review' | 'approved' | 'rejected'
  feedback?: string
  version: number
}

interface UseRequirementsAgentReturn {
  isGenerating: boolean
  progress: number
  currentThought: string
  userStories: UserStory[]
  generateRequirements: (projectDescription: string) => Promise<void>
  regenerateStory: (storyId: string) => Promise<void>
  approveStory: (storyId: string) => void
  rejectStory: (storyId: string, feedback: string) => void
  editStory: (storyId: string, updates: Partial<UserStory>) => void
}

export function useRequirementsAgent(): UseRequirementsAgentReturn {
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentThought, setCurrentThought] = useState('')
  const [userStories, setUserStories] = useState<UserStory[]>([])

  const generateRequirements = useCallback(async (projectDescription: string) => {
    setIsGenerating(true)
    setProgress(0)
    setUserStories([])

    const thoughts = [
      "Analyzing project description and identifying key objectives...",
      "Identifying potential user personas and stakeholders...",
      "Breaking down functionality into user-centered features...",
      "Defining user goals and motivations for each feature...",
      "Creating user stories with clear acceptance criteria...",
      "Prioritizing stories based on business value and complexity...",
      "Validating story completeness and consistency...",
      "Estimating story points and effort requirements...",
      "Finalizing requirements documentation..."
    ]

    // Simulate AI thinking process
    for (let i = 0; i < thoughts.length; i++) {
      setCurrentThought(thoughts[i])
      setProgress(Math.round((i / thoughts.length) * 100))
      
      // Generate stories at certain progress points
      if (i === 4) {
        generateSampleStories(projectDescription, 'initial')
      } else if (i === 6) {
        generateSampleStories(projectDescription, 'additional')
      }
      
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000))
    }

    setProgress(100)
    setCurrentThought("Requirements generation completed!")
    
    setTimeout(() => {
      setIsGenerating(false)
      setCurrentThought('')
    }, 1000)
  }, [])

  const generateSampleStories = (projectDescription: string, phase: 'initial' | 'additional') => {
    const sampleStories: Omit<UserStory, 'id'>[] = phase === 'initial' ? [
      {
        title: "User Registration and Authentication",
        asA: "new user",
        iWant: "to create an account and log in securely",
        soThat: "I can access personalized features and save my preferences",
        acceptanceCriteria: [
          "User can register with email and password",
          "User receives email verification",
          "User can log in with valid credentials",
          "User sees appropriate error messages for invalid attempts",
          "User can reset password if forgotten"
        ],
        priority: 'high',
        storyPoints: 8,
        status: 'draft',
        version: 1
      },
      {
        title: "User Profile Management",
        asA: "registered user",
        iWant: "to manage my profile information",
        soThat: "I can keep my account details up to date",
        acceptanceCriteria: [
          "User can view current profile information",
          "User can edit profile details",
          "User can upload profile picture",
          "Changes are saved and reflected immediately",
          "User can delete account if desired"
        ],
        priority: 'medium',
        storyPoints: 5,
        status: 'draft',
        version: 1
      },
      {
        title: "Dashboard Overview",
        asA: "logged-in user",
        iWant: "to see an overview of my account and recent activity",
        soThat: "I can quickly understand my current status and take relevant actions",
        acceptanceCriteria: [
          "Dashboard shows key metrics and statistics",
          "Recent activity is displayed chronologically",
          "Quick action buttons are easily accessible",
          "Dashboard loads quickly and is responsive",
          "Information is updated in real-time"
        ],
        priority: 'high',
        storyPoints: 6,
        status: 'draft',
        version: 1
      }
    ] : [
      {
        title: "Search and Filter Functionality",
        asA: "user",
        iWant: "to search and filter content effectively",
        soThat: "I can quickly find what I'm looking for",
        acceptanceCriteria: [
          "Search returns relevant results",
          "Multiple filter options are available",
          "Search and filters work together",
          "Results are paginated for performance",
          "Search history is saved for convenience"
        ],
        priority: 'medium',
        storyPoints: 7,
        status: 'draft',
        version: 1
      },
      {
        title: "Notification System",
        asA: "user",
        iWant: "to receive relevant notifications",
        soThat: "I stay informed about important updates and activities",
        acceptanceCriteria: [
          "User receives notifications for important events",
          "Notifications can be customized by type",
          "User can mark notifications as read",
          "Notification preferences can be managed",
          "Email notifications are optional"
        ],
        priority: 'low',
        storyPoints: 4,
        status: 'draft',
        version: 1
      },
      {
        title: "Data Export and Backup",
        asA: "user",
        iWant: "to export my data",
        soThat: "I can backup my information or use it elsewhere",
        acceptanceCriteria: [
          "User can export data in multiple formats",
          "Export includes all user-generated content",
          "Export process is secure and private",
          "User receives confirmation when export is ready",
          "Exported data is complete and accurate"
        ],
        priority: 'low',
        storyPoints: 3,
        status: 'draft',
        version: 1
      }
    ]

    const newStories = sampleStories.map(story => ({
      ...story,
      id: `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }))

    setUserStories(prev => [...prev, ...newStories])
  }

  const regenerateStory = useCallback(async (storyId: string) => {
    const story = userStories.find(s => s.id === storyId)
    if (!story) return

    setUserStories(prev => prev.map(s => 
      s.id === storyId 
        ? { ...s, status: 'draft' as const, version: s.version + 1 }
        : s
    ))

    // Simulate regeneration process
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Update with "improved" version
    setUserStories(prev => prev.map(s => 
      s.id === storyId 
        ? { 
            ...s, 
            acceptanceCriteria: [
              ...s.acceptanceCriteria,
              "Additional validation and error handling",
              "Improved user experience and accessibility"
            ]
          }
        : s
    ))
  }, [userStories])

  const approveStory = useCallback((storyId: string) => {
    setUserStories(prev => prev.map(story =>
      story.id === storyId
        ? { ...story, status: 'approved' as const }
        : story
    ))
  }, [])

  const rejectStory = useCallback((storyId: string, feedback: string) => {
    setUserStories(prev => prev.map(story =>
      story.id === storyId
        ? { ...story, status: 'rejected' as const, feedback }
        : story
    ))
  }, [])

  const editStory = useCallback((storyId: string, updates: Partial<UserStory>) => {
    setUserStories(prev => {
      const existingStory = prev.find(s => s.id === storyId)
      if (existingStory) {
        return prev.map(story =>
          story.id === storyId
            ? { ...story, ...updates, version: story.version + 1 }
            : story
        )
      } else {
        // Adding new story
        return [...prev, { ...updates, id: storyId } as UserStory]
      }
    })
  }, [])

  return {
    isGenerating,
    progress,
    currentThought,
    userStories,
    generateRequirements,
    regenerateStory,
    approveStory,
    rejectStory,
    editStory
  }
}