import { createDesignAgent, DesignUtils } from '../design-agent'

/**
 * Example usage of the DesignAgent
 * This demonstrates how to use the Design Agent to generate comprehensive design documents
 */

// Sample requirements for a task management application
const sampleRequirements = [
  {
    id: 'FR-001',
    type: 'functional',
    userStory: {
      role: 'project manager',
      feature: 'create and manage projects',
      benefit: 'organize work efficiently'
    },
    acceptanceCriteria: [
      {
        id: 'AC-001',
        condition: 'user creates a new project',
        response: 'system SHALL save project with unique ID',
        format: 'WHEN_THEN'
      }
    ],
    priority: 'high',
    complexity: 'moderate',
    dependencies: [],
    tags: ['project-management']
  },
  {
    id: 'FR-002',
    type: 'functional',
    userStory: {
      role: 'team member',
      feature: 'create and assign tasks',
      benefit: 'track work progress'
    },
    acceptanceCriteria: [
      {
        id: 'AC-002',
        condition: 'user creates a task',
        response: 'system SHALL assign unique task ID and save to database',
        format: 'WHEN_THEN'
      }
    ],
    priority: 'high',
    complexity: 'simple',
    dependencies: ['FR-001'],
    tags: ['task-management']
  },
  {
    id: 'NFR-001',
    type: 'non-functional',
    userStory: {
      role: 'system user',
      feature: 'fast response times',
      benefit: 'productive user experience'
    },
    acceptanceCriteria: [
      {
        id: 'AC-003',
        condition: 'user performs any action',
        response: 'system SHALL respond within 200ms',
        format: 'WHEN_THEN'
      }
    ],
    priority: 'medium',
    complexity: 'complex',
    dependencies: [],
    tags: ['performance']
  }
]

async function demonstrateDesignAgent() {
  console.log('🎯 Design Agent Example - Task Management Application\n')

  try {
    // Create Design Agent instance
    console.log('1. Creating Design Agent...')
    const designAgent = createDesignAgent('openai', 'gpt-4o-mini')
    console.log('   ✅ Design Agent created\n')

    // Step 1: Analyze Architecture
    console.log('2. Analyzing Architecture Patterns...')
    const architecturalAnalysis = await designAgent.analyzeArchitecture(sampleRequirements)
    
    console.log('   📋 Recommended Patterns:')
    architecturalAnalysis.recommendedPatterns.forEach((pattern, index) => {
      console.log(`   ${index + 1}. ${pattern.name}`)
      console.log(`      Description: ${pattern.description}`)
      console.log(`      Benefits: ${pattern.benefits.join(', ')}`)
      console.log(`      Applicability: ${pattern.applicability}`)
    })
    console.log()

    // Step 2: Select Technology Stack
    console.log('3. Selecting Technology Stack...')
    const techStackSelection = await designAgent.selectTechnologyStack(
      sampleRequirements,
      ['Must support real-time updates', 'Prefer TypeScript']
    )
    
    console.log('   🛠️ Recommended Technology Stack:')
    console.log(`   Frontend: ${techStackSelection.recommended.frontend.framework} + ${techStackSelection.recommended.frontend.language}`)
    console.log(`   Backend: ${techStackSelection.recommended.backend.framework} + ${techStackSelection.recommended.backend.runtime}`)
    console.log(`   Database: ${techStackSelection.recommended.backend.database}`)
    console.log(`   Hosting: ${techStackSelection.recommended.infrastructure.hosting}`)
    console.log()

    // Step 3: Generate Complete Design Document
    console.log('4. Generating Complete Design Document...')
    const designDocument = await designAgent.generateDesignDocument(sampleRequirements, {
      includeDetailedSpecs: true,
      architecturalStyle: 'hybrid',
      scalabilityRequirements: 'medium',
      securityLevel: 'standard',
      existingConstraints: ['Must support real-time updates']
    })
    
    console.log('   📄 Design Document Generated:')
    console.log(`   Project: ${designDocument.projectName}`)
    console.log(`   Architecture: ${designDocument.architecture.pattern.name}`)
    console.log(`   Components: ${designDocument.architecture.components.length}`)
    console.log(`   Data Entities: ${designDocument.dataModel.entities.length}`)
    console.log(`   API Endpoints: ${designDocument.apiDesign.endpoints.length}`)
    console.log()

    // Step 4: Design System Components
    console.log('5. Designing System Components...')
    const components = await designAgent.designComponents(
      sampleRequirements,
      architecturalAnalysis.recommendedPatterns[0]
    )
    
    console.log('   🧩 System Components:')
    components.forEach((component, index) => {
      console.log(`   ${index + 1}. ${component.name} (${component.type})`)
      console.log(`      Responsibilities: ${component.responsibilities.join(', ')}`)
      console.log(`      Dependencies: ${component.dependencies.join(', ')}`)
    })
    console.log()

    // Step 5: Design Data Model
    console.log('6. Designing Data Model...')
    const dataModel = await designAgent.designDataModel(
      sampleRequirements,
      techStackSelection.recommended
    )
    
    console.log('   🗄️ Data Model:')
    dataModel.entities.forEach((entity, index) => {
      console.log(`   ${index + 1}. ${entity.name}`)
      console.log(`      Attributes: ${entity.attributes.length}`)
      console.log(`      Relationships: ${entity.relationships.length}`)
    })
    console.log()

    // Step 6: Design API
    console.log('7. Designing API Specifications...')
    const apiDesign = await designAgent.designAPI(sampleRequirements, components, dataModel)
    
    console.log('   🌐 API Design:')
    console.log(`   Authentication: ${apiDesign.authentication.type}`)
    console.log(`   Endpoints: ${apiDesign.endpoints.length}`)
    apiDesign.endpoints.forEach((endpoint, index) => {
      console.log(`   ${index + 1}. ${endpoint.method} ${endpoint.path}`)
      console.log(`      Description: ${endpoint.description}`)
    })
    console.log()

    // Step 7: Perform Comprehensive Analysis
    console.log('8. Performing Comprehensive Design Analysis...')
    const comprehensiveAnalysis = await designAgent.performDesignAnalysis(sampleRequirements, {
      includeDetailedSpecs: true,
      architecturalStyle: 'hybrid',
      scalabilityRequirements: 'medium',
      securityLevel: 'standard'
    })
    
    console.log('   📊 Analysis Results:')
    console.log(`   Quality Score: ${comprehensiveAnalysis.qualityScore}/100`)
    console.log(`   Recommendations: ${comprehensiveAnalysis.recommendations.length}`)
    comprehensiveAnalysis.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`)
    })
    console.log()

    // Step 8: Generate Markdown Documentation
    console.log('9. Generating Markdown Documentation...')
    const markdownDoc = await designAgent.generateMarkdownDocument(comprehensiveAnalysis.designDocument)
    
    console.log('   📝 Markdown Documentation Generated')
    console.log(`   Length: ${markdownDoc.length} characters`)
    console.log(`   Preview: ${markdownDoc.substring(0, 200)}...`)
    console.log()

    // Step 9: Validate Design Completeness
    console.log('10. Validating Design Completeness...')
    const validation = DesignUtils.validateDesignCompleteness(comprehensiveAnalysis.designDocument)
    
    console.log('   ✅ Validation Results:')
    console.log(`   Complete: ${validation.isComplete}`)
    console.log(`   Completeness Score: ${validation.completenessScore}/100`)
    if (validation.missingElements.length > 0) {
      console.log(`   Missing Elements: ${validation.missingElements.join(', ')}`)
    }
    console.log()

    // Step 10: Extract Technology Dependencies
    console.log('11. Extracting Technology Dependencies...')
    const dependencies = DesignUtils.extractTechnologyDependencies(comprehensiveAnalysis.designDocument)
    
    console.log('   🔗 Technology Dependencies:')
    console.log(`   Frontend: ${dependencies.frontend.join(', ')}`)
    console.log(`   Backend: ${dependencies.backend.join(', ')}`)
    console.log(`   Database: ${dependencies.database.join(', ')}`)
    console.log(`   Infrastructure: ${dependencies.infrastructure.join(', ')}`)
    console.log()

    // Step 11: Generate Component Dependency Graph
    console.log('12. Generating Component Dependency Graph...')
    const dependencyGraph = DesignUtils.generateComponentDependencyGraph(components)
    
    console.log('   📈 Component Dependency Graph:')
    console.log(`   Nodes: ${dependencyGraph.nodes.length}`)
    console.log(`   Edges: ${dependencyGraph.edges.length}`)
    dependencyGraph.nodes.forEach(node => {
      console.log(`   • ${node.label} (${node.type})`)
    })
    dependencyGraph.edges.forEach(edge => {
      console.log(`   • ${edge.from} → ${edge.to}`)
    })
    console.log()

    console.log('🎉 Design Agent Example Completed Successfully!')
    console.log('\n📋 Summary of Generated Artifacts:')
    console.log('- ✅ Architectural Analysis and Pattern Recommendations')
    console.log('- ✅ Technology Stack Selection with Rationale')
    console.log('- ✅ Comprehensive Design Document')
    console.log('- ✅ System Component Design')
    console.log('- ✅ Data Model with Entity Relationships')
    console.log('- ✅ API Specifications and Endpoints')
    console.log('- ✅ Markdown Documentation')
    console.log('- ✅ Design Quality Assessment')
    console.log('- ✅ Technology Dependency Analysis')
    console.log('- ✅ Component Dependency Graph')

    return {
      architecturalAnalysis,
      techStackSelection,
      designDocument: comprehensiveAnalysis.designDocument,
      components,
      dataModel,
      apiDesign,
      markdownDoc,
      validation,
      dependencies,
      dependencyGraph
    }

  } catch (error) {
    console.error('❌ Design Agent Example Failed:', error)
    throw error
  }
}

// Export for use in other modules
export { demonstrateDesignAgent, sampleRequirements }

// Run example if this file is executed directly
if (require.main === module) {
  demonstrateDesignAgent()
    .then(results => {
      console.log('\n✨ Example completed successfully!')
      console.log('Generated artifacts are available in the results object.')
    })
    .catch(error => {
      console.error('Example failed:', error)
      process.exit(1)
    })
}