"use strict";
/**
 * AG3NT Framework - Executor Agent
 *
 * Specialized agent for executing individual tasks and coordinating with other agents.
 * Acts as the orchestrator for task execution and delegation.
 *
 * Features:
 * - Task execution coordination
 * - Agent delegation and handoffs
 * - Progress monitoring and reporting
 * - Error handling and recovery
 * - Quality assurance integration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.ExecutorAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Executor Agent - Task execution and coordination
 */
class ExecutorAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('executor', {
            capabilities: {
                requiredCapabilities: [
                    'task_execution',
                    'agent_coordination',
                    'progress_monitoring',
                    'quality_assurance',
                    'error_handling',
                    'resource_management'
                ],
                contextFilters: ['execution', 'tasks', 'agents', 'resources'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.executionSteps = [
            'analyze_task', 'plan_execution', 'delegate_or_execute',
            'monitor_progress', 'quality_check', 'handle_results', 'plan_next_actions'
        ];
    }
    /**
     * Execute task execution workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`⚡ Starting task execution: ${input.task.title}`);
        // Initialize execution log
        state.results.executionLog = [];
        // Execute execution steps sequentially
        for (const stepId of this.executionSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            // Log execution step
            this.logExecution(stepId, 'info', stepResult);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            if (stepResult.delegated) {
                state.results.status = 'delegated';
                state.results.delegatedTo = stepResult.delegatedTo;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed and not delegated
        if (!state.needsInput && state.results.status !== 'delegated') {
            state.completed = true;
            console.log(`✅ Task execution completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual execution step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_task':
                return await this.analyzeTaskWithMCP(enhancedState, input);
            case 'plan_execution':
                return await this.planExecutionWithMCP(enhancedState);
            case 'delegate_or_execute':
                return await this.delegateOrExecuteWithMCP(enhancedState);
            case 'monitor_progress':
                return await this.monitorProgressWithMCP(enhancedState);
            case 'quality_check':
                return await this.qualityCheckWithMCP(enhancedState);
            case 'handle_results':
                return await this.handleResultsWithMCP(enhancedState);
            case 'plan_next_actions':
                return await this.planNextActionsWithMCP(enhancedState);
            default:
                throw new Error(`Unknown execution step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.executionSteps.length;
    }
    /**
     * Get relevant documentation for task execution
     */
    async getRelevantDocumentation() {
        return {
            taskExecution: 'Task execution patterns and best practices',
            agentCoordination: 'Multi-agent coordination and delegation strategies',
            qualityAssurance: 'Quality metrics and validation procedures',
            errorHandling: 'Error recovery and resilience patterns',
            resourceManagement: 'Resource allocation and optimization'
        };
    }
    /**
     * Log execution activity
     */
    logExecution(action, status, details) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            action,
            agentId: this.agentId,
            details,
            status
        };
        if (!this.state.results.executionLog) {
            this.state.results.executionLog = [];
        }
        this.state.results.executionLog.push(logEntry);
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeTaskWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeExecutableTask(input.task, input.context, input.resources);
        this.state.results.taskAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async planExecutionWithMCP(state) {
        const taskAnalysis = this.state.results.taskAnalysis;
        const executionPlan = await ai_service_1.aiService.planTaskExecution(taskAnalysis, this.state.input.resources);
        this.state.results.executionPlan = executionPlan;
        return {
            results: executionPlan,
            needsInput: false,
            completed: false
        };
    }
    async delegateOrExecuteWithMCP(state) {
        const executionPlan = this.state.results.executionPlan;
        const task = this.state.input.task;
        // Determine if task should be delegated or executed directly
        const shouldDelegate = await ai_service_1.aiService.shouldDelegateTask(task, executionPlan, this.state.input.resources.agents);
        if (shouldDelegate.delegate) {
            // Delegate to specialized agent
            const delegationResult = await this.delegateTask(task, shouldDelegate.targetAgent, shouldDelegate.delegationContext);
            return {
                results: delegationResult,
                delegated: true,
                delegatedTo: shouldDelegate.targetAgent,
                needsInput: false,
                completed: false
            };
        }
        else {
            // Execute directly
            const executionResult = await ai_service_1.aiService.executeTask(task, executionPlan);
            this.state.results.executionResult = executionResult;
            return {
                results: executionResult,
                needsInput: false,
                completed: false
            };
        }
    }
    async monitorProgressWithMCP(state) {
        const executionResult = this.state.results.executionResult;
        if (!executionResult) {
            // Task was delegated, monitor delegation
            const delegationStatus = await this.monitorDelegation();
            return {
                results: delegationStatus,
                needsInput: false,
                completed: false
            };
        }
        // Monitor direct execution
        const progressMetrics = await ai_service_1.aiService.monitorTaskProgress(executionResult);
        this.state.results.progressMetrics = progressMetrics;
        return {
            results: progressMetrics,
            needsInput: false,
            completed: false
        };
    }
    async qualityCheckWithMCP(state) {
        const executionResult = this.state.results.executionResult;
        const qualityMetrics = await ai_service_1.aiService.performQualityCheck(executionResult, this.state.input.task.acceptanceCriteria);
        this.state.results.qualityMetrics = qualityMetrics;
        return {
            results: qualityMetrics,
            needsInput: false,
            completed: false
        };
    }
    async handleResultsWithMCP(state) {
        const executionResult = this.state.results.executionResult;
        const qualityMetrics = this.state.results.qualityMetrics;
        const resultHandling = await ai_service_1.aiService.handleTaskResults(executionResult, qualityMetrics, this.state.input.task);
        this.state.results.resultHandling = resultHandling;
        return {
            results: resultHandling,
            needsInput: false,
            completed: false
        };
    }
    async planNextActionsWithMCP(state) {
        const resultHandling = this.state.results.resultHandling;
        const task = this.state.input.task;
        const nextActions = await ai_service_1.aiService.planNextActions(resultHandling, task, this.state.input.context);
        this.state.results.nextActions = nextActions;
        return {
            results: nextActions,
            needsInput: false,
            completed: true // Final step
        };
    }
    /**
     * Delegate task to specialized agent
     */
    async delegateTask(task, targetAgent, context) {
        // Send delegation message through communication protocol
        // This would integrate with the AgentCommunicationProtocol
        console.log(`🔄 Delegating task ${task.taskId} to ${targetAgent}`);
        return {
            delegated: true,
            targetAgent,
            delegationId: `delegation-${Date.now()}`,
            context
        };
    }
    /**
     * Monitor delegated task progress
     */
    async monitorDelegation() {
        // Monitor delegation through communication protocol
        // This would check status of delegated tasks
        return {
            status: 'monitoring',
            lastUpdate: new Date().toISOString()
        };
    }
}
exports.ExecutorAgent = ExecutorAgent;
exports.default = ExecutorAgent;
//# sourceMappingURL=executor-agent.js.map