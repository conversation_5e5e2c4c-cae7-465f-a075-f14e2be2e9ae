import { useState, useEffect, useCallback } from "react"
import { 
  planningSessionManager, 
  PlanningWorkflowState, 
  PlanningPreferences 
} from "@/lib/planning-session-manager"
import { PlanningMode, PlanningSession } from "@/components/planning-mode-selector"

export interface UsePlanningSessionReturn {
  // State
  currentSession: PlanningSession | undefined
  preferences: PlanningPreferences
  sessionHistory: PlanningSession[]
  isLoading: boolean

  // Session Management
  startPlanning: (mode: PlanningMode, projectName?: string) => void
  pausePlanning: () => void
  resumePlanning: () => void
  stopPlanning: () => void
  completeSession: () => void

  // Phase Management
  advancePhase: () => void
  updatePhaseProgress: (progress: number) => void
  getCurrentPhase: () => PlanningSession["currentPhase"] | undefined
  getProgress: () => number

  // Preferences
  updatePreferences: (preferences: Partial<PlanningPreferences>) => void
  shouldShowIntervention: (phase: keyof PlanningPreferences["interventionPoints"]) => boolean

  // Workflow
  getWorkflowRoute: () => string
  
  // Utilities
  getSessionMetrics: () => ReturnType<typeof planningSessionManager.getSessionMetrics>
  clearHistory: () => void
  handleError: (error: string) => void
}

export function usePlanningSession(): UsePlanningSessionReturn {
  const [state, setState] = useState<PlanningWorkflowState>(() => 
    planningSessionManager.getState()
  )
  const [isLoading, setIsLoading] = useState(false)

  // Subscribe to state changes
  useEffect(() => {
    const unsubscribe = planningSessionManager.subscribe((newState) => {
      setState(newState)
    })

    return unsubscribe
  }, [])

  // Session Management
  const startPlanning = useCallback(async (mode: PlanningMode, projectName?: string) => {
    setIsLoading(true)
    try {
      planningSessionManager.createSession(mode, projectName)
    } catch (error) {
      console.error("Failed to start planning session:", error)
      planningSessionManager.handleSessionError(error instanceof Error ? error.message : "Unknown error")
    } finally {
      setIsLoading(false)
    }
  }, [])

  const pausePlanning = useCallback(() => {
    planningSessionManager.pauseSession()
  }, [])

  const resumePlanning = useCallback(() => {
    planningSessionManager.resumeSession()
  }, [])

  const stopPlanning = useCallback(() => {
    planningSessionManager.stopSession()
  }, [])

  const completeSession = useCallback(() => {
    planningSessionManager.completeSession()
  }, [])

  // Phase Management
  const advancePhase = useCallback(() => {
    planningSessionManager.advancePhase()
  }, [])

  const updatePhaseProgress = useCallback((progress: number) => {
    planningSessionManager.updatePhaseProgress(progress)
  }, [])

  const getCurrentPhase = useCallback(() => {
    return state.currentSession?.currentPhase
  }, [state.currentSession])

  const getProgress = useCallback(() => {
    return state.currentSession?.progress || 0
  }, [state.currentSession])

  // Preferences
  const updatePreferences = useCallback((preferences: Partial<PlanningPreferences>) => {
    planningSessionManager.updatePreferences(preferences)
  }, [])

  const shouldShowIntervention = useCallback((phase: keyof PlanningPreferences["interventionPoints"]) => {
    return planningSessionManager.shouldShowInterventionPoint(phase)
  }, [])

  // Workflow
  const getWorkflowRoute = useCallback(() => {
    return planningSessionManager.getWorkflowRoute()
  }, [])

  // Utilities
  const getSessionMetrics = useCallback(() => {
    return planningSessionManager.getSessionMetrics()
  }, [])

  const clearHistory = useCallback(() => {
    planningSessionManager.clearHistory()
  }, [])

  const handleError = useCallback((error: string) => {
    planningSessionManager.handleSessionError(error)
  }, [])

  return {
    // State
    currentSession: state.currentSession,
    preferences: state.preferences,
    sessionHistory: state.history,
    isLoading,

    // Session Management
    startPlanning,
    pausePlanning,
    resumePlanning,
    stopPlanning,
    completeSession,

    // Phase Management
    advancePhase,
    updatePhaseProgress,
    getCurrentPhase,
    getProgress,

    // Preferences
    updatePreferences,
    shouldShowIntervention,

    // Workflow
    getWorkflowRoute,

    // Utilities
    getSessionMetrics,
    clearHistory,
    handleError
  }
}

// Additional hooks for specific use cases

export function usePlanningMode() {
  const { preferences, updatePreferences } = usePlanningSession()
  
  const setDefaultMode = useCallback((mode: PlanningMode) => {
    updatePreferences({ defaultMode: mode })
  }, [updatePreferences])

  return {
    defaultMode: preferences.defaultMode,
    setDefaultMode
  }
}

export function usePlanningProgress() {
  const { currentSession, updatePhaseProgress, advancePhase } = usePlanningSession()

  const incrementProgress = useCallback((amount: number = 10) => {
    if (!currentSession) return
    
    const newProgress = Math.min(currentSession.progress + amount, 100)
    updatePhaseProgress(newProgress)
  }, [currentSession, updatePhaseProgress])

  const setProgress = useCallback((progress: number) => {
    updatePhaseProgress(Math.max(0, Math.min(100, progress)))
  }, [updatePhaseProgress])

  return {
    progress: currentSession?.progress || 0,
    phase: currentSession?.currentPhase || "requirements",
    incrementProgress,
    setProgress,
    advancePhase
  }
}

export function usePlanningInterventions() {
  const { preferences, updatePreferences, shouldShowIntervention } = usePlanningSession()

  const toggleIntervention = useCallback((phase: keyof PlanningPreferences["interventionPoints"]) => {
    updatePreferences({
      interventionPoints: {
        ...preferences.interventionPoints,
        [phase]: !preferences.interventionPoints[phase]
      }
    })
  }, [preferences.interventionPoints, updatePreferences])

  return {
    interventionPoints: preferences.interventionPoints,
    toggleIntervention,
    shouldShowIntervention
  }
}