"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, FileText, Zap } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface StreamingPreviewProps {
  className?: string
}

export default function StreamingPreview({ className }: StreamingPreviewProps) {
  const [currentLine, setCurrentLine] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const currentLineRef = useRef<HTMLDivElement>(null)

  const codeLines = [
    `import React, { useState } from 'react'`,
    `import { Button } from '@/components/ui/button'`,
    `import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'`,
    `import { Badge } from '@/components/ui/badge'`,
    `import { ArrowLeft, Users, Activity, Settings } from 'lucide-react'`,
    ``,
    `export default function AdminDashboard() {`,
    `  const [activeUsers, setActiveUsers] = useState(247)`,
    `  const [systemStatus, setSystemStatus] = useState('operational')`,
    ``,
    `  return (`,
    `    <div className="min-h-screen bg-gray-50 p-6">`,
    `      <div className="max-w-7xl mx-auto">`,
    `        <div className="flex items-center gap-4 mb-8">`,
    `          <Button variant="outline" size="sm">`,
    `            <ArrowLeft className="w-4 h-4 mr-2" />`,
    `            Back to Editor`,
    `          </Button>`,
    `          <div>`,
    `            <h1 className="text-3xl font-bold text-gray-900">`,
    `              <span className="text-blue-600">APBX</span> Admin`,
    `            </h1>`,
    `            <p className="text-gray-600">System monitoring and management</p>`,
    `          </div>`,
    `        </div>`,
    ``,
    `        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">`,
    `          <Card>`,
    `            <CardHeader className="flex flex-row items-center justify-between">`,
    `              <CardTitle className="text-sm font-medium">Active Users</CardTitle>`,
    `              <Users className="h-4 w-4 text-muted-foreground" />`,
    `            </CardHeader>`,
    `            <CardContent>`,
    `              <div className="text-2xl font-bold">{activeUsers}</div>`,
    `              <p className="text-xs text-muted-foreground">+12% from last hour</p>`,
    `            </CardContent>`,
    `          </Card>`,
    ``,
    `          <Card>`,
    `            <CardHeader className="flex flex-row items-center justify-between">`,
    `              <CardTitle className="text-sm font-medium">System Status</CardTitle>`,
    `              <Activity className="h-4 w-4 text-muted-foreground" />`,
    `            </CardHeader>`,
    `            <CardContent>`,
    `              <Badge variant={systemStatus === 'operational' ? 'default' : 'destructive'}>`,
    `                {systemStatus}`,
    `              </Badge>`,
    `              <p className="text-xs text-muted-foreground mt-2">All systems running</p>`,
    `            </CardContent>`,
    `          </Card>`,
    ``,
    `          <Card>`,
    `            <CardHeader className="flex flex-row items-center justify-between">`,
    `              <CardTitle className="text-sm font-medium">Settings</CardTitle>`,
    `              <Settings className="h-4 w-4 text-muted-foreground" />`,
    `            </CardHeader>`,
    `            <CardContent>`,
    `              <Button size="sm" className="w-full">Configure</Button>`,
    `            </CardContent>`,
    `          </Card>`,
    `        </div>`,
    `      </div>`,
    `    </div>`,
    `  )`,
    `}`
  ]

  // Auto-start streaming on mount
  useEffect(() => {
    if (currentLine < codeLines.length - 1) {
      const timer = setTimeout(() => {
        setCurrentLine((prev) => prev + 1)
      }, 120) // Slightly faster for better UX
      return () => clearTimeout(timer)
    } else if (currentLine === codeLines.length - 1 && !isComplete) {
      setIsComplete(true)
      // Restart after a pause
      setTimeout(() => {
        setCurrentLine(0)
        setIsComplete(false)
      }, 3000)
    }
  }, [currentLine, codeLines.length, isComplete])

  // Auto-scroll to current line
  useEffect(() => {
    if (currentLineRef.current && scrollAreaRef.current) {
      currentLineRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  }, [currentLine])

  return (
    <div className={`h-full flex items-center justify-center p-8 ${className || ''}`}>
      <Card className="w-full max-w-2xl bg-[#1a1a1a] border-[#2a2a2a] shadow-xl">
        <CardHeader className="border-b border-[#2a2a2a] pb-3">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <FileText className="w-4 h-4 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-white text-base">
                components/admin-dashboard.tsx
              </CardTitle>
              <p className="text-gray-400 text-xs">
                AG3NT is generating code...
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <div className="h-64 overflow-hidden">
            {/* Stream Header */}
            <div className="bg-[#181818] border-b border-[#2a2a2a] px-3 py-2 flex items-center gap-2">
              <Loader2 className="w-3 h-3 animate-spin text-blue-400" />
              <span className="text-blue-400 text-xs font-medium">Generating...</span>
              <div className="ml-auto flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-400">Live</span>
                </div>
                <span className="text-xs text-gray-500">
                  {currentLine + 1}/{codeLines.length}
                </span>
              </div>
            </div>

            {/* Code Stream */}
            <ScrollArea className="h-full bg-[#181818]" ref={scrollAreaRef}>
              <div className="p-3 font-mono text-xs space-y-0.5">
                {codeLines.slice(0, Math.min(currentLine + 1, codeLines.length)).map((line, index) => (
                  <div
                    key={index}
                    ref={index === Math.min(currentLine, codeLines.length - 1) ? currentLineRef : null}
                    className={`flex items-start gap-2 transition-all duration-200 ${
                      index === Math.min(currentLine, codeLines.length - 1)
                        ? "text-blue-400 bg-blue-500/10 rounded px-1 py-0.5"
                        : "text-gray-300"
                    }`}
                  >
                    <span className="text-gray-600 text-xs w-6 text-right flex-shrink-0">{index + 1}</span>
                    <span className="flex-1 leading-tight">
                      {line.split(" ").map((word, wordIndex) => (
                        <span
                          key={wordIndex}
                          className={
                            word.includes("className") || word.includes("onClick") || word.includes("variant")
                              ? "text-yellow-400"
                              : word.includes('"') || word.includes("'")
                                ? "text-green-400"
                                : word.includes("<") || word.includes(">") || word.includes("React") || word.includes("import") || word.includes("export") || word.includes("function") || word.includes("const") || word.includes("return")
                                  ? "text-cyan-400"
                                  : word.includes("useState") || word.includes("useEffect")
                                    ? "text-purple-400"
                                    : ""
                          }
                        >
                          {word}{" "}
                        </span>
                      ))}
                    </span>
                  </div>
                ))}

                {/* Typing cursor */}
                {!isComplete && currentLine < codeLines.length - 1 && (
                  <div className="flex items-center gap-2">
                    <span className="text-gray-600 text-xs w-6 text-right">
                      {Math.min(currentLine + 2, codeLines.length)}
                    </span>
                    <div className="w-1.5 h-3 bg-blue-400 animate-pulse rounded-sm"></div>
                  </div>
                )}

                {/* Completion indicator */}
                {isComplete && (
                  <div className="flex items-center gap-2 mt-2 p-1.5 bg-green-500/10 rounded">
                    <span className="text-gray-600 text-xs w-6 text-right">✓</span>
                    <span className="text-green-400 text-xs">Complete! Restarting...</span>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Progress Footer */}
            <div className="bg-[#181818] border-t border-[#2a2a2a] px-3 py-1.5">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-[#2a2a2a] rounded-full h-1">
                    <div
                      className="bg-blue-400 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${((currentLine + 1) / codeLines.length) * 100}%` }}
                    />
                  </div>
                  <span className="text-blue-400 text-xs">
                    {Math.round(((currentLine + 1) / codeLines.length) * 100)}%
                  </span>
                </div>
                <div className="flex items-center gap-1 text-gray-400">
                  <Zap className="w-2.5 h-2.5" />
                  <span className="text-xs">AI Generating</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
