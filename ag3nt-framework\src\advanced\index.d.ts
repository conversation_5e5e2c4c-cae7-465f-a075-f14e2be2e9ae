/**
 * AG3NT Framework - Advanced Features
 *
 * Export all advanced framework features that differentiate AG3NT
 * from competitors like CrewAI and LangGraph.
 */
export { default as AdaptiveLearningSystem } from './adaptive-learning-system';
export type { LearningConfiguration, ExecutionRecord, LearningPattern, OptimizationRecommendation, LearningInsight, AdaptationResult } from './adaptive-learning-system';
export { default as TemporalContextDatabase } from './temporal-context-database';
export type { TemporalDatabaseConfig, TemporalNode, TemporalRelationship, TemporalQuery, QueryResult, EvolutionAnalysis, ContextSnapshot } from './temporal-context-database';
export { default as RealtimeCollaborationSystem } from './realtime-collaboration-system';
export type { CollaborationConfig, CollaborativeSession, AgentParticipant, Contribution, Conflict, SharedWorkspace, ConsensusRequest, CollaborationMetrics } from './realtime-collaboration-system';
export { default as DynamicOptimizationSystem } from './dynamic-optimization-system';
export type { OptimizationConfig, AgentConfiguration, OptimizationCandidate, OptimizationExperiment, OptimizationResult, OptimizationInsight } from './dynamic-optimization-system';
export { default as AgentMarketplace } from './agent-marketplace';
export type { MarketplaceConfig, AgentPlugin, InstalledPlugin, PluginInstallation, MarketplaceQuery, MarketplaceResult, PluginUpdate } from './agent-marketplace';
export { default as AdvancedMonitoringSystem } from './advanced-monitoring-system';
export type { MonitoringConfig, MetricDefinition, MetricValue, MonitoringDashboard, PerformanceInsight, PredictiveModel, AnomalyDetection } from './advanced-monitoring-system';
/**
 * Advanced Features Manager
 *
 * Centralized manager for all advanced framework features
 */
export declare class AdvancedFeaturesManager {
    private config;
    private adaptiveLearning?;
    private temporalDatabase?;
    private collaboration?;
    private optimization?;
    private marketplace?;
    private monitoring?;
    constructor(config?: AdvancedFeaturesConfig);
    /**
     * Initialize all advanced features
     */
    initialize(): Promise<void>;
    /**
     * Get adaptive learning system
     */
    getAdaptiveLearning(): AdaptiveLearningSystem | undefined;
    /**
     * Get temporal database
     */
    getTemporalDatabase(): TemporalContextDatabase | undefined;
    /**
     * Get collaboration system
     */
    getCollaboration(): RealtimeCollaborationSystem | undefined;
    /**
     * Get optimization system
     */
    getOptimization(): DynamicOptimizationSystem | undefined;
    /**
     * Get marketplace
     */
    getMarketplace(): AgentMarketplace | undefined;
    /**
     * Get monitoring system
     */
    getMonitoring(): AdvancedMonitoringSystem | undefined;
    /**
     * Record agent execution for learning and monitoring
     */
    recordAgentExecution(agentId: string, executionData: AgentExecutionData): void;
    /**
     * Get comprehensive insights across all systems
     */
    getComprehensiveInsights(agentId?: string): Promise<ComprehensiveInsights>;
    /**
     * Shutdown all advanced features
     */
    shutdown(): Promise<void>;
}
export interface AdvancedFeaturesConfig {
    adaptiveLearning?: Partial<LearningConfiguration>;
    temporalDatabase?: Partial<TemporalDatabaseConfig>;
    collaboration?: Partial<CollaborationConfig>;
    optimization?: Partial<OptimizationConfig>;
    marketplace?: Partial<MarketplaceConfig>;
    monitoring?: Partial<MonitoringConfig>;
}
export interface AgentExecutionData {
    learningRecord?: ExecutionRecord;
    contextSnapshot?: ContextSnapshot;
    metrics?: Record<string, number>;
    performance?: any;
    pluginUsage?: PluginUsageRecord[];
}
export interface PluginUsageRecord {
    pluginId: string;
    executionTime: number;
    success: boolean;
    dataProcessed: number;
}
export interface ComprehensiveInsights {
    learning: LearningInsight[];
    optimization: OptimizationRecommendation[];
    performance: PerformanceInsight[];
    collaboration: any[];
    marketplace: any[];
}
import type { LearningConfiguration, ExecutionRecord, LearningInsight, OptimizationRecommendation } from './adaptive-learning-system';
import type { TemporalDatabaseConfig, ContextSnapshot } from './temporal-context-database';
import type { CollaborationConfig } from './realtime-collaboration-system';
import type { OptimizationConfig } from './dynamic-optimization-system';
import type { MarketplaceConfig } from './agent-marketplace';
import type { MonitoringConfig, PerformanceInsight } from './advanced-monitoring-system';
import AdaptiveLearningSystem from './adaptive-learning-system';
import TemporalContextDatabase from './temporal-context-database';
import RealtimeCollaborationSystem from './realtime-collaboration-system';
import DynamicOptimizationSystem from './dynamic-optimization-system';
import AgentMarketplace from './agent-marketplace';
import AdvancedMonitoringSystem from './advanced-monitoring-system';
export default AdvancedFeaturesManager;
//# sourceMappingURL=index.d.ts.map