{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["index.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,OAAO,IAAI,sBAAsB,EAAE,MAAM,4BAA4B,CAAA;AAC9E,YAAY,EACV,qBAAqB,EACrB,eAAe,EACf,eAAe,EACf,0BAA0B,EAC1B,eAAe,EACf,gBAAgB,EACjB,MAAM,4BAA4B,CAAA;AAGnC,OAAO,EAAE,OAAO,IAAI,uBAAuB,EAAE,MAAM,6BAA6B,CAAA;AAChF,YAAY,EACV,sBAAsB,EACtB,YAAY,EACZ,oBAAoB,EACpB,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,eAAe,EAChB,MAAM,6BAA6B,CAAA;AAGpC,OAAO,EAAE,OAAO,IAAI,2BAA2B,EAAE,MAAM,iCAAiC,CAAA;AACxF,YAAY,EACV,mBAAmB,EACnB,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,EACZ,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,oBAAoB,EACrB,MAAM,iCAAiC,CAAA;AAGxC,OAAO,EAAE,OAAO,IAAI,yBAAyB,EAAE,MAAM,+BAA+B,CAAA;AACpF,YAAY,EACV,kBAAkB,EAClB,kBAAkB,EAClB,qBAAqB,EACrB,sBAAsB,EACtB,kBAAkB,EAClB,mBAAmB,EACpB,MAAM,+BAA+B,CAAA;AAGtC,OAAO,EAAE,OAAO,IAAI,gBAAgB,EAAE,MAAM,qBAAqB,CAAA;AACjE,YAAY,EACV,iBAAiB,EACjB,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,iBAAiB,EACjB,YAAY,EACb,MAAM,qBAAqB,CAAA;AAG5B,OAAO,EAAE,OAAO,IAAI,wBAAwB,EAAE,MAAM,8BAA8B,CAAA;AAClF,YAAY,EACV,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,mBAAmB,EACnB,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EACjB,MAAM,8BAA8B,CAAA;AAErC;;;;GAIG;AACH,qBAAa,uBAAuB;IAQtB,OAAO,CAAC,MAAM;IAP1B,OAAO,CAAC,gBAAgB,CAAC,CAAwB;IACjD,OAAO,CAAC,gBAAgB,CAAC,CAAyB;IAClD,OAAO,CAAC,aAAa,CAAC,CAA6B;IACnD,OAAO,CAAC,YAAY,CAAC,CAA2B;IAChD,OAAO,CAAC,WAAW,CAAC,CAAkB;IACtC,OAAO,CAAC,UAAU,CAAC,CAA0B;gBAEzB,MAAM,GAAE,sBAA2B;IAEvD;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAoCjC;;OAEG;IACH,mBAAmB,IAAI,sBAAsB,GAAG,SAAS;IAIzD;;OAEG;IACH,mBAAmB,IAAI,uBAAuB,GAAG,SAAS;IAI1D;;OAEG;IACH,gBAAgB,IAAI,2BAA2B,GAAG,SAAS;IAI3D;;OAEG;IACH,eAAe,IAAI,yBAAyB,GAAG,SAAS;IAIxD;;OAEG;IACH,cAAc,IAAI,gBAAgB,GAAG,SAAS;IAI9C;;OAEG;IACH,aAAa,IAAI,wBAAwB,GAAG,SAAS;IAIrD;;OAEG;IACH,oBAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,kBAAkB,GAAG,IAAI;IAoC9E;;OAEG;IACG,wBAAwB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAqChF;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAgChC;AAGD,MAAM,WAAW,sBAAsB;IACrC,gBAAgB,CAAC,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAA;IACjD,gBAAgB,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAA;IAClD,aAAa,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAA;IAC5C,YAAY,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAA;IAC1C,WAAW,CAAC,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACxC,UAAU,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAA;CACvC;AAED,MAAM,WAAW,kBAAkB;IACjC,cAAc,CAAC,EAAE,eAAe,CAAA;IAChC,eAAe,CAAC,EAAE,eAAe,CAAA;IACjC,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAChC,WAAW,CAAC,EAAE,GAAG,CAAA;IACjB,WAAW,CAAC,EAAE,iBAAiB,EAAE,CAAA;CAClC;AAED,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,MAAM,CAAA;IAChB,aAAa,EAAE,MAAM,CAAA;IACrB,OAAO,EAAE,OAAO,CAAA;IAChB,aAAa,EAAE,MAAM,CAAA;CACtB;AAED,MAAM,WAAW,qBAAqB;IACpC,QAAQ,EAAE,eAAe,EAAE,CAAA;IAC3B,YAAY,EAAE,0BAA0B,EAAE,CAAA;IAC1C,WAAW,EAAE,kBAAkB,EAAE,CAAA;IACjC,aAAa,EAAE,GAAG,EAAE,CAAA;IACpB,WAAW,EAAE,GAAG,EAAE,CAAA;CACnB;AAGD,OAAO,KAAK,EAAE,qBAAqB,EAAE,eAAe,EAAE,eAAe,EAAE,0BAA0B,EAAE,MAAM,4BAA4B,CAAA;AACrI,OAAO,KAAK,EAAE,sBAAsB,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAA;AAC1F,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAA;AAC1E,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAA;AACvE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAA;AAC5D,OAAO,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAA;AACxF,OAAO,sBAAsB,MAAM,4BAA4B,CAAA;AAC/D,OAAO,uBAAuB,MAAM,6BAA6B,CAAA;AACjE,OAAO,2BAA2B,MAAM,iCAAiC,CAAA;AACzE,OAAO,yBAAyB,MAAM,+BAA+B,CAAA;AACrE,OAAO,gBAAgB,MAAM,qBAAqB,CAAA;AAClD,OAAO,wBAAwB,MAAM,8BAA8B,CAAA;AAEnE,eAAe,uBAAuB,CAAA"}