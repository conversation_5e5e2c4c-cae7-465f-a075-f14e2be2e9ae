{"version": 3, "file": "frontend-coder-agent.d.ts", "sourceRoot": "", "sources": ["frontend-coder-agent.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAA;AAGvE,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,YAAY,CAAA;IAClB,MAAM,EAAE,mBAAmB,CAAA;IAC3B,YAAY,EAAE,oBAAoB,CAAA;IAClC,QAAQ,EAAE,eAAe,CAAA;CAC1B;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,GAAG,cAAc,GAAG,SAAS,CAAA;IAChF,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAA;IAChD,kBAAkB,EAAE,MAAM,EAAE,CAAA;IAC5B,qBAAqB,EAAE,MAAM,EAAE,CAAA;CAChC;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,CAAC,EAAE,GAAG,EAAE,CAAA;IAClB,OAAO,CAAC,EAAE,GAAG,EAAE,CAAA;IACf,YAAY,CAAC,EAAE,YAAY,CAAA;IAC3B,SAAS,CAAC,EAAE,QAAQ,EAAE,CAAA;IACtB,qBAAqB,CAAC,EAAE,oBAAoB,EAAE,CAAA;CAC/C;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,YAAY,CAAA;IACpB,UAAU,EAAE,UAAU,CAAA;IACtB,OAAO,EAAE,YAAY,CAAA;IACrB,UAAU,EAAE,gBAAgB,CAAA;IAC5B,KAAK,EAAE,WAAW,CAAA;CACnB;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,EAAE,CAAA;IACjB,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,OAAO,EAAE,MAAM,EAAE,CAAA;IACjB,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,CAAA;QACf,OAAO,EAAE,MAAM,CAAA;QACf,KAAK,EAAE,MAAM,CAAA;QACb,IAAI,EAAE,MAAM,CAAA;KACb,CAAA;CACF;AAED,MAAM,WAAW,UAAU;IACzB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,WAAW,EAAE,MAAM,EAAE,CAAA;CACtB;AAED,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAM,EAAE,CAAA;CAChB;AAED,MAAM,WAAW,gBAAgB;IAC/B,CAAC,aAAa,EAAE,MAAM,GAAG,aAAa,CAAA;CACvC;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,KAAK,EAAE,aAAa,EAAE,CAAA;IACtB,MAAM,EAAE,MAAM,EAAE,CAAA;IAChB,QAAQ,EAAE,GAAG,EAAE,CAAA;CAChB;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,CAAC,EAAE,GAAG,CAAA;IACb,WAAW,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,WAAW;IAC1B,KAAK,EAAE,SAAS,GAAG,QAAQ,GAAG,SAAS,CAAA;IACvC,KAAK,EAAE,MAAM,EAAE,CAAA;CAChB;AAED,MAAM,WAAW,QAAQ;IACvB,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,YAAY,EAAE,CAAA;IACrB,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,UAAU,EAAE,MAAM,EAAE,CAAA;CACrB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,WAAW,EAAE,MAAM,EAAE,CAAA;CACtB;AAED,MAAM,WAAW,oBAAoB;IACnC,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,oBAAoB;IACnC,SAAS,EAAE,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,GAAG,SAAS,CAAA;IAC7D,QAAQ,EAAE,YAAY,GAAG,YAAY,CAAA;IACrC,OAAO,EAAE,KAAK,GAAG,MAAM,GAAG,mBAAmB,GAAG,UAAU,GAAG,SAAS,CAAA;IACtE,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,CAAA;IACpE,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,YAAY,CAAA;IACrD,OAAO,EAAE,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAA;IACjD,aAAa,EAAE,OAAO,CAAA;IACtB,WAAW,EAAE,uBAAuB,CAAA;IACpC,OAAO,EAAE,cAAc,CAAA;CACxB;AAED,MAAM,WAAW,uBAAuB;IACtC,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,CAAA;IAClB,aAAa,EAAE,OAAO,CAAA;IACtB,WAAW,EAAE,OAAO,CAAA;CACrB;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;IACf,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,OAAO,CAAA;CAChB;AAED,MAAM,WAAW,eAAe;IAC9B,gBAAgB,EAAE,gBAAgB,CAAA;IAClC,kBAAkB,EAAE,iBAAiB,EAAE,CAAA;IACvC,YAAY,EAAE,UAAU,EAAE,CAAA;IAC1B,WAAW,EAAE,WAAW,CAAA;CACzB;AAED,MAAM,WAAW,gBAAgB;IAC/B,YAAY,EAAE,MAAM,CAAA;IACpB,mBAAmB,EAAE,MAAM,CAAA;IAC3B,cAAc,EAAE,MAAM,CAAA;IACtB,eAAe,EAAE,MAAM,CAAA;IACvB,eAAe,EAAE,MAAM,CAAA;IACvB,cAAc,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,aAAa,EAAE,CAAA;IACtB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,KAAK,EAAE,MAAM,EAAE,CAAA;CAChB;AAED,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,YAAY,GAAG,eAAe,GAAG,gBAAgB,CAAA;CACxD;AAED,MAAM,WAAW,WAAW;IAC1B,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,GAAG,CAAA;IACd,YAAY,EAAE,GAAG,CAAA;CAClB;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,WAAW,GAAG,QAAQ,GAAG,cAAc,CAAA;IAC/C,YAAY,EAAE,WAAW,EAAE,CAAA;IAC3B,WAAW,EAAE,UAAU,EAAE,CAAA;IACzB,WAAW,EAAE,UAAU,EAAE,CAAA;IACzB,kBAAkB,EAAE,kBAAkB,CAAA;IACtC,mBAAmB,EAAE,mBAAmB,CAAA;IACxC,aAAa,EAAE,aAAa,EAAE,CAAA;CAC/B;AAED,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,eAAe,CAAA;IAC/D,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE,MAAM,CAAA;IACf,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAA;IACpC,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,YAAY,EAAE,MAAM,CAAA;CACrB;AAED,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,MAAM,CAAA;IACjB,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,kBAAkB;IACjC,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,MAAM,CAAA;IAClB,eAAe,EAAE,MAAM,CAAA;IACvB,WAAW,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,mBAAmB;IAClC,KAAK,EAAE,MAAM,CAAA;IACb,UAAU,EAAE,sBAAsB,EAAE,CAAA;IACpC,eAAe,EAAE,MAAM,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,OAAO,GAAG,SAAS,GAAG,MAAM,CAAA;IACtC,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,MAAM,CAAA;IACnB,GAAG,EAAE,MAAM,CAAA;CACZ;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,WAAW,GAAG,KAAK,GAAG,OAAO,GAAG,WAAW,CAAA;IACjD,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,MAAM,CAAA;IACf,QAAQ,EAAE,GAAG,EAAE,CAAA;CAChB;AAED;;GAEG;AACH,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,OAAO,CAAC,QAAQ,CAAC,WAAW,CAI3B;gBAEW,MAAM,GAAE,OAAO,CAAC,WAAW,CAAM;IAsB7C;;OAEG;cACa,eAAe,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IA2CvE;;OAEG;YACW,sBAAsB;IA+BpC;;OAEG;IACH,SAAS,CAAC,aAAa,IAAI,MAAM;IAIjC;;OAEG;cACa,wBAAwB,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAe1D,0BAA0B;YAiB1B,yBAAyB;YAiBzB,uBAAuB;YAiBvB,wBAAwB;YAkBxB,uBAAuB;YAkBvB,sBAAsB;YAkBtB,0BAA0B;YAiB1B,0BAA0B;YAiB1B,iBAAiB;YAiBjB,mBAAmB;CAgBlC;AAGD,OAAO,EAAE,kBAAkB,IAAI,OAAO,EAAE,CAAA"}