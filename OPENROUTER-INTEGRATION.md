# OpenRouter Integration Guide

## Overview

This implementation provides comprehensive OpenRouter integration as the primary AI provider for the Autonomous AI Development Ecosystem. OpenRouter serves as a unified gateway to multiple AI models from different providers, offering cost optimization, model diversity, and enhanced reliability through fallback mechanisms.

## Features Implemented

### ✅ Core Integration
- **Primary Provider Configuration**: OpenRouter configured as the default AI provider
- **Comprehensive Model Catalog**: 15+ models from OpenAI, Anthropic, Meta, Google, and specialized providers
- **Fallback Support**: Automatic fallback to direct providers (OpenAI, Anthropic, Google) when needed
- **Environment Validation**: Robust validation of API keys and configuration

### ✅ Cost Tracking & Analytics
- **Real-time Usage Tracking**: Track tokens, costs, latency, and success rates
- **Budget Management**: Monthly budget tracking with alerts and projections
- **Cost Calculation**: Accurate per-request cost calculation based on model pricing
- **Usage Analytics Dashboard**: Comprehensive analytics with charts and insights

### ✅ Model Selection & Recommendations
- **Enhanced Model Selector**: Advanced UI component with categorization and filtering
- **Smart Recommendations**: AI-powered model recommendations based on use case and performance
- **Performance Metrics**: Real-time model performance tracking and comparison
- **Category-based Organization**: Models organized by coding, multimodal, efficient, etc.

### ✅ Performance Monitoring
- **Latency Tracking**: Monitor response times across different models
- **Success Rate Monitoring**: Track request success rates and reliability
- **Provider Health Checks**: Automatic health monitoring for all providers
- **Performance-based Recommendations**: Suggest optimal models based on actual usage data

## Configuration

### Environment Variables

Create a `.env.local` file with the following configuration:

```bash
# Primary Provider (Recommended)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Fallback Providers (Optional but recommended)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_MODEL=openai-gpt-4o-mini

# Budget and Usage Controls
MONTHLY_AI_BUDGET=100.00
MAX_TOKENS_PER_REQUEST=4000
MAX_REQUESTS_PER_MINUTE=10
```

### Getting OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai)
2. Sign up for an account
3. Navigate to the API Keys section
4. Generate a new API key
5. Add credits to your account for usage

## Available Models

### OpenAI Models (via OpenRouter)
- **GPT-4o**: Latest flagship model with vision capabilities
- **GPT-4o Mini**: Faster, cost-effective version of GPT-4o
- **GPT-4 Turbo**: High-performance model with large context

### Anthropic Models (via OpenRouter)
- **Claude 3.5 Sonnet**: Most capable model with excellent reasoning
- **Claude 3.5 Haiku**: Fast and efficient for quick tasks

### Meta Llama Models
- **Llama 3.1 405B**: Largest open-source model
- **Llama 3.1 70B**: High-performance open-source model
- **Llama 3.1 8B**: Efficient model for general tasks

### Specialized Models
- **DeepSeek Coder**: Optimized for programming tasks
- **Qwen 2.5 Coder**: Advanced coding capabilities

### Google Models (via OpenRouter)
- **Gemini Pro 1.5**: Advanced multimodal model
- **Gemini Flash 1.5**: Fast and efficient

## Usage

### Basic Model Selection

```typescript
import { getAIModel } from '@/lib/ai-config'

// Get OpenRouter model
const model = getAIModel('openrouter', 'openai-gpt-4o-mini')

// Automatic fallback if provider unavailable
const fallbackModel = getAIModel('invalid-provider', 'model')
```

### Cost Calculation

```typescript
import { calculateCost } from '@/lib/ai-config'

// Calculate cost for a request
const cost = calculateCost('openai/gpt-4o-mini', 1000, 500)
console.log(`Request cost: $${cost.toFixed(4)}`)
```

### Usage Tracking

```typescript
import { trackAPIUsage } from '@/lib/usage-tracker'

// Track API usage
trackAPIUsage(
  'openrouter',           // provider
  'openai-gpt-4o-mini',  // model
  1000,                   // prompt tokens
  500,                    // completion tokens
  1200,                   // latency in ms
  true                    // success
)
```

### Getting Usage Analytics

```typescript
import { usageTracker } from '@/lib/usage-tracker'

// Get usage statistics
const stats = usageTracker.getUsageStats(30) // last 30 days
console.log(`Total cost: $${stats.totalCost}`)
console.log(`Success rate: ${stats.successRate * 100}%`)

// Get budget status
const budget = usageTracker.getBudgetStatus()
console.log(`Budget used: ${budget.percentUsed}%`)
```

## UI Components

### ModelSelector Component

Advanced model selection with categorization, recommendations, and performance metrics:

```tsx
import ModelSelector from '@/components/model-selector'

<ModelSelector 
  value={selectedModel}
  onValueChange={setSelectedModel}
  showMetrics={true}
  showRecommendations={true}
  compact={false}
/>
```

### UsageAnalytics Component

Comprehensive usage analytics dashboard:

```tsx
import UsageAnalytics from '@/components/usage-analytics'

<UsageAnalytics />
```

## API Endpoints

### Models API
- `GET /api/models` - Get available models with metadata
- `GET /api/models?metrics=true` - Include usage metrics
- `GET /api/models?category=coding` - Filter by category
- `POST /api/models` - Get detailed model information

### Usage API
- `GET /api/usage` - Get usage statistics
- `GET /api/usage?days=30` - Get usage for specific period
- `GET /api/usage?format=csv` - Export usage data as CSV
- `POST /api/usage` - Perform usage management actions

### Chat API
- `POST /api/chat` - Enhanced chat with usage tracking
- Automatic cost tracking and performance monitoring
- Fallback provider support
- Enhanced error handling with suggestions

## Model Categories

### Coding Models
Optimized for programming tasks:
- DeepSeek Coder
- Qwen 2.5 Coder
- GPT-4o (excellent for code)

### Multimodal Models
Support text and vision:
- GPT-4o
- GPT-4o Mini
- Claude 3.5 Sonnet
- Gemini Pro 1.5

### Efficient Models
Cost-effective options:
- Llama 3.1 8B
- Claude 3.5 Haiku
- GPT-4o Mini

### Long Context Models
Large context windows:
- Claude 3.5 Sonnet (200K tokens)
- GPT-4o (128K tokens)
- Gemini Pro 1.5 (2M tokens)

## Cost Optimization

### Automatic Recommendations
The system provides recommendations for:
- **Most Cost Efficient**: Best cost per token ratio
- **Most Reliable**: Highest success rates
- **Best Value**: Optimal performance/cost balance
- **Fastest**: Lowest latency

### Budget Management
- Set monthly budget limits
- Real-time spend tracking
- Projected monthly spend calculations
- Budget alerts and warnings

### Smart Model Selection
- Automatic model recommendations based on task type
- Performance-based model suggestions
- Cost-aware model switching

## Monitoring & Analytics

### Real-time Metrics
- Request latency tracking
- Success rate monitoring
- Token usage analytics
- Cost per request calculations

### Performance Insights
- Model comparison charts
- Usage trend analysis
- Cost optimization suggestions
- Provider health monitoring

### Export Capabilities
- CSV export of usage data
- Comprehensive analytics reports
- Budget and cost summaries

## Error Handling & Fallbacks

### Provider Fallbacks
1. **Primary**: OpenRouter (recommended)
2. **Fallback 1**: Direct OpenAI API
3. **Fallback 2**: Direct Anthropic API
4. **Fallback 3**: Direct Google AI API

### Error Recovery
- Automatic retry with exponential backoff
- Provider health checks
- Graceful degradation
- Detailed error reporting with suggestions

### Rate Limiting
- Configurable request limits
- Automatic rate limit handling
- Queue management for high-volume usage

## Security Considerations

### API Key Management
- Environment variable configuration
- Secure key storage
- Key validation and health checks

### Usage Monitoring
- Comprehensive audit logging
- Usage pattern analysis
- Anomaly detection

### Cost Controls
- Budget limits and alerts
- Usage quotas
- Automatic spending controls

## Troubleshooting

### Common Issues

1. **Missing API Key**
   - Ensure `OPENROUTER_API_KEY` is set in `.env.local`
   - Check API key validity on OpenRouter dashboard

2. **Model Not Available**
   - Check model availability on OpenRouter
   - Verify sufficient credits in account
   - Try fallback providers

3. **High Costs**
   - Review usage analytics dashboard
   - Check model selection (use efficient models for simple tasks)
   - Set appropriate budget limits

4. **Slow Response Times**
   - Check model performance metrics
   - Consider switching to faster models
   - Monitor provider health status

### Debug Mode
Enable detailed logging by setting:
```bash
DEBUG=true
LOG_LEVEL=debug
```

## Performance Benchmarks

Based on typical usage patterns:

| Model | Avg Latency | Cost/1K Tokens | Use Case |
|-------|-------------|----------------|----------|
| GPT-4o Mini | 800ms | $0.0006 | General tasks |
| Claude 3.5 Haiku | 1200ms | $0.004 | Quick responses |
| Llama 3.1 8B | 600ms | $0.00018 | Efficient tasks |
| DeepSeek Coder | 900ms | $0.00028 | Coding tasks |

## Future Enhancements

### Planned Features
- [ ] Advanced model routing based on request content
- [ ] Custom model fine-tuning integration
- [ ] Multi-provider load balancing
- [ ] Advanced cost prediction algorithms
- [ ] Integration with external monitoring tools

### Experimental Features
- [ ] A/B testing for model selection
- [ ] Automatic model performance optimization
- [ ] Custom pricing alerts and notifications
- [ ] Advanced usage pattern analysis

## Support

For issues related to:
- **OpenRouter API**: Contact OpenRouter support
- **Integration Issues**: Check the troubleshooting section
- **Feature Requests**: Submit via project issues

## License

This integration is part of the Autonomous AI Development Ecosystem and follows the same licensing terms as the main project.