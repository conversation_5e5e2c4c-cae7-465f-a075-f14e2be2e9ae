import { streamText, convertToCoreMessages } from 'ai'
import { getAIModel, validateEnvironment, defaultConfig } from '@/lib/ai-config'
import { trackAPIUsage, usageTracker } from '@/lib/usage-tracker'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  const startTime = Date.now()
  let provider = defaultConfig.provider
  let model = defaultConfig.model
  let success = true
  let promptTokens = 0
  let completionTokens = 0

  try {
    // Validate environment variables
    if (!validateEnvironment()) {
      return new Response('Missing required environment variables', { status: 500 })
    }

    const requestBody = await req.json()
    const { messages, ...options } = requestBody
    
    // Extract provider and model from request
    provider = requestBody.provider || defaultConfig.provider
    model = requestBody.model || defaultConfig.model

    // Validate messages
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 })
    }

    // Check budget limits before processing
    const budgetStatus = usageTracker.getBudgetStatus()
    if (budgetStatus.isOverBudget) {
      console.warn('Monthly budget exceeded, but continuing with request')
      // Could implement hard limits here if needed
    }

    // Get the AI model instance with fallback support
    let aiModel
    try {
      aiModel = getAIModel(provider, model)
    } catch (error) {
      console.warn(`Failed to get model ${provider}:${model}, falling back to default`)
      provider = defaultConfig.fallbackProvider
      model = defaultConfig.fallbackModel
      aiModel = getAIModel(provider, model)
    }

    // Convert messages to the format expected by the AI SDK
    const coreMessages = convertToCoreMessages(messages)

    // Enhanced configuration for OpenRouter
    const streamConfig: any = {
      model: aiModel,
      messages: coreMessages,
      maxTokens: options.maxTokens || defaultConfig.maxTokens,
      temperature: options.temperature || 0.7,
      topP: options.topP || 1,
      frequencyPenalty: options.frequencyPenalty || 0,
      presencePenalty: options.presencePenalty || 0,
      system: options.system || 'You are a helpful AI assistant.',
    }

    // Add OpenRouter-specific headers if using OpenRouter
    if (provider === 'openrouter') {
      streamConfig.headers = {
        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        'X-Title': 'Autonomous AI Development Ecosystem'
      }
    }

    // Create the streaming response
    const result = await streamText(streamConfig)

    // Track token usage from the result
    result.usage.then((usage) => {
      if (usage) {
        promptTokens = usage.promptTokens || 0
        completionTokens = usage.completionTokens || 0
        
        // Track the usage
        const latency = Date.now() - startTime
        trackAPIUsage(provider, model, promptTokens, completionTokens, latency, true)
      }
    }).catch((error) => {
      console.error('Error tracking usage:', error)
    })

    // Return the streaming response with enhanced headers
    const response = result.toDataStreamResponse()
    
    // Add custom headers for monitoring
    response.headers.set('X-Provider', provider)
    response.headers.set('X-Model', model)
    response.headers.set('X-Request-ID', `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
    
    return response

  } catch (error) {
    success = false
    console.error('Error in chat API:', error)
    
    // Track failed request
    const latency = Date.now() - startTime
    trackAPIUsage(provider, model, promptTokens, completionTokens, latency, false)
    
    // Handle specific error types with enhanced error reporting
    if (error instanceof Error) {
      if (error.message.includes('API key') || error.message.includes('401')) {
        return new Response(
          JSON.stringify({ 
            error: 'Invalid or missing API key',
            provider,
            model,
            suggestion: 'Please check your API key configuration'
          }), 
          { 
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
      
      if (error.message.includes('rate limit') || error.message.includes('429')) {
        return new Response(
          JSON.stringify({ 
            error: 'Rate limit exceeded',
            provider,
            model,
            suggestion: 'Please wait before making another request'
          }), 
          { 
            status: 429,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
      
      if (error.message.includes('Unsupported')) {
        return new Response(
          JSON.stringify({ 
            error: error.message,
            provider,
            model,
            suggestion: 'Please try a different model or provider'
          }), 
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }

      if (error.message.includes('context length') || error.message.includes('too long')) {
        return new Response(
          JSON.stringify({ 
            error: 'Message too long for model context',
            provider,
            model,
            suggestion: 'Please reduce the message length or try a model with larger context'
          }), 
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
    }

    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        provider,
        model,
        suggestion: 'Please try again or contact support if the issue persists'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
