import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { createDesignAgent, DesignAgent, DesignUtils } from '../design-agent'

// Mock the AI SDK
jest.mock('ai', () => ({
  generateText: jest.fn(),
  generateObject: jest.fn()
}))

// Mock the AI config
jest.mock('../ai-config', () => ({
  getAIModel: jest.fn(() => 'mock-model')
}))

describe('DesignAgent', () => {
  let agent: DesignAgent

  beforeEach(() => {
    agent = createDesignAgent('openai', 'gpt-4o-mini')
    jest.clearAllMocks()
  })

  describe('Agent Creation', () => {
    it('should create a DesignAgent instance', () => {
      expect(agent).toBeInstanceOf(DesignAgent)
    })

    it('should create agent with factory function', () => {
      const factoryAgent = createDesignAgent()
      expect(factoryAgent).toBeInstanceOf(DesignAgent)
    })
  })

  describe('Design Document Generation', () => {
    it('should generate design document from requirements', async () => {
      const { generateObject } = require('ai')
      const mockDesignDoc = {
        projectName: 'Test Project',
        overview: 'Test overview',
        architecture: {
          pattern: {
            name: 'Microservices',
            description: 'Distributed architecture',
            benefits: ['Scalability'],
            tradeoffs: ['Complexity'],
            applicability: 'Large systems',
            implementation: 'Container-based'
          },
          components: [],
          dataFlow: 'Request flow',
          deployment: 'Cloud deployment'
        },
        techStack: {
          frontend: {
            framework: 'React',
            language: 'TypeScript',
            styling: 'CSS',
            buildTool: 'Webpack',
            testing: 'Jest'
          },
          backend: {
            framework: 'Express',
            language: 'TypeScript',
            runtime: 'Node.js',
            database: 'PostgreSQL',
            authentication: 'JWT',
            testing: 'Jest'
          },
          infrastructure: {
            hosting: 'AWS',
            cicd: 'GitHub Actions',
            monitoring: 'CloudWatch'
          },
          rationale: {}
        },
        dataModel: {
          entities: [],
          schema: ''
        },
        apiDesign: {
          endpoints: [],
          authentication: {
            type: 'jwt' as const,
            implementation: 'Bearer token'
          },
          documentation: 'OpenAPI'
        },
        security: {
          authentication: 'JWT',
          authorization: 'RBAC',
          dataProtection: 'Encryption',
          vulnerabilities: []
        },
        performance: {
          requirements: [],
          optimizations: [],
          monitoring: 'APM'
        },
        testing: {
          strategy: 'TDD',
          types: ['Unit'],
          tools: ['Jest'],
          coverage: '80%'
        },
        deployment: {
          strategy: 'Blue-green',
          environments: ['dev', 'prod'],
          cicd: 'Automated',
          monitoring: 'Real-time'
        }
      }

      generateObject.mockResolvedValue({ object: mockDesignDoc })

      const requirements = [
        { id: 'REQ-001', description: 'User authentication' }
      ]

      const result = await agent.generateDesignDocument(requirements)

      expect(result).toEqual(mockDesignDoc)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'mock-model',
          system: expect.stringContaining('software architect'),
          prompt: expect.stringContaining('requirements'),
          temperature: 0.3
        })
      )
    })

    it('should handle generation errors gracefully', async () => {
      const { generateObject } = require('ai')
      generateObject.mockRejectedValue(new Error('API Error'))

      const requirements = [{ id: 'REQ-001', description: 'Test requirement' }]

      await expect(agent.generateDesignDocument(requirements))
        .rejects.toThrow('Failed to generate design document: API Error')
    })
  })

  describe('Architecture Analysis', () => {
    it('should analyze architecture patterns', async () => {
      const { generateObject } = require('ai')
      const mockAnalysis = {
        recommendedPatterns: [
          {
            name: 'Microservices',
            description: 'Distributed architecture',
            benefits: ['Scalability', 'Flexibility'],
            tradeoffs: ['Complexity'],
            applicability: 'Large systems',
            implementation: 'Container-based'
          }
        ],
        techStackOptions: [],
        scalabilityConsiderations: ['Load balancing', 'Auto-scaling'],
        securityRecommendations: ['Authentication', 'Authorization'],
        performanceConsiderations: ['Caching', 'Database optimization']
      }

      generateObject.mockResolvedValue({ object: mockAnalysis })

      const requirements = [{ id: 'REQ-001', description: 'Scalable system' }]
      const result = await agent.analyzeArchitecture(requirements)

      expect(result).toEqual(mockAnalysis)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('software architect'),
          prompt: expect.stringContaining('architectural patterns')
        })
      )
    })
  })

  describe('Technology Stack Selection', () => {
    it('should select optimal technology stack', async () => {
      const { generateObject } = require('ai')
      const mockSelection = {
        recommended: {
          frontend: {
            framework: 'React',
            language: 'TypeScript',
            styling: 'Tailwind CSS',
            buildTool: 'Vite',
            testing: 'Jest'
          },
          backend: {
            framework: 'Express',
            language: 'TypeScript',
            runtime: 'Node.js',
            database: 'PostgreSQL',
            authentication: 'JWT',
            testing: 'Jest'
          },
          infrastructure: {
            hosting: 'AWS',
            cicd: 'GitHub Actions',
            monitoring: 'CloudWatch'
          },
          rationale: {
            'React': 'Popular and well-supported',
            'TypeScript': 'Type safety'
          }
        },
        alternatives: [],
        rationale: {
          'React': 'Most popular frontend framework',
          'PostgreSQL': 'Reliable relational database'
        }
      }

      generateObject.mockResolvedValue({ object: mockSelection })

      const requirements = [{ id: 'REQ-001', description: 'Web application' }]
      const constraints = ['Must use TypeScript']

      const result = await agent.selectTechnologyStack(requirements, constraints)

      expect(result).toEqual(mockSelection)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('technology consultant'),
          prompt: expect.stringContaining('technology stack')
        })
      )
    })
  })

  describe('Component Design', () => {
    it('should design system components', async () => {
      const { generateObject } = require('ai')
      const mockComponents = [
        {
          name: 'UserService',
          type: 'service' as const,
          description: 'Handles user operations',
          responsibilities: ['User CRUD', 'Authentication'],
          interfaces: [
            {
              name: 'UserAPI',
              type: 'api' as const,
              specification: 'REST API'
            }
          ],
          dependencies: ['Database'],
          implementation: {
            language: 'TypeScript',
            framework: 'Express',
            patterns: ['Repository Pattern']
          }
        }
      ]

      generateObject.mockResolvedValue({ object: { components: mockComponents } })

      const requirements = [{ id: 'REQ-001', description: 'User management' }]
      const architecture = {
        name: 'Microservices',
        description: 'Distributed architecture',
        benefits: ['Scalability'],
        tradeoffs: ['Complexity'],
        applicability: 'Large systems',
        implementation: 'Container-based'
      }

      const result = await agent.designComponents(requirements, architecture)

      expect(result).toEqual(mockComponents)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('component design'),
          prompt: expect.stringContaining('components')
        })
      )
    })
  })

  describe('Data Model Design', () => {
    it('should design data model', async () => {
      const { generateObject } = require('ai')
      const mockDataModel = {
        entities: [
          {
            name: 'User',
            description: 'System user',
            attributes: [
              {
                name: 'id',
                type: 'UUID',
                required: true,
                description: 'Unique identifier'
              }
            ],
            relationships: [],
            indexes: ['id']
          }
        ],
        schema: 'CREATE TABLE users (id UUID PRIMARY KEY);'
      }

      generateObject.mockResolvedValue({ object: mockDataModel })

      const requirements = [{ id: 'REQ-001', description: 'User data storage' }]
      const techStack = {
        frontend: {
          framework: 'React',
          language: 'TypeScript',
          styling: 'CSS',
          buildTool: 'Webpack',
          testing: 'Jest'
        },
        backend: {
          framework: 'Express',
          language: 'TypeScript',
          runtime: 'Node.js',
          database: 'PostgreSQL',
          authentication: 'JWT',
          testing: 'Jest'
        },
        infrastructure: {
          hosting: 'AWS',
          cicd: 'GitHub Actions',
          monitoring: 'CloudWatch'
        },
        rationale: {}
      }

      const result = await agent.designDataModel(requirements, techStack)

      expect(result).toEqual(mockDataModel)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('database architect'),
          prompt: expect.stringContaining('data model')
        })
      )
    })
  })

  describe('API Design', () => {
    it('should design API specifications', async () => {
      const { generateObject } = require('ai')
      const mockAPIDesign = {
        endpoints: [
          {
            path: '/api/users',
            method: 'GET' as const,
            description: 'Get users',
            parameters: [],
            responses: [
              {
                status: 200,
                description: 'Success',
                schema: 'Array<User>'
              }
            ],
            authentication: true
          }
        ],
        authentication: {
          type: 'jwt' as const,
          implementation: 'Bearer token'
        },
        documentation: 'OpenAPI 3.0'
      }

      generateObject.mockResolvedValue({ object: mockAPIDesign })

      const requirements = [{ id: 'REQ-001', description: 'User API' }]
      const components = []
      const dataModel = { entities: [], schema: '' }

      const result = await agent.designAPI(requirements, components, dataModel)

      expect(result).toEqual(mockAPIDesign)
      expect(generateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('API architect'),
          prompt: expect.stringContaining('API specifications')
        })
      )
    })
  })

  describe('Markdown Generation', () => {
    it('should generate markdown documentation', async () => {
      const { generateText } = require('ai')
      const mockMarkdown = '# Design Document\n\nThis is a test design document.'

      generateText.mockResolvedValue({ text: mockMarkdown })

      const designDoc = {
        projectName: 'Test Project',
        overview: 'Test overview'
      } as any

      const result = await agent.generateMarkdownDocument(designDoc)

      expect(result).toBe(mockMarkdown)
      expect(generateText).toHaveBeenCalledWith(
        expect.objectContaining({
          system: expect.stringContaining('technical writer'),
          prompt: expect.stringContaining('markdown format')
        })
      )
    })
  })
})

describe('DesignUtils', () => {
  describe('validateDesignCompleteness', () => {
    it('should validate complete design document', () => {
      const completeDesign = {
        projectName: 'Test',
        overview: 'Complete overview',
        architecture: {
          pattern: { name: 'Microservices' },
          components: [{ name: 'TestComponent' }],
          dataFlow: 'Flow',
          deployment: 'Deployment'
        },
        techStack: {
          frontend: { framework: 'React' },
          backend: { framework: 'Express' },
          infrastructure: { hosting: 'AWS' }
        },
        dataModel: {
          entities: [{ name: 'User' }]
        },
        apiDesign: {
          endpoints: [{ path: '/api/test' }]
        },
        security: {
          authentication: 'JWT'
        }
      } as any

      const result = DesignUtils.validateDesignCompleteness(completeDesign)

      expect(result.isComplete).toBe(true)
      expect(result.missingElements).toHaveLength(0)
      expect(result.completenessScore).toBe(100)
    })

    it('should identify missing elements', () => {
      const incompleteDesign = {
        projectName: 'Test',
        architecture: {
          pattern: {},
          components: [],
          dataFlow: '',
          deployment: ''
        },
        techStack: {
          frontend: {},
          backend: {},
          infrastructure: {}
        },
        dataModel: {
          entities: []
        },
        apiDesign: {
          endpoints: []
        },
        security: {}
      } as any

      const result = DesignUtils.validateDesignCompleteness(incompleteDesign)

      expect(result.isComplete).toBe(false)
      expect(result.missingElements.length).toBeGreaterThan(0)
      expect(result.completenessScore).toBeLessThan(100)
    })
  })

  describe('extractTechnologyDependencies', () => {
    it('should extract technology dependencies', () => {
      const design = {
        techStack: {
          frontend: {
            framework: 'React',
            language: 'TypeScript',
            styling: 'Tailwind CSS',
            buildTool: 'Vite',
            testing: 'Jest'
          },
          backend: {
            framework: 'Express',
            language: 'TypeScript',
            runtime: 'Node.js',
            database: 'PostgreSQL',
            orm: 'Prisma',
            authentication: 'JWT',
            testing: 'Jest'
          },
          infrastructure: {
            hosting: 'AWS',
            containerization: 'Docker',
            cicd: 'GitHub Actions',
            monitoring: 'CloudWatch',
            caching: 'Redis'
          }
        }
      } as any

      const result = DesignUtils.extractTechnologyDependencies(design)

      expect(result.frontend).toContain('React')
      expect(result.frontend).toContain('TypeScript')
      expect(result.backend).toContain('Express')
      expect(result.backend).toContain('Node.js')
      expect(result.database).toContain('PostgreSQL')
      expect(result.database).toContain('Prisma')
      expect(result.infrastructure).toContain('AWS')
      expect(result.infrastructure).toContain('Docker')
    })
  })

  describe('generateComponentDependencyGraph', () => {
    it('should generate component dependency graph', () => {
      const components = [
        {
          name: 'UserService',
          type: 'service' as const,
          dependencies: ['Database', 'AuthService']
        },
        {
          name: 'AuthService',
          type: 'service' as const,
          dependencies: ['Database']
        }
      ] as any

      const result = DesignUtils.generateComponentDependencyGraph(components)

      expect(result.nodes).toHaveLength(2)
      expect(result.nodes[0]).toEqual({
        id: 'UserService',
        label: 'UserService',
        type: 'service'
      })
      expect(result.edges).toHaveLength(3)
      expect(result.edges).toContainEqual({
        from: 'UserService',
        to: 'Database',
        label: 'depends on'
      })
    })
  })
})