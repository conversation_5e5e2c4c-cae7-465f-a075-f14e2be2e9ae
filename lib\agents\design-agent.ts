import { generateText, generateObject } from 'ai'
import { getAIModel } from '../ai-config'
import { z } from 'zod'

/**
 * DesignAgent - Autonomous Design Generation Agent
 * 
 * This agent generates comprehensive design documents from requirements using:
 * - OpenRouter AI models for intelligent architectural analysis and design generation
 * - Architectural pattern recognition and recommendation
 * - Technology stack selection based on requirements analysis
 * - Component and interface design generation
 * - Data model and API design capabilities
 * 
 * Capabilities:
 * - Generate comprehensive design documents from requirements
 * - Recommend architectural patterns based on requirements analysis
 * - Select optimal technology stacks for project requirements
 * - Design system components and interfaces
 * - Create data models and API specifications
 * - Generate professional markdown documentation with diagrams
 */

// Technology Stack Configuration
const TechStackSchema = z.object({
  frontend: z.object({
    framework: z.string(),
    language: z.string(),
    stateManagement: z.string().optional(),
    styling: z.string(),
    buildTool: z.string(),
    testing: z.string()
  }),
  backend: z.object({
    framework: z.string(),
    language: z.string(),
    runtime: z.string(),
    database: z.string(),
    orm: z.string().optional(),
    authentication: z.string(),
    testing: z.string()
  }),
  infrastructure: z.object({
    hosting: z.string(),
    containerization: z.string().optional(),
    cicd: z.string(),
    monitoring: z.string(),
    caching: z.string().optional()
  }),
  rationale: z.record(z.string())
})

// Architectural Pattern Schema
const ArchitecturalPatternSchema = z.object({
  name: z.string(),
  description: z.string(),
  benefits: z.array(z.string()),
  tradeoffs: z.array(z.string()),
  applicability: z.string(),
  implementation: z.string()
})

// Component Design Schema
const ComponentSchema = z.object({
  name: z.string(),
  type: z.enum(['ui', 'service', 'utility', 'data', 'integration']),
  description: z.string(),
  responsibilities: z.array(z.string()),
  interfaces: z.array(z.object({
    name: z.string(),
    type: z.enum(['api', 'event', 'data', 'ui']),
    specification: z.string()
  })),
  dependencies: z.array(z.string()),
  implementation: z.object({
    language: z.string(),
    framework: z.string().optional(),
    patterns: z.array(z.string())
  })
})

// Data Model Schema
const DataModelSchema = z.object({
  entities: z.array(z.object({
    name: z.string(),
    description: z.string(),
    attributes: z.array(z.object({
      name: z.string(),
      type: z.string(),
      required: z.boolean(),
      description: z.string(),
      constraints: z.array(z.string()).optional()
    })),
    relationships: z.array(z.object({
      type: z.enum(['one-to-one', 'one-to-many', 'many-to-many']),
      target: z.string(),
      description: z.string()
    })),
    indexes: z.array(z.string()).optional()
  })),
  schema: z.string()
})

// API Design Schema
const APIDesignSchema = z.object({
  endpoints: z.array(z.object({
    path: z.string(),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
    description: z.string(),
    parameters: z.array(z.object({
      name: z.string(),
      type: z.string(),
      location: z.enum(['path', 'query', 'body', 'header']),
      required: z.boolean(),
      description: z.string()
    })),
    responses: z.array(z.object({
      status: z.number(),
      description: z.string(),
      schema: z.string()
    })),
    authentication: z.boolean(),
    rateLimit: z.string().optional()
  })),
  authentication: z.object({
    type: z.enum(['jwt', 'oauth', 'api-key', 'session']),
    implementation: z.string()
  }),
  documentation: z.string()
})

// UI/UX Design Schema
const UIPageSchema = z.object({
  name: z.string(),
  route: z.string(),
  description: z.string(),
  pageType: z.enum(['landing', 'dashboard', 'form', 'list', 'detail', 'auth', 'settings', 'error']),
  layout: z.object({
    type: z.enum(['single-column', 'two-column', 'three-column', 'grid', 'sidebar', 'header-footer']),
    sections: z.array(z.string())
  }),
  components: z.array(z.object({
    name: z.string(),
    type: z.enum(['header', 'navigation', 'form', 'table', 'card', 'button', 'modal', 'sidebar', 'footer', 'chart', 'list']),
    position: z.string(),
    properties: z.record(z.string())
  })),
  wireframe: z.string(),
  userFlows: z.array(z.string()),
  responsiveBreakpoints: z.array(z.string())
})

const UIDesignSchema = z.object({
  pages: z.array(UIPageSchema),
  designSystem: z.object({
    colorPalette: z.record(z.string()),
    typography: z.object({
      headings: z.record(z.string()),
      body: z.string(),
      sizes: z.record(z.string())
    }),
    spacing: z.record(z.string()),
    components: z.array(z.object({
      name: z.string(),
      description: z.string(),
      variants: z.array(z.string())
    }))
  }),
  navigation: z.object({
    type: z.enum(['top-nav', 'side-nav', 'bottom-nav', 'breadcrumb', 'tabs']),
    structure: z.array(z.object({
      label: z.string(),
      route: z.string(),
      children: z.array(z.string()).optional()
    }))
  }),
  userJourney: z.array(z.object({
    step: z.string(),
    page: z.string(),
    action: z.string(),
    outcome: z.string()
  }))
})

// Complete Design Document Schema
const DesignDocumentSchema = z.object({
  projectName: z.string(),
  overview: z.string(),
  architecture: z.object({
    pattern: ArchitecturalPatternSchema,
    components: z.array(ComponentSchema),
    dataFlow: z.string(),
    deployment: z.string()
  }),
  techStack: TechStackSchema,
  dataModel: DataModelSchema,
  apiDesign: APIDesignSchema,
  uiDesign: UIDesignSchema,
  security: z.object({
    authentication: z.string(),
    authorization: z.string(),
    dataProtection: z.string(),
    vulnerabilities: z.array(z.string())
  }),
  performance: z.object({
    requirements: z.array(z.string()),
    optimizations: z.array(z.string()),
    monitoring: z.string()
  }),
  testing: z.object({
    strategy: z.string(),
    types: z.array(z.string()),
    tools: z.array(z.string()),
    coverage: z.string()
  }),
  deployment: z.object({
    strategy: z.string(),
    environments: z.array(z.string()),
    cicd: z.string(),
    monitoring: z.string()
  })
})

export type TechStack = z.infer<typeof TechStackSchema>
export type ArchitecturalPattern = z.infer<typeof ArchitecturalPatternSchema>
export type Component = z.infer<typeof ComponentSchema>
export type DataModel = z.infer<typeof DataModelSchema>
export type APIDesign = z.infer<typeof APIDesignSchema>
export type UIPage = z.infer<typeof UIPageSchema>
export type UIDesign = z.infer<typeof UIDesignSchema>
export type DesignDocument = z.infer<typeof DesignDocumentSchema>

export interface DesignGenerationOptions {
  provider?: string
  model?: string
  includeDetailedSpecs?: boolean
  architecturalStyle?: 'microservices' | 'monolithic' | 'serverless' | 'hybrid'
  scalabilityRequirements?: 'low' | 'medium' | 'high' | 'enterprise'
  securityLevel?: 'basic' | 'standard' | 'high' | 'enterprise'
  existingConstraints?: string[]
}

export interface ArchitecturalAnalysis {
  recommendedPatterns: ArchitecturalPattern[]
  techStackOptions: TechStack[]
  scalabilityConsiderations: string[]
  securityRecommendations: string[]
  performanceConsiderations: string[]
}

export class DesignAgent {
  private aiModel: any
  private provider: string
  private modelName: string

  constructor(provider: string = 'openrouter', model: string = 'kimi-k2') {
    this.provider = provider
    this.modelName = model
    this.aiModel = getAIModel(provider, model)
  }

  /**
   * Generate comprehensive design document from requirements
   */
  async generateDesignDocument(
    requirements: any[],
    options: DesignGenerationOptions = {}
  ): Promise<DesignDocument> {
    const {
      includeDetailedSpecs = true,
      architecturalStyle = 'hybrid',
      scalabilityRequirements = 'medium',
      securityLevel = 'standard',
      existingConstraints = []
    } = options

    const systemPrompt = this.buildDesignGenerationPrompt(
      includeDetailedSpecs,
      architecturalStyle,
      scalabilityRequirements,
      securityLevel,
      existingConstraints
    )

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Generate a comprehensive design document based on the following requirements:\n\n${JSON.stringify(requirements, null, 2)}`,
        schema: DesignDocumentSchema,
        temperature: 0.3
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to generate design document: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Analyze requirements and recommend architectural patterns
   */
  async analyzeArchitecture(requirements: any[]): Promise<ArchitecturalAnalysis> {
    const analysisSchema = z.object({
      recommendedPatterns: z.array(ArchitecturalPatternSchema),
      techStackOptions: z.array(TechStackSchema),
      scalabilityConsiderations: z.array(z.string()),
      securityRecommendations: z.array(z.string()),
      performanceConsiderations: z.array(z.string())
    })

    const systemPrompt = `You are a senior software architect with expertise in system design and architectural patterns.
    Your task is to analyze requirements and recommend appropriate architectural patterns and technology stacks.

    Consider the following architectural patterns and their applicability:
    - Microservices Architecture: For scalable, distributed systems
    - Monolithic Architecture: For simpler, cohesive applications
    - Serverless Architecture: For event-driven, auto-scaling applications
    - Event-Driven Architecture: For loosely coupled, reactive systems
    - Layered Architecture: For structured, maintainable applications
    - Hexagonal Architecture: For testable, adaptable systems
    - CQRS: For complex read/write operations
    - Event Sourcing: For audit trails and temporal queries

    Evaluate:
    1. System complexity and scale requirements
    2. Performance and scalability needs
    3. Security and compliance requirements
    4. Team size and expertise
    5. Deployment and operational constraints
    6. Integration requirements
    7. Data consistency needs
    8. Development timeline and budget

    Provide multiple architectural options with clear rationale for each recommendation.`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Analyze the following requirements and recommend architectural patterns:\n\n${JSON.stringify(requirements, null, 2)}`,
        schema: analysisSchema,
        temperature: 0.2
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to analyze architecture: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Select optimal technology stack based on requirements
   */
  async selectTechnologyStack(
    requirements: any[],
    constraints: string[] = []
  ): Promise<{
    recommended: TechStack
    alternatives: TechStack[]
    rationale: Record<string, string>
  }> {
    const selectionSchema = z.object({
      recommended: TechStackSchema,
      alternatives: z.array(TechStackSchema),
      rationale: z.record(z.string())
    })

    const systemPrompt = `You are a technology consultant specializing in technology stack selection.
    Your task is to recommend optimal technology stacks based on project requirements and constraints.

    Consider these factors:
    1. Project requirements and complexity
    2. Team expertise and learning curve
    3. Performance and scalability needs
    4. Development speed and productivity
    5. Long-term maintenance and support
    6. Community and ecosystem maturity
    7. Cost and licensing considerations
    8. Integration capabilities
    9. Security and compliance requirements
    10. Deployment and operational requirements

    Popular technology combinations:
    
    Traditional Full-Stack Combinations:
    - React + Node.js + PostgreSQL (Full-stack JavaScript)
    - Next.js + Prisma + PostgreSQL (Modern React stack)
    - Vue.js + Express + MongoDB (MEVN stack)
    - Angular + .NET + SQL Server (Enterprise stack)
    - Python Django + PostgreSQL (Rapid development)
    - Java Spring Boot + MySQL (Enterprise Java)
    - Go + PostgreSQL (High performance)
    - Rust + PostgreSQL (Systems programming)

    Agentic & AI-Powered Stacks:
    The landscape of AI, particularly agentic solutions, has given rise to new and powerful technology combinations. 
    These stacks are designed to build applications that can reason, act, and interact with their environment 
    in a more autonomous fashion.

    - LangChain + FastAPI + Supabase (pgvector) - Agentic Backend Focus
      * LangChain provides core framework for orchestrating interactions between language models
      * FastAPI creates robust and scalable APIs for agentic logic
      * Pydantic ensures well-structured and validated data flow
      * Supabase with pgvector extension for vector embeddings and semantic search
      * Ideal for: RAG applications, semantic search, AI-powered backends

    - Next.js + LangChain.js + Supabase - Full-Stack TypeScript Agentic Applications
      * Next.js for UI and server-side rendering
      * LangChain.js for agentic workflows in TypeScript
      * Supabase as backend-as-a-service with database, auth, and serverless functions
      * Ideal for: Interactive chat applications, RAG interfaces, AI-driven user experiences

    - LangGraph + Pydantic + Streamlit - Complex Agentic Workflows & Prototyping
      * LangGraph for complex, stateful, multi-agent systems as graphs
      * Pydantic for state object validation between workflow nodes
      * Streamlit for interactive UIs to visualize complex agentic systems
      * Ideal for: Multi-agent systems, human-in-the-loop workflows, AI prototyping

    - Python (Pydantic models) + Instructor + OpenAI API - Reliable & Structured LLM Outputs
      * Pydantic models define desired output schemas
      * Instructor library ensures structured data extraction from LLM responses
      * OpenAI API for language model capabilities
      * Ideal for: Structured data extraction, content classification, reliable AI outputs

    - Vercel AI SDK + OpenRouter + Next.js - Modern AI Application Development
      * Vercel AI SDK for unified AI provider integration
      * OpenRouter for access to multiple AI models
      * Next.js for full-stack React applications
      * Ideal for: AI-powered web applications, chatbots, content generation

    - CrewAI + FastAPI + PostgreSQL - Multi-Agent Orchestration
      * CrewAI for coordinating multiple specialized AI agents
      * FastAPI for high-performance API endpoints
      * PostgreSQL for reliable data persistence
      * Ideal for: Complex multi-agent workflows, automated business processes

    Consider the specific AI/ML requirements when selecting stacks:
    - Vector databases for semantic search (Pinecone, Weaviate, Supabase pgvector)
    - Model serving platforms (Hugging Face, Replicate, OpenAI)
    - Orchestration frameworks (LangChain, LlamaIndex, Haystack)
    - Monitoring and observability (LangSmith, Weights & Biases, MLflow)

    Provide detailed rationale for each technology choice and consider alternatives.`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Select optimal technology stack for the following requirements and constraints:
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Constraints:
        ${constraints.join('\n')}`,
        schema: selectionSchema,
        temperature: 0.2
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to select technology stack: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Design system components and interfaces
   */
  async designComponents(
    requirements: any[],
    architecture: ArchitecturalPattern
  ): Promise<Component[]> {
    const systemPrompt = `You are a software architect specializing in component design and system decomposition.
    Your task is to design system components based on requirements and architectural patterns.

    Component design principles:
    1. Single Responsibility Principle
    2. High Cohesion, Low Coupling
    3. Interface Segregation
    4. Dependency Inversion
    5. Open/Closed Principle

    Component types:
    - UI Components: User interface elements and pages
    - Service Components: Business logic and application services
    - Utility Components: Shared utilities and helpers
    - Data Components: Data access and persistence layers
    - Integration Components: External system integrations

    For each component, define:
    - Clear responsibilities and boundaries
    - Well-defined interfaces (APIs, events, data contracts)
    - Dependencies and relationships
    - Implementation approach and patterns

    Consider the architectural pattern: ${architecture.name}
    Implementation approach: ${architecture.implementation}`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Design system components based on the following requirements and architecture:
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Architecture:
        ${JSON.stringify(architecture, null, 2)}`,
        schema: z.object({
          components: z.array(ComponentSchema)
        }),
        temperature: 0.3
      })

      return result.object.components
    } catch (error) {
      throw new Error(`Failed to design components: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Create data model design
   */
  async designDataModel(
    requirements: any[],
    techStack: TechStack
  ): Promise<DataModel> {
    const systemPrompt = `You are a database architect specializing in data modeling and database design.
    Your task is to design a comprehensive data model based on requirements and technology stack.

    Data modeling principles:
    1. Normalization for data integrity
    2. Denormalization for performance where needed
    3. Proper indexing strategy
    4. Referential integrity
    5. Data validation and constraints
    6. Scalability considerations

    Consider the database technology: ${techStack.backend.database}
    ORM/Data layer: ${techStack.backend.orm || 'Native queries'}

    For each entity, define:
    - Attributes with proper data types
    - Validation rules and constraints
    - Relationships with other entities
    - Indexing strategy for performance
    - Business rules and invariants

    Generate appropriate schema syntax for the target database.`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Design a data model based on the following requirements and technology stack:
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Technology Stack:
        ${JSON.stringify(techStack, null, 2)}`,
        schema: DataModelSchema,
        temperature: 0.2
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to design data model: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Design API specifications
   */
  async designAPI(
    requirements: any[],
    components: Component[],
    dataModel: DataModel
  ): Promise<APIDesign> {
    const systemPrompt = `You are an API architect specializing in RESTful API design and API specifications.
    Your task is to design comprehensive API specifications based on requirements, components, and data model.

    API design principles:
    1. RESTful design patterns
    2. Consistent naming conventions
    3. Proper HTTP methods and status codes
    4. Resource-oriented URLs
    5. Stateless design
    6. Proper error handling
    7. Authentication and authorization
    8. Rate limiting and throttling
    9. Versioning strategy
    10. Documentation and discoverability

    For each endpoint, define:
    - Clear resource paths and HTTP methods
    - Request/response schemas
    - Authentication requirements
    - Error responses and status codes
    - Rate limiting policies
    - Documentation and examples

    Consider the components and data model for API design consistency.`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Design API specifications based on the following:
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Components:
        ${JSON.stringify(components, null, 2)}
        
        Data Model:
        ${JSON.stringify(dataModel, null, 2)}`,
        schema: APIDesignSchema,
        temperature: 0.2
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to design API: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Design UI/UX and generate wireframes with Mermaid diagrams
   */
  async designUI(
    requirements: any[],
    components: Component[],
    apiDesign: APIDesign
  ): Promise<UIDesign> {
    const systemPrompt = `You are a UX/UI architect specializing in user interface design and user experience optimization.
    Your task is to design comprehensive UI/UX specifications including wireframes using Mermaid ASCII diagrams.

    UI/UX Design Principles:
    1. User-centered design approach
    2. Intuitive navigation and information architecture
    3. Responsive design for multiple devices
    4. Accessibility and inclusive design
    5. Consistent design system and patterns
    6. Performance-optimized interfaces
    7. Clear visual hierarchy and typography
    8. Effective use of whitespace and layout
    9. Interactive feedback and micro-interactions
    10. Mobile-first responsive design

    For each page, create:
    1. **Page Structure**: Define layout type and sections
    2. **Component Placement**: Position UI components logically
    3. **Mermaid Wireframe**: Generate ASCII wireframe using Mermaid flowchart syntax
    4. **User Flows**: Define user interaction patterns
    5. **Responsive Breakpoints**: Specify mobile, tablet, desktop layouts

    **Mermaid Wireframe Guidelines:**
    - Use flowchart syntax with rectangular nodes for UI elements
    - Structure: Header → Navigation → Main Content → Footer
    - Include component types: forms, tables, cards, buttons, modals
    - Show relationships between UI elements
    - Use descriptive labels for each UI component
    - Represent user flow with arrows and connections

    **Example Mermaid Wireframe Structure:**
    \`\`\`mermaid
    flowchart TD
        A[Header - Logo & User Menu] --> B[Navigation Bar]
        B --> C[Main Content Area]
        C --> D[Sidebar - Filters/Actions]
        C --> E[Content Grid/List]
        E --> F[Pagination/Load More]
        G[Footer - Links & Info] --> H[End]
        
        subgraph "Main Content"
            E --> I[Card Component]
            I --> J[Title & Description]
            I --> K[Action Buttons]
        end
    \`\`\`

    Consider the API endpoints and components for consistent UI design.`

    try {
      const result = await generateObject({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Design UI/UX specifications with Mermaid wireframes based on the following:
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Components:
        ${JSON.stringify(components, null, 2)}
        
        API Design:
        ${JSON.stringify(apiDesign, null, 2)}`,
        schema: UIDesignSchema,
        temperature: 0.3
      })

      return result.object
    } catch (error) {
      throw new Error(`Failed to design UI: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate individual page wireframe with Mermaid
   */
  async generatePageWireframe(
    pageName: string,
    pageType: string,
    requirements: any[],
    components: Component[]
  ): Promise<string> {
    const systemPrompt = `You are a UX designer specializing in wireframe creation using Mermaid diagrams.
    Your task is to generate a detailed Mermaid wireframe for a specific page.

    **Mermaid Wireframe Best Practices:**
    1. Use flowchart TD (top-down) or LR (left-right) syntax
    2. Create clear visual hierarchy with proper node shapes
    3. Use rectangles [Text] for UI components
    4. Use rounded rectangles (Text) for buttons and interactive elements
    5. Use diamonds {Decision} for conditional UI elements
    6. Connect related elements with arrows
    7. Group related components using subgraphs
    8. Include responsive considerations
    9. Show user interaction flows
    10. Label components clearly and descriptively

    **Page Type Guidelines:**
    - **Landing**: Hero section, features, CTA, testimonials, footer
    - **Dashboard**: Header, sidebar nav, main content grid, widgets, notifications
    - **Form**: Header, form fields, validation, submit/cancel buttons, progress
    - **List**: Header, filters/search, data table/grid, pagination, actions
    - **Detail**: Header, breadcrumb, main content, sidebar info, related items
    - **Auth**: Logo, form fields, social login, forgot password, footer
    - **Settings**: Sidebar nav, content sections, form controls, save/cancel
    - **Error**: Error message, illustration, action buttons, navigation

    Generate a comprehensive Mermaid wireframe that shows:
    - Complete page layout structure
    - All major UI components and their relationships
    - User interaction flows and navigation paths
    - Responsive design considerations
    - Component hierarchy and grouping`

    try {
      const result = await generateText({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Generate a detailed Mermaid wireframe for the following page:
        
        Page Name: ${pageName}
        Page Type: ${pageType}
        
        Requirements:
        ${JSON.stringify(requirements, null, 2)}
        
        Available Components:
        ${JSON.stringify(components, null, 2)}
        
        Return only the Mermaid diagram code wrapped in \`\`\`mermaid code blocks.`,
        temperature: 0.2
      })

      return result.text
    } catch (error) {
      throw new Error(`Failed to generate page wireframe: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate complete UI design with wireframes for all pages
   */
  async generateCompleteUIDesign(
    requirements: any[],
    components: Component[],
    apiDesign: APIDesign
  ): Promise<{
    uiDesign: UIDesign
    wireframes: Record<string, string>
    userJourneyDiagram: string
  }> {
    try {
      // Step 1: Generate UI design specifications
      const uiDesign = await this.designUI(requirements, components, apiDesign)

      // Step 2: Generate individual wireframes for each page
      const wireframes: Record<string, string> = {}
      for (const page of uiDesign.pages) {
        const wireframe = await this.generatePageWireframe(
          page.name,
          page.pageType,
          requirements,
          components
        )
        wireframes[page.name] = wireframe
      }

      // Step 3: Generate user journey diagram
      const userJourneyDiagram = await this.generateUserJourneyDiagram(uiDesign.userJourney)

      return {
        uiDesign,
        wireframes,
        userJourneyDiagram
      }
    } catch (error) {
      throw new Error(`Failed to generate complete UI design: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate user journey diagram with Mermaid
   */
  async generateUserJourneyDiagram(userJourney: any[]): Promise<string> {
    const systemPrompt = `You are a UX designer specializing in user journey mapping using Mermaid diagrams.
    Your task is to create a comprehensive user journey diagram showing the complete user flow.

    **User Journey Diagram Guidelines:**
    1. Use flowchart syntax to show user progression
    2. Include decision points and alternative paths
    3. Show page transitions and user actions
    4. Include success and error states
    5. Use different node shapes for different types of interactions:
       - Rectangles [Page/Screen] for pages
       - Diamonds {Decision?} for decision points
       - Rounded rectangles (Action) for user actions
       - Circles ((State)) for system states
    6. Connect steps with labeled arrows showing user actions
    7. Include parallel flows where applicable
    8. Show entry and exit points clearly

    Create a comprehensive user journey that shows:
    - User entry points and onboarding
    - Main user flows and interactions
    - Decision points and branching paths
    - Success scenarios and completion states
    - Error handling and recovery paths
    - Exit points and user retention loops`

    try {
      const result = await generateText({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Generate a comprehensive user journey diagram using Mermaid syntax based on the following user journey steps:
        
        User Journey:
        ${JSON.stringify(userJourney, null, 2)}
        
        Return only the Mermaid diagram code wrapped in \`\`\`mermaid code blocks.`,
        temperature: 0.2
      })

      return result.text
    } catch (error) {
      throw new Error(`Failed to generate user journey diagram: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate design document in markdown format
   */
  async generateMarkdownDocument(designDoc: DesignDocument): Promise<string> {
    const systemPrompt = `You are a technical writer specializing in software architecture documentation.
    Generate a comprehensive design document in markdown format from the provided design data.

    The document should include:
    1. Project overview and objectives
    2. Architecture overview with diagrams (use Mermaid syntax)
    3. Technology stack rationale
    4. Component design and interfaces
    5. Data model with entity relationships
    6. API specifications and endpoints
    7. Security considerations
    8. Performance requirements
    9. Testing strategy
    10. Deployment architecture

    Use proper markdown formatting with:
    - Clear headers and sections
    - Code blocks for technical specifications
    - Mermaid diagrams for architecture visualization
    - Tables for structured data
    - Lists for requirements and features

    Make the document professional, comprehensive, and easy to follow.`

    try {
      const result = await generateText({
        model: this.aiModel,
        system: systemPrompt,
        prompt: `Generate a comprehensive design document in markdown format:\n\n${JSON.stringify(designDoc, null, 2)}`,
        temperature: 0.1
      })

      return result.text
    } catch (error) {
      throw new Error(`Failed to generate markdown document: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Perform comprehensive design analysis with validation
   */
  async performDesignAnalysis(
    requirements: any[],
    options: DesignGenerationOptions = {}
  ): Promise<{
    designDocument: DesignDocument
    architecturalAnalysis: ArchitecturalAnalysis
    recommendations: string[]
    qualityScore: number
    wireframes?: Record<string, string>
    userJourneyDiagram?: string
  }> {
    try {
      // Step 1: Analyze architecture options
      const architecturalAnalysis = await this.analyzeArchitecture(requirements)

      // Step 2: Select optimal technology stack
      const techStackSelection = await this.selectTechnologyStack(
        requirements,
        options.existingConstraints
      )

      // Step 3: Generate comprehensive design document
      const designDocument = await this.generateDesignDocument(requirements, {
        ...options,
        architecturalStyle: this.determineArchitecturalStyle(architecturalAnalysis.recommendedPatterns[0])
      })

      // Step 4: Generate UI wireframes if UI design is included
      let wireframes: Record<string, string> | undefined
      let userJourneyDiagram: string | undefined
      
      if (designDocument.uiDesign && designDocument.uiDesign.pages.length > 0) {
        const uiDesignResult = await this.generateCompleteUIDesign(
          requirements,
          designDocument.architecture.components,
          designDocument.apiDesign
        )
        wireframes = uiDesignResult.wireframes
        userJourneyDiagram = uiDesignResult.userJourneyDiagram
      }

      // Step 5: Validate design quality
      const qualityScore = this.calculateDesignQuality(designDocument)

      // Step 6: Generate recommendations
      const recommendations = this.generateDesignRecommendations(
        designDocument,
        architecturalAnalysis,
        qualityScore
      )

      return {
        designDocument,
        architecturalAnalysis,
        recommendations,
        qualityScore,
        wireframes,
        userJourneyDiagram
      }
    } catch (error) {
      throw new Error(`Failed to perform design analysis: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private buildDesignGenerationPrompt(
    includeDetailedSpecs: boolean,
    architecturalStyle: string,
    scalabilityRequirements: string,
    securityLevel: string,
    existingConstraints: string[]
  ): string {
    return `You are a senior software architect with expertise in system design, architectural patterns, and technology selection.
    Your task is to generate comprehensive design documents based on requirements analysis.

    Design Parameters:
    - Architectural Style: ${architecturalStyle}
    - Scalability Requirements: ${scalabilityRequirements}
    - Security Level: ${securityLevel}
    - Include Detailed Specifications: ${includeDetailedSpecs}
    ${existingConstraints.length > 0 ? `- Existing Constraints: ${existingConstraints.join(', ')}` : ''}

    Generate a complete design document that includes:

    1. **Architecture Overview**
       - Recommended architectural pattern with rationale
       - System components and their relationships
       - Data flow and communication patterns
       - Deployment architecture

    2. **Technology Stack Selection**
       - Frontend framework and tools
       - Backend framework and runtime
       - Database and data storage
       - Infrastructure and deployment tools
       - Detailed rationale for each choice

    3. **Component Design**
       - System decomposition into components
       - Component responsibilities and interfaces
       - Dependencies and relationships
       - Implementation patterns

    4. **Data Model Design**
       - Entity definitions and relationships
       - Database schema design
       - Data validation and constraints
       - Indexing and performance considerations

    5. **API Design**
       - RESTful endpoint specifications
       - Request/response schemas
       - Authentication and authorization
       - Error handling and status codes

    6. **Security Architecture**
       - Authentication and authorization strategy
       - Data protection and encryption
       - Security vulnerabilities and mitigations
       - Compliance considerations

    7. **Performance and Scalability**
       - Performance requirements and targets
       - Scalability strategies and optimizations
       - Monitoring and observability
       - Caching and optimization techniques

    8. **Testing Strategy**
       - Testing approach and methodology
       - Test types and coverage requirements
       - Testing tools and frameworks
       - Quality assurance processes

    9. **Deployment Strategy**
       - Deployment environments and pipeline
       - CI/CD implementation
       - Infrastructure as code
       - Monitoring and alerting

    Ensure the design is:
    - Aligned with requirements and constraints
    - Scalable and maintainable
    - Secure and compliant
    - Testable and observable
    - Cost-effective and practical
    - Following industry best practices`
  }

  private determineArchitecturalStyle(pattern: ArchitecturalPattern): 'microservices' | 'monolithic' | 'serverless' | 'hybrid' {
    const name = pattern.name.toLowerCase()
    if (name.includes('microservice')) return 'microservices'
    if (name.includes('serverless')) return 'serverless'
    if (name.includes('monolith')) return 'monolithic'
    return 'hybrid'
  }

  private calculateDesignQuality(design: DesignDocument): number {
    let score = 0
    const maxScore = 100

    // Architecture completeness (20 points)
    if (design.architecture.pattern.name) score += 5
    if (design.architecture.components.length > 0) score += 5
    if (design.architecture.dataFlow) score += 5
    if (design.architecture.deployment) score += 5

    // Technology stack completeness (20 points)
    if (design.techStack.frontend.framework) score += 5
    if (design.techStack.backend.framework) score += 5
    if (design.techStack.backend.database) score += 5
    if (design.techStack.infrastructure.hosting) score += 5

    // Data model quality (20 points)
    if (design.dataModel.entities.length > 0) score += 10
    if (design.dataModel.schema) score += 10

    // API design quality (20 points)
    if (design.apiDesign.endpoints.length > 0) score += 10
    if (design.apiDesign.authentication.type) score += 10

    // Security considerations (10 points)
    if (design.security.authentication) score += 3
    if (design.security.authorization) score += 3
    if (design.security.dataProtection) score += 4

    // Testing and deployment (10 points)
    if (design.testing.strategy) score += 5
    if (design.deployment.strategy) score += 5

    return Math.min(score, maxScore)
  }

  private generateDesignRecommendations(
    design: DesignDocument,
    analysis: ArchitecturalAnalysis,
    qualityScore: number
  ): string[] {
    const recommendations: string[] = []

    if (qualityScore < 70) {
      recommendations.push('Consider adding more detailed component specifications')
    }

    if (design.architecture.components.length < 3) {
      recommendations.push('System may benefit from better component decomposition')
    }

    if (design.dataModel.entities.length === 0) {
      recommendations.push('Data model needs to be defined with proper entities and relationships')
    }

    if (design.apiDesign.endpoints.length === 0) {
      recommendations.push('API endpoints should be specified for system interfaces')
    }

    if (!design.security.authentication) {
      recommendations.push('Authentication strategy should be clearly defined')
    }

    if (analysis.scalabilityConsiderations.length > 0) {
      recommendations.push('Review scalability considerations and implement recommended optimizations')
    }

    if (analysis.securityRecommendations.length > 0) {
      recommendations.push('Implement recommended security measures for enhanced protection')
    }

    return recommendations
  }
}

// Factory function for creating DesignAgent instances
export function createDesignAgent(
  provider: string = 'openrouter',
  model: string = 'kimi-k2'
): DesignAgent {
  return new DesignAgent(provider, model)
}

// Utility functions for working with design documents
export const DesignUtils = {
  /**
   * Validate design document completeness
   */
  validateDesignCompleteness(design: DesignDocument): {
    isComplete: boolean
    missingElements: string[]
    completenessScore: number
  } {
    const missingElements: string[] = []
    let score = 0

    if (!design.overview) missingElements.push('Project overview')
    else score += 10

    if (!design.architecture.pattern.name) missingElements.push('Architectural pattern')
    else score += 15

    if (design.architecture.components.length === 0) missingElements.push('System components')
    else score += 15

    if (!design.techStack.frontend.framework) missingElements.push('Frontend technology')
    else score += 10

    if (!design.techStack.backend.framework) missingElements.push('Backend technology')
    else score += 10

    if (design.dataModel.entities.length === 0) missingElements.push('Data model entities')
    else score += 15

    if (design.apiDesign.endpoints.length === 0) missingElements.push('API endpoints')
    else score += 15

    if (!design.security.authentication) missingElements.push('Security strategy')
    else score += 10

    return {
      isComplete: missingElements.length === 0,
      missingElements,
      completenessScore: score
    }
  },

  /**
   * Extract technology dependencies
   */
  extractTechnologyDependencies(design: DesignDocument): {
    frontend: string[]
    backend: string[]
    database: string[]
    infrastructure: string[]
  } {
    return {
      frontend: [
        design.techStack.frontend.framework,
        design.techStack.frontend.language,
        design.techStack.frontend.styling,
        design.techStack.frontend.buildTool,
        design.techStack.frontend.testing
      ].filter(Boolean),
      backend: [
        design.techStack.backend.framework,
        design.techStack.backend.language,
        design.techStack.backend.runtime,
        design.techStack.backend.authentication,
        design.techStack.backend.testing
      ].filter(Boolean),
      database: [
        design.techStack.backend.database,
        design.techStack.backend.orm
      ].filter(Boolean),
      infrastructure: [
        design.techStack.infrastructure.hosting,
        design.techStack.infrastructure.containerization,
        design.techStack.infrastructure.cicd,
        design.techStack.infrastructure.monitoring,
        design.techStack.infrastructure.caching
      ].filter(Boolean)
    }
  },

  /**
   * Generate component dependency graph
   */
  generateComponentDependencyGraph(components: Component[]): {
    nodes: Array<{ id: string; label: string; type: string }>
    edges: Array<{ from: string; to: string; label: string }>
  } {
    const nodes = components.map(comp => ({
      id: comp.name,
      label: comp.name,
      type: comp.type
    }))

    const edges: Array<{ from: string; to: string; label: string }> = []
    
    components.forEach(comp => {
      comp.dependencies.forEach(dep => {
        edges.push({
          from: comp.name,
          to: dep,
          label: 'depends on'
        })
      })
    })

    return { nodes, edges }
  }
}